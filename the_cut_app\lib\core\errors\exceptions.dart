/// Base exception
class AppException implements Exception {
  final String message;
  final int? code;

  AppException({required this.message, this.code});

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

/// Server exception
class ServerException extends AppException {
  ServerException({required super.message, super.code});
}

/// Cache exception
class CacheException extends AppException {
  CacheException({required super.message, super.code});
}

/// Network exception
class NetworkException extends AppException {
  NetworkException({required super.message, super.code});
}

/// Authentication exception
class AuthException extends AppException {
  AuthException({required super.message, super.code});
}

/// Permission exception
class PermissionException extends AppException {
  PermissionException({required super.message, super.code});
}

/// Validation exception
class ValidationException extends AppException {
  final Map<String, String>? errors;

  ValidationException({
    required super.message,
    super.code,
    this.errors,
  });
}

/// Not found exception
class NotFoundException extends AppException {
  NotFoundException({required super.message, int? code})
      : super(code: code ?? 404);
}

/// Timeout exception
class TimeoutException extends AppException {
  TimeoutException({super.message = 'Operation timed out', int? code})
      : super(code: code ?? 408);
}
