{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,13559", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,13635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4707,4815,4981,5106,5218,5356,5478,5589,5836,5984,6092,6256,6381,6524,6674,6735,6801", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "4810,4976,5101,5213,5351,5473,5584,5684,5979,6087,6251,6376,6519,6669,6730,6796,6878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "69,76,144,148,155,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6883,7475,12955,13251,13904,14331,14415", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "6950,7556,13031,13388,14068,14410,14492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2825,2911,2992,3046,3098,3164,3237,3317,3402,3482,3553,3629,3708,3777,3884,3980,4058,4153,4249,4323,4398,4497,4548,4630,4697,4784,4874,4936,5000,5063,5130,5232,5337,5434,5536,5594,5650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2820,2906,2987,3041,3093,3159,3232,3312,3397,3477,3548,3624,3703,3772,3879,3975,4053,4148,4244,4318,4393,4492,4543,4625,4692,4779,4869,4931,4995,5058,5125,5227,5332,5429,5531,5589,5645,5723"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,3335,4151,4250,4370,7412,7639,8102,8356,8415,8505,8568,8633,8697,8766,8828,8882,8997,9055,9116,9170,9243,9370,9456,9540,9673,9748,9824,9957,10043,10124,10178,10230,10296,10369,10449,10534,10614,10685,10761,10840,10909,11016,11112,11190,11285,11381,11455,11530,11629,11680,11762,11829,11916,12006,12068,12132,12195,12262,12364,12469,12566,12668,12726,13173", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "313,3070,3142,3224,3330,3428,4245,4365,4449,7470,7725,8164,8410,8500,8563,8628,8692,8761,8823,8877,8992,9050,9111,9165,9238,9365,9451,9535,9668,9743,9819,9952,10038,10119,10173,10225,10291,10364,10444,10529,10609,10680,10756,10835,10904,11011,11107,11185,11280,11376,11450,11525,11624,11675,11757,11824,11911,12001,12063,12127,12190,12257,12359,12464,12561,12663,12721,12777,13246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,72", "endOffsets": "258,331"}, "to": {"startLines": "82,161", "startColumns": "4,4", "startOffsets": "8041,14497", "endColumns": "60,76", "endOffsets": "8097,14569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,211", "endColumns": "78,76,77", "endOffsets": "129,206,284"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4628,7248,7561", "endColumns": "78,76,77", "endOffsets": "4702,7320,7634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3433,3527,3630,3727,3829,3931,4029,13715", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3522,3625,3722,3824,3926,4024,4146,13811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "13816", "endColumns": "87", "endOffsets": "13899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4454,4546,7056,7149,7325,8169,8255,12782,12869,13036,13104,13393,13476,13640,14073,14149,14215", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "4541,4623,7144,7243,7407,8250,8351,12864,12950,13099,13168,13471,13554,13710,14144,14210,14326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6955,7730,7831,7940", "endColumns": "100,100,108,100", "endOffsets": "7051,7826,7935,8036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5689", "endColumns": "146", "endOffsets": "5831"}}]}]}