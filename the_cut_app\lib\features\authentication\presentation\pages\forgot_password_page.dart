import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// Forgot password page
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _sendPasswordResetEmail() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
            SendPasswordResetEmailEvent(
              email: _emailController.text.trim(),
            ),
          );
    }
  }

  void _navigateBack() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Forgot Password',
      ),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthErrorState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is PasswordResetEmailSentState) {
            setState(() {
              _emailSent = true;
            });
          }
        },
        builder: (context, state) {
          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: _emailSent
                    ? _buildSuccessContent()
                    : _buildFormContent(state),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFormContent(AuthState state) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Icon
          Icon(
            Icons.lock_reset_outlined,
            size: 80.r,
            color: AppColors.primary,
          ),
          SizedBox(height: 24.h),
          
          // Title
          Text(
            'Forgot Password',
            style: AppTextStyles.h2,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            'Enter your email address and we\'ll send you a link to reset your password.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32.h),
          
          // Email field
          CustomTextField(
            label: 'Email',
            hint: 'Enter your email',
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: Validators.validateEmail,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _sendPasswordResetEmail(),
          ),
          SizedBox(height: 32.h),
          
          // Send button
          state is AuthLoadingState
              ? const Center(
                  child: LoadingIndicator(),
                )
              : CustomButton(
                  text: 'Send Reset Link',
                  onPressed: _sendPasswordResetEmail,
                  isFullWidth: true,
                  size: ButtonSize.large,
                ),
          SizedBox(height: 16.h),
          
          // Back to login
          CustomButton(
            text: 'Back to Login',
            onPressed: _navigateBack,
            isFullWidth: true,
            type: ButtonType.outlined,
            size: ButtonSize.large,
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Success icon
        Icon(
          Icons.check_circle_outline,
          size: 80.r,
          color: AppColors.success,
        ),
        SizedBox(height: 24.h),
        
        // Title
        Text(
          'Email Sent',
          style: AppTextStyles.h2,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'We\'ve sent a password reset link to ${_emailController.text}. Please check your email and follow the instructions to reset your password.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 32.h),
        
        // Back to login
        CustomButton(
          text: 'Back to Login',
          onPressed: _navigateBack,
          isFullWidth: true,
          size: ButtonSize.large,
        ),
      ],
    );
  }
}
