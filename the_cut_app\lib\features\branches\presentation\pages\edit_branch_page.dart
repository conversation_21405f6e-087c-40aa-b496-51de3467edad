import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/branch_entity.dart';
import '../bloc/branch_bloc.dart';
import '../bloc/branch_event.dart';
import '../bloc/branch_state.dart';

/// Edit branch page
class EditBranchPage extends StatefulWidget {
  final BranchEntity branch;

  const EditBranchPage({
    super.key,
    required this.branch,
  });

  @override
  State<EditBranchPage> createState() => _EditBranchPageState();
}

class _EditBranchPageState extends State<EditBranchPage> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _addressController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _managerNameController;
  late bool _isActive;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.branch.name);
    _addressController = TextEditingController(text: widget.branch.address);
    _phoneController = TextEditingController(text: widget.branch.phoneNumber);
    _emailController = TextEditingController(text: widget.branch.email);
    _managerNameController = TextEditingController(text: widget.branch.managerName);
    _isActive = widget.branch.isActive;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _managerNameController.dispose();
    super.dispose();
  }

  void _updateBranch() {
    if (_formKey.currentState?.validate() ?? false) {
      final updatedBranch = widget.branch.copyWith(
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        managerName: _managerNameController.text.trim(),
        isActive: _isActive,
      );

      context.read<BranchBloc>().add(
            UpdateBranchEvent(branch: updatedBranch),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Edit Branch',
      ),
      body: BlocConsumer<BranchBloc, BranchState>(
        listener: (context, state) {
          if (state is BranchErrorState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is BranchUpdatedState) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Branch updated successfully'),
                backgroundColor: AppColors.success,
              ),
            );
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          return SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Text(
                      'Branch Information',
                      style: AppTextStyles.h4,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Update the branch details',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(height: 24.h),
                    
                    // Branch name
                    CustomTextField(
                      label: 'Branch Name',
                      hint: 'Enter branch name',
                      controller: _nameController,
                      prefixIcon: Icons.business_outlined,
                      validator: (value) => Validators.validateRequired(
                        value,
                        'Branch name',
                      ),
                      textInputAction: TextInputAction.next,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Branch address
                    CustomTextField(
                      label: 'Address',
                      hint: 'Enter branch address',
                      controller: _addressController,
                      prefixIcon: Icons.location_on_outlined,
                      textInputAction: TextInputAction.next,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Branch phone
                    CustomTextField(
                      label: 'Phone Number',
                      hint: 'Enter branch phone number',
                      controller: _phoneController,
                      prefixIcon: Icons.phone_outlined,
                      keyboardType: TextInputType.phone,
                      validator: (value) => value?.isEmpty == true
                          ? null
                          : Validators.validatePhone(value),
                      textInputAction: TextInputAction.next,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Branch email
                    CustomTextField(
                      label: 'Email',
                      hint: 'Enter branch email',
                      controller: _emailController,
                      prefixIcon: Icons.email_outlined,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) => value?.isEmpty == true
                          ? null
                          : Validators.validateEmail(value),
                      textInputAction: TextInputAction.next,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Branch manager
                    CustomTextField(
                      label: 'Manager Name',
                      hint: 'Enter branch manager name',
                      controller: _managerNameController,
                      prefixIcon: Icons.person_outlined,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _updateBranch(),
                    ),
                    SizedBox(height: 24.h),
                    
                    // Branch status
                    Row(
                      children: [
                        Text(
                          'Branch Status',
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'Active',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: _isActive
                                ? AppColors.primary
                                : AppColors.textSecondary,
                          ),
                        ),
                        Switch(
                          value: _isActive,
                          onChanged: (value) {
                            setState(() {
                              _isActive = value;
                            });
                          },
                          activeColor: AppColors.primary,
                        ),
                      ],
                    ),
                    SizedBox(height: 32.h),
                    
                    // Submit button
                    state is BranchLoadingState
                        ? const Center(
                            child: LoadingIndicator(),
                          )
                        : CustomButton(
                            text: 'Update Branch',
                            onPressed: _updateBranch,
                            isFullWidth: true,
                            size: ButtonSize.large,
                          ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
