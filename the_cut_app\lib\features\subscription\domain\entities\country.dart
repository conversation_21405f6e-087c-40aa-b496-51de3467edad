import 'package:equatable/equatable.dart';

/// Country entity
class Country extends Equatable {
  /// Country code (ISO 3166-1 alpha-2)
  final String code;

  /// Country name
  final String name;

  /// Country name in Arabic
  final String nameAr;

  /// Country flag emoji
  final String flagEmoji;

  /// Currency code
  final String currencyCode;

  /// Currency symbol
  final String currencySymbol;

  /// Currency name
  final String currencyName;

  /// Currency name in Arabic
  final String currencyNameAr;

  /// Exchange rate to USD (1 USD = X local currency)
  final double exchangeRate;

  /// Phone code (e.g., +966 for Saudi Arabia)
  final String phoneCode;

  /// Whether the country is supported for payments
  final bool isPaymentSupported;

  /// Payment methods supported in this country
  final List<String> supportedPaymentMethods;

  /// Constructor
  const Country({
    required this.code,
    required this.name,
    required this.nameAr,
    required this.flagEmoji,
    required this.currencyCode,
    required this.currencySymbol,
    required this.currencyName,
    required this.currencyNameAr,
    required this.exchangeRate,
    required this.phoneCode,
    this.isPaymentSupported = true,
    required this.supportedPaymentMethods,
  });

  @override
  List<Object?> get props => [
        code,
        name,
        nameAr,
        flagEmoji,
        currencyCode,
        currencySymbol,
        currencyName,
        currencyNameAr,
        exchangeRate,
        phoneCode,
        isPaymentSupported,
        supportedPaymentMethods,
      ];
}
