import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/font_service.dart';
import '../../domain/entities/subscription_plan.dart';

/// Widget to display a subscription plan card
class SubscriptionPlanCard extends StatelessWidget {
  /// Subscription plan
  final SubscriptionPlan plan;

  /// Callback when the user subscribes to the plan
  final VoidCallback onSubscribe;

  /// Currency symbol
  final String currencySymbol;

  /// Exchange rate
  final double exchangeRate;

  /// Constructor
  const SubscriptionPlanCard({
    super.key,
    required this.plan,
    required this.onSubscribe,
    required this.currencySymbol,
    required this.exchangeRate,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final fontService = FontService.of(context);
    final localizedPrice = (plan.price * exchangeRate).toStringAsFixed(2);

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: plan.isPopular
            ? BorderSide(color: AppColors.primary, width: 2.w)
            : BorderSide.none,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Popular badge
          if (plan.isPopular)
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Text(
                localizations.translate('most_popular'),
                textAlign: TextAlign.center,
                style: GoogleFonts.getFont(
                  fontService.fontFamily,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),

          // Plan content
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plan name
                Text(
                  plan.name,
                  style: GoogleFonts.getFont(
                    fontService.fontFamily,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 8.h),

                // Plan description
                Text(
                  plan.description,
                  style: GoogleFonts.getFont(
                    fontService.fontFamily,
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 16.h),

                // Price
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '$currencySymbol$localizedPrice',
                      style: GoogleFonts.getFont(
                        fontService.fontFamily,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '/${localizations.translate('month')}',
                      style: GoogleFonts.getFont(
                        fontService.fontFamily,
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),

                // Original price if there's a discount
                if (plan.discountPercentage > 0)
                  Row(
                    children: [
                      Text(
                        '$currencySymbol${(plan.originalPrice * exchangeRate).toStringAsFixed(2)}',
                        style: GoogleFonts.getFont(
                          fontService.fontFamily,
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success.withAlpha(30),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          '${plan.discountPercentage.toStringAsFixed(0)}% ${localizations.translate('off')}',
                          style: GoogleFonts.getFont(
                            fontService.fontFamily,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      ),
                    ],
                  ),
                SizedBox(height: 16.h),

                // Features
                ...plan.features.map((feature) => _buildFeatureItem(context, feature, fontService)),
                SizedBox(height: 16.h),

                // Subscribe button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onSubscribe,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      localizations.translate('subscribe_now'),
                      style: GoogleFonts.getFont(
                        fontService.fontFamily,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, String feature, FontService fontService) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 18.w,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              feature,
              style: GoogleFonts.getFont(
                fontService.fontFamily,
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
