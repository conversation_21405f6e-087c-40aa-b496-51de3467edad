import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'core/constants/app_colors.dart';
import 'core/constants/app_constants.dart';
import 'core/constants/app_text_styles.dart';
import 'core/localization/app_localizations.dart';
import 'core/services/auth_service.dart';
import 'core/services/real_estate_service.dart';
import 'core/services/supervision_service.dart';
import 'core/services/language_service.dart';
import 'core/services/font_service.dart';
import 'features/authentication/presentation/screens/splash_screen.dart';
import 'features/authentication/presentation/screens/real_estate_login_screen.dart';
import 'features/authentication/presentation/screens/register_screen.dart';
import 'features/authentication/presentation/screens/forgot_password_screen.dart';
import 'features/real_estate_projects/presentation/screens/property_type_selection_screen.dart';
import 'features/real_estate_projects/presentation/screens/construction_phases_screen.dart';
import 'features/real_estate_projects/presentation/screens/phase_sections_screen.dart';
import 'features/real_estate_projects/presentation/screens/section_content_screen.dart';
import 'features/real_estate_projects/presentation/screens/project_details_screen.dart';
import 'features/supervision/presentation/screens/supervision_login_screen.dart';
import 'features/supervision/presentation/screens/supervision_register_screen.dart';
import 'features/supervision/presentation/screens/supervision_forgot_password_screen.dart';
import 'features/supervision/presentation/screens/supervision_dashboard_screen.dart';
import 'features/settings/presentation/screens/settings_screen.dart';
import 'features/real_estate_projects/domain/entities/project_entity.dart';
import 'features/real_estate_projects/domain/entities/phase_entity.dart';

// TODO: Uncomment when Firebase is configured
// import 'package:firebase_core/firebase_core.dart';
// import 'firebase/firebase_options.dart';
// import 'injection_container.dart' as di;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // TODO: Initialize Firebase
  // await Firebase.initializeApp(
  //   options: DefaultFirebaseOptions.currentPlatform,
  // );

  // TODO: Initialize dependency injection
  // await di.init();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: AppColors.background,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late LanguageService _languageService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  late FontService _fontService;

  Future<void> _initializeApp() async {
    // Initialize language service
    _languageService = LanguageService();
    await _languageService.init();

    // Initialize font service
    _fontService = FontService();
    await _fontService.init();

    // Set font based on language
    await _fontService
        .setFontForLanguage(_languageService.currentLocale.languageCode);

    setState(() {
      _isInitialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProvider(create: (_) => RealEstateService()),
        ChangeNotifierProvider(create: (_) => SupervisionService()),
        ChangeNotifierProvider.value(value: _languageService),
        ChangeNotifierProvider.value(value: _fontService),
      ],
      child: Consumer2<LanguageService, FontService>(
        builder: (context, languageService, fontService, _) {
          return ScreenUtilInit(
            designSize: const Size(375, 812),
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (context, child) {
              // Determine font family based on language
              final String fontFamily =
                  languageService.currentLocale.languageCode == 'ar'
                      ? FontService.defaultArabicFont
                      : FontService.defaultEnglishFont;

              return MaterialApp(
                title: AppConstants.appName,
                debugShowCheckedModeBanner: false,
                // Localization support
                locale: languageService.currentLocale,
                supportedLocales: const [
                  Locale('en', ''),
                  Locale('ar', ''),
                ],
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                // RTL support
                builder: (context, child) {
                  return Directionality(
                    textDirection: languageService.isRTL
                        ? TextDirection.rtl
                        : TextDirection.ltr,
                    child: child!,
                  );
                },
                theme: ThemeData(
                  primaryColor: AppColors.primary,
                  scaffoldBackgroundColor: AppColors.background,
                  fontFamily: fontFamily, // Apply font family to all text
                  colorScheme: ColorScheme.fromSwatch().copyWith(
                    primary: AppColors.primary,
                    secondary: AppColors.accent,
                  ),
                  appBarTheme: AppBarTheme(
                    backgroundColor: AppColors.background,
                    elevation: 0,
                    centerTitle: true,
                    iconTheme: const IconThemeData(
                      color: AppColors.textPrimary,
                    ),
                    titleTextStyle: GoogleFonts.getFont(
                      fontFamily,
                      color: AppColors.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  elevatedButtonTheme: ElevatedButtonThemeData(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      textStyle: GoogleFonts.getFont(
                        fontFamily,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  textButtonTheme: TextButtonThemeData(
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      textStyle: GoogleFonts.getFont(
                        fontFamily,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  outlinedButtonTheme: OutlinedButtonThemeData(
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(
                        color: AppColors.primary,
                        width: 1,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      textStyle: GoogleFonts.getFont(
                        fontFamily,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  inputDecorationTheme: InputDecorationTheme(
                    filled: true,
                    fillColor: AppColors.background,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.border,
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.border,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.primary,
                        width: 1,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.error,
                        width: 1,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.error,
                        width: 1,
                      ),
                    ),
                    // Apply font to input fields
                    labelStyle: GoogleFonts.getFont(
                      fontFamily,
                      color: AppColors.textSecondary,
                    ),
                    hintStyle: GoogleFonts.getFont(
                      fontFamily,
                      color: AppColors.textSecondary,
                    ),
                    errorStyle: GoogleFonts.getFont(
                      fontFamily,
                      color: AppColors.error,
                      fontSize: 12,
                    ),
                  ),
                ),
                initialRoute: '/',
                onGenerateRoute: (settings) {
                  switch (settings.name) {
                    case '/':
                      return MaterialPageRoute(
                        builder: (context) => const SplashScreen(),
                      );
                    case '/login':
                      return MaterialPageRoute(
                        builder: (context) => const RealEstateLoginScreen(),
                      );
                    case '/register':
                      return MaterialPageRoute(
                        builder: (context) => const RegisterScreen(),
                      );
                    case '/forgot-password':
                      return MaterialPageRoute(
                        builder: (context) => const ForgotPasswordScreen(),
                      );
                    case '/property-type-selection':
                      return MaterialPageRoute(
                        builder: (context) => const PropertyTypeSelectionScreen(),
                      );
                    case '/construction-phases':
                      final propertyType = settings.arguments as PropertyType;
                      return MaterialPageRoute(
                        builder: (context) => ConstructionPhasesScreen(propertyType: propertyType),
                      );
                    case '/phase-sections':
                      final args = settings.arguments as Map<String, dynamic>;
                      return MaterialPageRoute(
                        builder: (context) => PhaseSectionsScreen(
                          propertyType: args['propertyType'],
                          phase: args['phase'],
                        ),
                      );
                    case '/section-content':
                      final args = settings.arguments as Map<String, dynamic>;
                      return MaterialPageRoute(
                        builder: (context) => SectionContentScreen(
                          propertyType: args['propertyType'],
                          phase: args['phase'],
                          section: args['section'],
                        ),
                      );
                    case '/supervision-login':
                      return MaterialPageRoute(
                        builder: (context) => const SupervisionLoginScreen(),
                      );
                    case '/supervision-register':
                      return MaterialPageRoute(
                        builder: (context) => const SupervisionRegisterScreen(),
                      );
                    case '/supervision-forgot-password':
                      return MaterialPageRoute(
                        builder: (context) => const SupervisionForgotPasswordScreen(),
                      );
                    case '/supervision-dashboard':
                      return MaterialPageRoute(
                        builder: (context) => const SupervisionDashboardScreen(),
                      );
                    case '/project-details':
                      final project = settings.arguments as ProjectEntity;
                      return MaterialPageRoute(
                        builder: (context) => ProjectDetailsScreen(project: project),
                      );
                    case '/settings':
                      return MaterialPageRoute(
                        builder: (context) => const SettingsScreen(),
                      );
                    default:
                      return MaterialPageRoute(
                        builder: (context) => const SplashScreen(),
                      );
                  }
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Home screen
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    final user = authService.currentUser;
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('app_name')),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_outline),
            onPressed: () {
              Navigator.pushNamed(context, '/profile');
            },
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(
                color: AppColors.primary,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30.r,
                    backgroundColor: Colors.white,
                    child: Text(
                      user?.name?.isNotEmpty == true
                          ? user!.name!.substring(0, 1).toUpperCase()
                          : 'U',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    user?.name ?? 'User',
                    style: AppTextStyles.h4.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    user?.email ?? '',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white.withAlpha(204),
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.home_outlined),
              title: Text(localizations.translate('home')),
              selected: true,
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.store_outlined),
              title: Text(localizations.translate('branches')),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/branches');
              },
            ),
            ListTile(
              leading: const Icon(Icons.people_outline),
              title: Text(localizations.translate('team')),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/team');
              },
            ),
            ListTile(
              leading: const Icon(Icons.task_outlined),
              title: Text(localizations.translate('tasks')),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/tasks');
              },
            ),
            ListTile(
              leading: const Icon(Icons.attach_money_outlined),
              title: Text(localizations.translate('finance')),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/finance');
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings_outlined),
              title: Text(localizations.translate('settings')),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/settings');
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout_outlined),
              title: Text(localizations.translate('logout')),
              onTap: () async {
                Navigator.pop(context);
                await authService.signOut();
                if (context.mounted) {
                  Navigator.pushReplacementNamed(context, '/login');
                }
              },
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${localizations.translate('welcome')}, ${user?.name?.split(' ').first ?? localizations.translate('user')}!',
                      style: AppTextStyles.h3,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      localizations
                          .translate('manage_your_business_efficiently'),
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24.h),

            // Quick actions section
            Text(
              localizations.translate('quick_actions'),
              style: AppTextStyles.h4,
            ),
            SizedBox(height: 16.h),

            // Quick action cards
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    icon: Icons.store_outlined,
                    title: localizations.translate('branches'),
                    onTap: () {
                      Navigator.pushNamed(context, '/branches');
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    icon: Icons.people_outline,
                    title: localizations.translate('team'),
                    onTap: () {
                      Navigator.pushNamed(context, '/team');
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    icon: Icons.task_outlined,
                    title: localizations.translate('tasks'),
                    onTap: () {
                      Navigator.pushNamed(context, '/tasks');
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    icon: Icons.attach_money_outlined,
                    title: localizations.translate('finance'),
                    onTap: () {
                      Navigator.pushNamed(context, '/finance');
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),

            // Recent activity section
            Text(
              localizations.translate('recent_activities'),
              style: AppTextStyles.h4,
            ),
            SizedBox(height: 16.h),

            // Recent activity list
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    _buildActivityItem(
                      icon: Icons.store_outlined,
                      title: localizations.translate('branch_created'),
                      description:
                          localizations.translate('main_branch_was_created'),
                      time: localizations.translate('two_hours_ago'),
                    ),
                    SizedBox(height: 16.h),
                    _buildActivityItem(
                      icon: Icons.person_outlined,
                      title: localizations.translate('profile_updated'),
                      description:
                          localizations.translate('you_updated_your_profile'),
                      time: localizations.translate('one_day_ago'),
                    ),
                    SizedBox(height: 16.h),
                    _buildActivityItem(
                      icon: Icons.login_outlined,
                      title: localizations.translate('logged_in'),
                      description: localizations.translate('you_logged_in'),
                      time: localizations.translate('one_day_ago'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40.w,
                color: AppColors.primary,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String description,
    required String time,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(25),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            icon,
            size: 20.w,
            color: AppColors.primary,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: AppTextStyles.caption,
        ),
      ],
    );
  }
}
