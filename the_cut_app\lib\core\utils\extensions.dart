import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// String extensions
extension StringExtension on String {
  /// Capitalize the first letter of the string
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  /// Capitalize the first letter of each word in the string
  String get capitalizeWords {
    if (isEmpty) return this;
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  /// Check if the string is a valid email
  bool get isValidEmail {
    final emailRegExp = RegExp(
      r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+',
    );
    return emailRegExp.hasMatch(this);
  }

  /// Check if the string is a valid password
  bool get isValidPassword {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    final passwordRegExp = RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$',
    );
    return passwordRegExp.hasMatch(this);
  }

  /// Check if the string is a valid phone number
  bool get isValidPhone {
    final phoneRegExp = RegExp(r'^\+?[0-9]{10,15}$');
    return phoneRegExp.hasMatch(this);
  }

  /// Remove all whitespace from the string
  String get removeWhitespace {
    return replaceAll(RegExp(r'\s+'), '');
  }
}

/// DateTime extensions
extension DateTimeExtension on DateTime {
  /// Format the date as yyyy-MM-dd
  String get toYMD {
    return DateFormat('yyyy-MM-dd').format(this);
  }

  /// Format the date as dd/MM/yyyy
  String get toDMY {
    return DateFormat('dd/MM/yyyy').format(this);
  }

  /// Format the date as yyyy-MM-dd HH:mm
  String get toYMDHM {
    return DateFormat('yyyy-MM-dd HH:mm').format(this);
  }

  /// Format the date as dd/MM/yyyy HH:mm
  String get toDMYHM {
    return DateFormat('dd/MM/yyyy HH:mm').format(this);
  }

  /// Format the date as HH:mm
  String get toHM {
    return DateFormat('HH:mm').format(this);
  }

  /// Format the date as a relative time (e.g. "2 hours ago")
  String get toRelative {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} years ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
}

/// BuildContext extensions
extension BuildContextExtension on BuildContext {
  /// Get the screen size
  Size get screenSize => MediaQuery.of(this).size;

  /// Get the screen width
  double get screenWidth => MediaQuery.of(this).size.width;

  /// Get the screen height
  double get screenHeight => MediaQuery.of(this).size.height;

  /// Get the theme
  ThemeData get theme => Theme.of(this);

  /// Get the text theme
  TextTheme get textTheme => Theme.of(this).textTheme;

  /// Get the color scheme
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// Show a snackbar
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }
}
