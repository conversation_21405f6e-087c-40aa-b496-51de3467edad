import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'font_service.dart';

/// Language service for managing app language and direction
class LanguageService extends ChangeNotifier {
  Locale _currentLocale = const Locale('en');
  bool _isRTL = false;

  /// Get current locale
  Locale get currentLocale => _currentLocale;

  /// Check if the app is in RTL mode
  bool get isRTL => _isRTL;

  /// Initialize language service
  Future<void> init() async {
    await loadSavedLanguage();
  }

  /// Load saved language from shared preferences
  Future<void> loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('languageCode') ?? 'en';

    await setLanguage(Locale(languageCode));
  }

  /// Set app language
  Future<void> setLanguage(Locale locale, {FontService? fontService}) async {
    _currentLocale = locale;
    _isRTL = locale.languageCode == 'ar';

    // Save to shared preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', locale.languageCode);

    // Update font based on language
    if (fontService != null) {
      await fontService.setFontForLanguage(locale.languageCode);
    }

    notifyListeners();
  }

  /// Toggle between English and Arabic
  Future<void> toggleLanguage({FontService? fontService}) async {
    if (_currentLocale.languageCode == 'en') {
      await setLanguage(const Locale('ar'), fontService: fontService);
    } else {
      await setLanguage(const Locale('en'), fontService: fontService);
    }
  }

  /// Get language name based on locale
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'English';
    }
  }
}
