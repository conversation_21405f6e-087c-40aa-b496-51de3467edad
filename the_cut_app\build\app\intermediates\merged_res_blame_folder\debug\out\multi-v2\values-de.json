{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4793,4902,5066,5194,5306,5484,5615,5736,6000,6180,6292,6461,6592,6754,6930,7001,7064", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "4897,5061,5189,5301,5479,5610,5731,5850,6175,6287,6456,6587,6749,6925,6996,7059,7139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2946,3036,3115,3172,3223,3289,3363,3445,3538,3613,3687,3765,3837,3911,4021,4113,4195,4284,4373,4447,4525,4611,4666,4745,4812,4892,4976,5038,5102,5165,5234,5341,5448,5547,5653,5714,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2941,3031,3110,3167,3218,3284,3358,3440,3533,3608,3682,3760,3832,3906,4016,4108,4190,4279,4368,4442,4520,4606,4661,4740,4807,4887,4971,5033,5097,5160,5229,5336,5443,5542,5648,5709,5764,5846"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,7681,7912,8379,8626,8685,8793,8859,8928,8986,9058,9122,9176,9304,9364,9426,9480,9558,9695,9787,9871,10016,10100,10186,10319,10409,10488,10545,10596,10662,10736,10818,10911,10986,11060,11138,11210,11284,11394,11486,11568,11657,11746,11820,11898,11984,12039,12118,12185,12265,12349,12411,12475,12538,12607,12714,12821,12920,13026,13087,13531", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,7742,8001,8444,8680,8788,8854,8923,8981,9053,9117,9171,9299,9359,9421,9475,9553,9690,9782,9866,10011,10095,10181,10314,10404,10483,10540,10591,10657,10731,10813,10906,10981,11055,11133,11205,11279,11389,11481,11563,11652,11741,11815,11893,11979,12034,12113,12180,12260,12344,12406,12470,12533,12602,12709,12816,12915,13021,13082,13137,13608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7216,8006,8107,8218", "endColumns": "103,100,110,99", "endOffsets": "7315,8102,8213,8313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,13918", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,13995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,468,515,582,683,752,1042,1105,1203,1271,1326,1390,1712,1788,1852,1905,1958,2049,2180,2296,2353,2442,2523,2609,2676,2782,3138,3218,3295,3358,3418,3481,3541,3637,3720,3826,3926,4000,4079,4170,4409,4657,4774,4833,5639,5704", "endColumns": "161,250,46,66,100,68,289,62,97,67,54,63,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,95,82,105,99,73,78,90,238,247,116,58,805,64,54", "endOffsets": "212,463,510,577,678,747,1037,1100,1198,1266,1321,1385,1707,1783,1847,1900,1953,2044,2175,2291,2348,2437,2518,2604,2671,2777,3133,3213,3290,3353,3413,3476,3536,3632,3715,3821,3921,3995,4074,4165,4404,4652,4769,4828,5634,5699,5754"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20654,20816,21922,21969,22036,22137,22206,22496,22559,24430,24698,24899,25347,25980,26056,26528,27560,27859,27950,28081,28197,28254,28849,29103,29547,29693,29799,30155,30235,30373,30582,30642,30882,31608,32775,34298,34404,34504,34578,34657,35774,36413,36661,36778,36906,37712,39467", "endColumns": "161,250,46,66,100,68,289,62,97,67,54,63,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,95,82,105,99,73,78,90,238,247,116,58,805,64,54", "endOffsets": "20811,21062,21964,22031,22132,22201,22491,22554,22652,24493,24748,24958,25664,26051,26115,26576,27608,27945,28076,28192,28249,28338,28925,29184,29609,29794,30150,30230,30307,30431,30637,30700,30937,31699,32853,34399,34499,34573,34652,34743,36008,36656,36773,36832,37707,37772,39517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,245,391,503,602,748,940,1148,1321,1414,1520,1617,1713,1909,2015,2111,2369,2485,2582,2646,2713,2804,2900,3003,3247,3319,3396,3485,3570,3659,3731,3800,3925,4018,4112,4188,4259,4322,4427,4545,4653,4783,4879,4951,5037,5121,5194,5311,5448", "endColumns": "89,99,145,111,98,145,191,207,172,92,105,96,95,195,105,95,257,115,96,63,66,90,95,102,243,71,76,88,84,88,71,68,124,92,93,75,70,62,104,117,107,129,95,71,85,83,72,116,136,90", "endOffsets": "140,240,386,498,597,743,935,1143,1316,1409,1515,1612,1708,1904,2010,2106,2364,2480,2577,2641,2708,2799,2895,2998,3242,3314,3391,3480,3565,3654,3726,3795,3920,4013,4107,4183,4254,4317,4422,4540,4648,4778,4874,4946,5032,5116,5189,5306,5443,5534"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15283,22657,24753,25769,25881,30436,30942,31134,31342,31515,31704,31810,31907,32003,32199,32305,32401,32659,32858,32955,33019,33086,33177,33273,33376,33620,33692,33769,33858,33943,34032,34104,34173,34748,34841,34935,35011,35082,35145,35250,35368,35476,35606,35702,38939,39522,39606,39679,39796,39933", "endColumns": "89,99,145,111,98,145,191,207,172,92,105,96,95,195,105,95,257,115,96,63,66,90,95,102,243,71,76,88,84,88,71,68,124,92,93,75,70,62,104,117,107,129,95,71,85,83,72,116,136,90", "endOffsets": "15368,22752,24894,25876,25975,30577,31129,31337,31510,31603,31805,31902,31998,32194,32300,32396,32654,32770,32950,33014,33081,33172,33268,33371,33615,33687,33764,33853,33938,34027,34099,34168,34293,34836,34930,35006,35077,35140,35245,35363,35471,35601,35697,35769,39020,39601,39674,39791,39928,40019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15832,16637,16773,16835,16919,17216,17755,17947,18193,18523,18885,19185", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "15904,16690,16830,16914,16983,17289,17809,18018,18262,18593,18946,19251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8318,42833", "endColumns": "60,77", "endOffsets": "8374,42906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3341,3406,4111,4804,4878,5008,5114,5169,5277,5377,5445,5539,5639,5696,5784,5836,5916,6019,6096,6168,6219,6300,6395,6442,6493,6568,6626,6687,6907,7107,7266,7331,7420,7510,7606,7692,7779,7856,7945,8024,8111,8201,8257,8402,8454,8509,8578,8647,8722,8786,8858,8947,9020", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,50,80,94,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,86,89,55,144,51,54,68,68,74,63,71,88,72,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3336,3401,4106,4799,4873,5003,5109,5164,5272,5372,5440,5534,5634,5691,5779,5831,5911,6014,6091,6163,6214,6295,6390,6437,6488,6563,6621,6682,6902,7102,7261,7326,7415,7505,7601,7687,7774,7851,7940,8019,8106,8196,8252,8397,8449,8504,8573,8642,8717,8781,8853,8942,9015,9091"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14724,14794,14873,14942,15011,15089,15151,15454,15510,15597,15679,15909,16083,16180,16333,16695,16988,18023,18267,18361,18432,18598,18683,18772,18951,19256,19347,19425,19523,19612,19716,19805,19896,19990,20115,20277,20552,21067,21152,21217,22757,23450,23524,23654,23760,23815,23923,24023,24091,24185,24285,24342,24963,25015,25095,25198,25275,25718,26120,26201,26296,26343,26643,26718,26776,26837,27057,27257,27613,27678,28663,28753,28930,29016,29381,29458,29614,30705,30792,36105,36161,36306,36358,36837,39025,39094,39169,39233,39305,39394,40787", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,50,80,94,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,86,89,55,144,51,54,68,68,74,63,71,88,72,75", "endOffsets": "14789,14868,14937,15006,15084,15146,15205,15505,15592,15674,15762,16001,16175,16261,16417,16768,17072,18114,18356,18427,18518,18678,18767,18880,19032,19342,19420,19518,19607,19711,19800,19891,19985,20110,20195,20367,20649,21147,21212,21917,23445,23519,23649,23755,23810,23918,24018,24086,24180,24280,24337,24425,25010,25090,25193,25270,25342,25764,26196,26291,26338,26389,26713,26771,26832,27052,27252,27411,27673,27762,28748,28844,29011,29098,29453,29542,29688,30787,30877,36156,36301,36353,36408,36901,39089,39164,39228,39300,39389,39462,40858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,14075", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,14171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4627,7320,7418,7594,8449,8534,13142,13231,13402,13467,13753,13834,14000,42412,42491,42557", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4622,4710,7413,7513,7676,8529,8621,13226,13314,13462,13526,13829,13913,14070,42486,42552,42672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4715,7518,7835", "endColumns": "77,75,76", "endOffsets": "4788,7589,7907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5855", "endColumns": "144", "endOffsets": "5995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15767,16006,16266,16422,16507,16576,17077,17149,17294,17358,17426,17496,17556,17618,17691,17814,17884,18119,19037,19100,20200,20372,20459,24498,24610,25669,26394,26442,26581,27416,27491,28343,28442,29189,29287", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "15827,16078,16328,16502,16571,16632,17144,17211,17353,17421,17491,17551,17613,17686,17750,17879,17942,18188,19095,19180,20272,20454,20547,24605,24693,25713,26437,26523,26638,27486,27555,28437,28525,29282,29376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,209,342,403,495,554,619,705,814,1004,1322,1657,1732,1810,1893,1985,2126,2278,2346,2420,2604,2672,2731,2805,2893,3038,3177,3301,3393,3473,3559,3633", "endColumns": "72,80,132,60,91,58,64,85,108,189,317,334,74,77,82,91,140,151,67,73,183,67,58,73,87,144,138,123,91,79,85,73,166", "endOffsets": "123,204,337,398,490,549,614,700,809,999,1317,1652,1727,1805,1888,1980,2121,2273,2341,2415,2599,2667,2726,2800,2888,3033,3172,3296,3388,3468,3554,3628,3795"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15210,15373,28530,30312,36013,37777,37836,37901,37987,38096,38286,38604,40024,40099,40177,40260,40352,40493,40645,40713,40863,41047,41115,41174,41248,41336,41481,41620,41744,41836,41916,42002,42076", "endColumns": "72,80,132,60,91,58,64,85,108,189,317,334,74,77,82,91,140,151,67,73,183,67,58,73,87,144,138,123,91,79,85,73,166", "endOffsets": "15278,15449,28658,30368,36100,37831,37896,37982,38091,38281,38599,38934,40094,40172,40255,40347,40488,40640,40708,40782,41042,41110,41169,41243,41331,41476,41615,41739,41831,41911,41997,42071,42238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "27767", "endColumns": "91", "endOffsets": "27854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14176,14266,14368,14436,14503,14570,14650", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "14261,14363,14431,14498,14565,14645,14719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7144,7747,13319,13613,42243,42677,42757", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "7211,7830,13397,13748,42407,42752,42828"}}]}]}