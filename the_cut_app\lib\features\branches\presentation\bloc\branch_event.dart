import 'package:equatable/equatable.dart';
import '../../domain/entities/branch_entity.dart';

/// Branch events
abstract class BranchEvent extends Equatable {
  const BranchEvent();

  @override
  List<Object?> get props => [];
}

/// Get all branches event
class GetAllBranchesEvent extends BranchEvent {
  const GetAllBranchesEvent();
}

/// Get branch by id event
class GetBranchByIdEvent extends BranchEvent {
  final String branchId;

  const GetBranchByIdEvent({
    required this.branchId,
  });

  @override
  List<Object?> get props => [branchId];
}

/// Create branch event
class CreateBranchEvent extends BranchEvent {
  final String name;
  final String? address;
  final String? phoneNumber;
  final String? email;
  final String? managerId;
  final String? managerName;
  final String? logoUrl;

  const CreateBranchEvent({
    required this.name,
    this.address,
    this.phoneNumber,
    this.email,
    this.managerId,
    this.managerName,
    this.logoUrl,
  });

  @override
  List<Object?> get props => [
        name,
        address,
        phoneNumber,
        email,
        managerId,
        managerName,
        logoUrl,
      ];
}

/// Update branch event
class UpdateBranchEvent extends BranchEvent {
  final BranchEntity branch;

  const UpdateBranchEvent({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}

/// Delete branch event
class DeleteBranchEvent extends BranchEvent {
  final String branchId;

  const DeleteBranchEvent({
    required this.branchId,
  });

  @override
  List<Object?> get props => [branchId];
}

/// Get active branches event
class GetActiveBranchesEvent extends BranchEvent {
  const GetActiveBranchesEvent();
}
