<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.thecut.the_cut_app"
    android:versionCode="1"
    android:versionName="1.0.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />
    <!--
         The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!--
     Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
    -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />

            <data android:mimeType="text/plain" />
        </intent>
        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />

            <data android:mimeType="*/*" />
        </intent> <!-- Added to check the default browser that will host the AuthFlow. -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
        </intent>
        <intent>
            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="com.android.vending.BILLING" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="android.app.Application"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="the_cut_app" >
        <activity
            android:name="com.thecut.the_cut_app.MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize" >

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!--
             Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <provider
            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
            android:authorities="com.thecut.the_cut_app.flutter.image_provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_image_picker_file_paths" />
        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>

            <meta-data
                android:name="photopicker_activity:0:required"
                android:value="" />
        </service>

        <meta-data
            android:name="com.google.android.gms.wallet.api.enabled"
            android:value="true" />

        <activity
            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
                <data
                    android:host="link-accounts"
                    android:pathPrefix="/com.thecut.the_cut_app/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
                <data
                    android:host="link-native-accounts"
                    android:pathPrefix="/com.thecut.the_cut_app/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
                <data
                    android:host="link-accounts"
                    android:path="/com.thecut.the_cut_app/success"
                    android:scheme="stripe-auth" />
                <data
                    android:host="link-accounts"
                    android:path="/com.thecut.the_cut_app/cancel"
                    android:scheme="stripe-auth" />

                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
                <data
                    android:host="native-redirect"
                    android:pathPrefix="/com.thecut.the_cut_app"
                    android:scheme="stripe-auth" />

                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
                <data
                    android:host="auth-redirect"
                    android:pathPrefix="/com.thecut.the_cut_app"
                    android:scheme="stripe" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
        <activity
            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
            android:windowSoftInputMode="adjustResize" />

        <meta-data
            android:name="com.google.android.play.billingclient.version"
            android:value="7.1.1" />

        <activity
            android:name="com.android.billingclient.api.ProxyBillingActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
            android:theme="@style/StripePaymentSheetDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.link.LinkForegroundActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTop"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="complete"
                    android:path="/com.thecut.the_cut_app"
                    android:scheme="link-popup" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.AddPaymentMethodActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentMethodsActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentFlowActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.view.PaymentRelayActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" />
        <!--
        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
        launched the browser Activity will also handle the return URL deep link.
        -->
        <activity
            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Must match `DefaultReturnUrl#value`. -->
                <data
                    android:host="payment_return_url"
                    android:path="/com.thecut.the_cut_app"
                    android:scheme="stripesdk" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" /> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <activity
            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
            android:exported="false"
            android:theme="@style/Stripe3DS2Theme" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.thecut.the_cut_app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <meta-data
            android:name="aia-compat-api-min-version"
            android:value="1" />
    </application>

</manifest>