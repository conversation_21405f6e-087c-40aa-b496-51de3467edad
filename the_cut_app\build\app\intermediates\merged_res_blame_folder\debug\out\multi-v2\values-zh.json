{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,175,254,305,368,418,469,527,596,714,881,1064,1132,1197,1260,1335,1413,1497,1562,1625,1738,1803,1856,1918,1984,2069,2155,2233,2304,2367,2430,2488", "endColumns": "58,60,78,50,62,49,50,57,68,117,166,182,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,77,70,62,62,57,93", "endOffsets": "109,170,249,300,363,413,464,522,591,709,876,1059,1127,1192,1255,1330,1408,1492,1557,1620,1733,1798,1851,1913,1979,2064,2150,2228,2299,2362,2425,2483,2577"}, "to": {"startLines": "16,18,154,171,225,236,237,238,239,240,241,242,256,257,258,259,260,261,262,263,265,266,267,268,269,270,271,272,273,274,275,276,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "961,1086,10822,12047,16320,17201,17251,17302,17360,17429,17547,17714,18740,18808,18873,18936,19011,19089,19173,19238,19363,19476,19541,19594,19656,19722,19807,19893,19971,20042,20105,20168,20226", "endColumns": "58,60,78,50,62,49,50,57,68,117,166,182,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,77,70,62,62,57,93", "endOffsets": "1015,1142,10896,12093,16378,17246,17297,17355,17424,17542,17709,17892,18803,18868,18931,19006,19084,19168,19233,19296,19471,19536,19589,19651,19717,19802,19888,19966,20037,20100,20163,20221,20315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "63", "endOffsets": "114"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "10263", "endColumns": "63", "endOffsets": "10322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,572,632,698,762,821,879,945,1007,1072,1130,1195,1254,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,567,627,693,757,816,874,940,1002,1067,1125,1190,1249,1321,1394,1459,1530,1602,1665,1710,1756,1818,1880,1933,1995,2067,2134,2204,2273"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,110,111,121,131,132,134,141,142,152,153,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1396,1612,1841,1977,2050,2115,2566,2629,2751,2811,2877,2941,3000,3058,3124,3243,3308,3513,4279,4338,5193,5333,5398,7949,8021,8742,9320,9366,9479,9974,10027,10683,10755,11292,11362", "endColumns": "59,70,63,72,64,60,62,59,59,65,63,58,57,65,61,64,57,64,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "1451,1678,1900,2045,2110,2171,2624,2684,2806,2872,2936,2995,3053,3119,3181,3303,3361,3573,4333,4405,5261,5393,5464,8016,8079,8782,9361,9423,9536,10022,10084,10750,10817,11357,11426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1456,2176,2304,2364,2437,2689,3186,3366,3578,3862,4151,4410", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "1522,2228,2359,2432,2490,2746,3238,3428,3639,3917,4203,4468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,470", "endColumns": "74,83,64,65,59,64,61", "endOffsets": "125,209,274,340,400,465,527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,194,272,343,413,485,603,725,820,905,987,1071,1157,1261,1349,1435,1556,1641,1720,1777,1836,1910,1988,2073,2194,2258,2328,2401,2460,2529,2597,2663,2751,2832,2913,2977,3041,3097,3180,3256,3340,3439,3518,3585,3645,3710,3769,3848,3933", "endColumns": "65,72,77,70,69,71,117,121,94,84,81,83,85,103,87,85,120,84,78,56,58,73,77,84,120,63,69,72,58,68,67,65,87,80,80,63,63,55,82,75,83,98,78,66,59,64,58,78,84,72", "endOffsets": "116,189,267,338,408,480,598,720,815,900,982,1066,1152,1256,1344,1430,1551,1636,1715,1772,1831,1905,1983,2068,2189,2253,2323,2396,2455,2524,2592,2658,2746,2827,2908,2972,3036,3092,3175,3251,3335,3434,3513,3580,3640,3705,3764,3843,3928,4001"}, "to": {"startLines": "17,96,113,123,124,173,179,180,181,182,184,185,186,187,188,189,190,191,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,213,214,215,216,217,218,219,220,221,222,223,243,251,252,253,254,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1020,6745,8137,8831,8902,12152,12545,12663,12785,12880,13055,13137,13221,13307,13411,13499,13585,13706,13868,13947,14004,14063,14137,14215,14300,14421,14485,14555,14628,14687,14756,14824,14890,15385,15466,15547,15611,15675,15731,15814,15890,15974,16073,16152,17897,18379,18444,18503,18582,18667", "endColumns": "65,72,77,70,69,71,117,121,94,84,81,83,85,103,87,85,120,84,78,56,58,73,77,84,120,63,69,72,58,68,67,65,87,80,80,63,63,55,82,75,83,98,78,66,59,64,58,78,84,72", "endOffsets": "1081,6813,8210,8897,8967,12219,12658,12780,12875,12960,13132,13216,13302,13406,13494,13580,13701,13786,13942,13999,14058,14132,14210,14295,14416,14480,14550,14623,14682,14751,14819,14885,14973,15461,15542,15606,15670,15726,15809,15885,15969,16068,16147,16214,17952,18439,18498,18577,18662,18735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,302,345,403,478,542,660,715,787,843,896,960,1104,1164,1224,1275,1326,1392,1479,1556,1611,1682,1749,1816,1876,1951,2104,2171,2243,2297,2355,2413,2471,2561,2638,2728,2812,2884,2963,3045,3146,3255,3349,3398,3635,3692", "endColumns": "134,111,42,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,66,71,53,57,57,57,89,76,89,83,71,78,81,100,108,93,48,236,56,54", "endOffsets": "185,297,340,398,473,537,655,710,782,838,891,955,1099,1159,1219,1270,1321,1387,1474,1551,1606,1677,1744,1811,1871,1946,2099,2166,2238,2292,2350,2408,2466,2556,2633,2723,2807,2879,2958,3040,3141,3250,3344,3393,3630,3687,3742"}, "to": {"startLines": "84,85,89,90,91,92,93,94,95,109,112,114,120,125,126,133,143,147,148,149,150,151,157,160,165,167,168,169,170,172,174,175,178,183,192,208,209,210,211,212,224,230,231,232,234,235,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5561,5696,6260,6303,6361,6436,6500,6618,6673,7893,8084,8215,8598,8972,9032,9428,10089,10327,10393,10480,10557,10612,11035,11225,11564,11680,11755,11908,11975,12098,12224,12282,12487,12965,13791,14978,15068,15152,15224,15303,16219,16599,16708,16802,16907,17144,18324", "endColumns": "134,111,42,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,86,76,54,70,66,66,59,74,152,66,71,53,57,57,57,89,76,89,83,71,78,81,100,108,93,48,236,56,54", "endOffsets": "5691,5803,6298,6356,6431,6495,6613,6668,6740,7944,8132,8274,8737,9027,9087,9474,10135,10388,10475,10552,10607,10678,11097,11287,11619,11750,11903,11970,12042,12147,12277,12335,12540,13050,13863,15063,15147,15219,15298,15380,16315,16703,16797,16846,17139,17196,18374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,426,484,532,599,665,733,818,902,976,1048,1119,1190,1270,1349,1412,1488,1562,1633,1717,1788,1864,1934,2017,2082,2157,2228,2297,2367,2448,2508,2575,2667,2727,2788,3119,3435,3500,3584,3664,3719,3795,3867,3925,3996,4069,4124,4194,4239,4309,4391,4448,4513,4557,4621,4699,4742,4785,4842,4900,4958,5049,5141,5218,5278,5341,5400,5475,5538,5598,5660,5731,5787,5858,5934,5983,6055,6100,6150,6206,6262,6325,6384,6445,6516,6573", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,77,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,70,75,48,71,44,49,55,55,62,58,60,70,56,61", "endOffsets": "110,178,239,300,369,421,479,527,594,660,728,813,897,971,1043,1114,1185,1265,1344,1407,1483,1557,1628,1712,1783,1859,1929,2012,2077,2152,2223,2292,2362,2443,2503,2570,2662,2722,2783,3114,3430,3495,3579,3659,3714,3790,3862,3920,3991,4064,4119,4189,4234,4304,4386,4443,4508,4552,4616,4694,4737,4780,4837,4895,4953,5044,5136,5213,5273,5336,5395,5470,5533,5593,5655,5726,5782,5853,5929,5978,6050,6095,6145,6201,6257,6320,6379,6440,6511,6568,6630"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,86,87,88,97,98,99,100,101,102,103,104,105,106,107,108,115,116,117,118,119,122,127,128,129,130,135,136,137,138,139,140,144,145,155,156,158,159,163,164,166,176,177,226,227,228,229,233,244,245,246,247,248,249,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "532,592,660,721,782,851,903,1147,1195,1262,1328,1527,1683,1767,1905,2233,2495,3433,3644,3723,3786,3922,3996,4067,4208,4473,4549,4619,4702,4767,4842,4913,4982,5052,5133,5266,5469,5808,5868,5929,6818,7134,7199,7283,7363,7418,7494,7566,7624,7695,7768,7823,8279,8324,8394,8476,8533,8787,9092,9156,9234,9277,9541,9598,9656,9714,9805,9897,10140,10200,10901,10960,11102,11165,11431,11493,11624,12340,12411,16383,16432,16504,16549,16851,17957,18013,18076,18135,18196,18267,19301", "endColumns": "59,67,60,60,68,51,57,47,66,65,67,84,83,73,71,70,70,79,78,62,75,73,70,83,70,75,69,82,64,74,70,68,69,80,59,66,91,59,60,330,315,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,77,42,42,56,57,57,90,91,76,59,62,58,74,62,59,61,70,55,70,75,48,71,44,49,55,55,62,58,60,70,56,61", "endOffsets": "587,655,716,777,846,898,956,1190,1257,1323,1391,1607,1762,1836,1972,2299,2561,3508,3718,3781,3857,3991,4062,4146,4274,4544,4614,4697,4762,4837,4908,4977,5047,5128,5188,5328,5556,5863,5924,6255,7129,7194,7278,7358,7413,7489,7561,7619,7690,7763,7818,7888,8319,8389,8471,8528,8593,8826,9151,9229,9272,9315,9593,9651,9709,9800,9892,9969,10195,10258,10955,11030,11160,11220,11488,11559,11675,12406,12482,16427,16499,16544,16594,16902,18008,18071,18130,18191,18262,18319,19358"}}]}]}