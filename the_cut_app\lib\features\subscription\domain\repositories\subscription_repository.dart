import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/country.dart';
import '../entities/subscriber.dart';
import '../entities/subscription_plan.dart';
import '../entities/transaction_record.dart';

/// Repository interface for subscription-related operations
abstract class SubscriptionRepository {
  /// Get all available subscription plans
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans();

  /// Get subscription plans for a specific country
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlansByCountry(
      String countryCode);

  /// Get a specific subscription plan by ID
  Future<Either<Failure, SubscriptionPlan>> getSubscriptionPlanById(String id);

  /// Get the current subscriber information for a user
  Future<Either<Failure, Subscriber?>> getCurrentSubscriber(String userId);

  /// Subscribe to a plan
  Future<Either<Failure, Subscriber>> subscribeToPlan({
    required String userId,
    required String subscriptionPlanId,
    required String paymentMethod,
    required String countryCode,
    required String currencyCode,
    String? cardToken,
    String? cardLast4,
    String? cardBrand,
    int? cardExpiryMonth,
    int? cardExpiryYear,
    String? receiptData,
  });

  /// Cancel a subscription
  Future<Either<Failure, bool>> cancelSubscription({
    required String subscriberId,
    required String reason,
  });

  /// Update payment method
  Future<Either<Failure, bool>> updatePaymentMethod({
    required String subscriberId,
    required String paymentMethod,
    String? cardToken,
    String? cardLast4,
    String? cardBrand,
    int? cardExpiryMonth,
    int? cardExpiryYear,
  });

  /// Toggle auto-renew
  Future<Either<Failure, bool>> toggleAutoRenew({
    required String subscriberId,
    required bool autoRenew,
  });

  /// Get transaction history for a user
  Future<Either<Failure, List<TransactionRecord>>> getTransactionHistory(
      String userId);

  /// Get all supported countries
  Future<Either<Failure, List<Country>>> getSupportedCountries();

  /// Get country by code
  Future<Either<Failure, Country>> getCountryByCode(String countryCode);

  /// Get exchange rates
  Future<Either<Failure, Map<String, double>>> getExchangeRates();

  /// Validate receipt (for App Store or Play Store purchases)
  Future<Either<Failure, bool>> validateReceipt({
    required String userId,
    required String receiptData,
    required String productId,
  });

  /// Restore purchases
  Future<Either<Failure, Subscriber?>> restorePurchases({
    required String userId,
  });
}
