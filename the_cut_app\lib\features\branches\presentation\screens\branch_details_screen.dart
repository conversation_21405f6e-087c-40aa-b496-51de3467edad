import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../domain/entities/branch_entity.dart';

/// Branch details screen
class BranchDetailsScreen extends StatefulWidget {
  /// Branch ID
  final String branchId;

  /// Constructor
  const BranchDetailsScreen({
    super.key,
    required this.branchId,
  });

  @override
  State<BranchDetailsScreen> createState() => _BranchDetailsScreenState();
}

class _BranchDetailsScreenState extends State<BranchDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BranchService>(context, listen: false)
          .getBranchById(widget.branchId);
    });
  }

  Future<void> _deleteBranch(BranchEntity branch) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Branch'),
        content: Text(
          'Are you sure you want to delete ${branch.name}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final branchService = Provider.of<BranchService>(context, listen: false);
      final success = await branchService.deleteBranch(branch.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Branch deleted successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              branchService.error ?? 'Failed to delete branch',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final branchService = Provider.of<BranchService>(context);
    final branch = branchService.selectedBranch;

    return Scaffold(
      appBar: AppBar(
        title: Text(branch?.name ?? 'Branch Details'),
        actions: [
          if (branch != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/edit-branch',
                  arguments: branch.id,
                );
              },
            ),
        ],
      ),
      body: branchService.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : branch == null
              ? _buildBranchNotFound()
              : _buildBranchDetails(branch),
    );
  }

  Widget _buildBranchNotFound() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: AppColors.error.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'Branch Not Found',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'The branch you are looking for does not exist or has been deleted.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: 'Go Back',
              icon: Icons.arrow_back,
              onPressed: () {
                Navigator.pop(context);
              },
              width: 200.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBranchDetails(BranchEntity branch) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Branch header
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Branch name and status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          branch.name,
                          style: AppTextStyles.h3,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: branch.isActive
                              ? AppColors.success.withOpacity(0.1)
                              : AppColors.error.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          branch.isActive ? 'Active' : 'Inactive',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: branch.isActive
                                ? AppColors.success
                                : AppColors.error,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),

                  // Branch info
                  _buildInfoItem(
                    icon: Icons.location_on_outlined,
                    title: 'Address',
                    value: branch.address ?? 'No address',
                  ),
                  SizedBox(height: 8.h),

                  if (branch.phoneNumber != null) ...[
                    _buildInfoItem(
                      icon: Icons.phone_outlined,
                      title: 'Phone',
                      value: branch.phoneNumber!,
                    ),
                    SizedBox(height: 8.h),
                  ],

                  if (branch.email != null) ...[
                    _buildInfoItem(
                      icon: Icons.email_outlined,
                      title: 'Email',
                      value: branch.email!,
                    ),
                    SizedBox(height: 8.h),
                  ],

                  if (branch.managerName != null) ...[
                    _buildInfoItem(
                      icon: Icons.person_outline,
                      title: 'Manager',
                      value: branch.managerName!,
                    ),
                    SizedBox(height: 8.h),
                  ],

                  // Created and updated dates
                  if (branch.createdAt != null) ...[
                    _buildInfoItem(
                      icon: Icons.calendar_today_outlined,
                      title: 'Created',
                      value: _formatDate(branch.createdAt!),
                    ),
                    SizedBox(height: 8.h),
                  ],

                  if (branch.updatedAt != null) ...[
                    _buildInfoItem(
                      icon: Icons.update_outlined,
                      title: 'Last Updated',
                      value: _formatDate(branch.updatedAt!),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),

          // Actions
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Edit Branch',
                  icon: Icons.edit_outlined,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/edit-branch',
                      arguments: branch.id,
                    );
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CustomButton(
                  text: 'Delete Branch',
                  icon: Icons.delete_outline,
                  color: AppColors.error,
                  onPressed: () => _deleteBranch(branch),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Toggle status button
          CustomButton(
            text: branch.isActive
                ? 'Deactivate Branch'
                : 'Activate Branch',
            icon: branch.isActive
                ? Icons.toggle_off_outlined
                : Icons.toggle_on_outlined,
            color: branch.isActive
                ? Colors.grey.shade700
                : AppColors.success,
            onPressed: () {
              final branchService =
                  Provider.of<BranchService>(context, listen: false);
              branchService.updateBranch(
                branch.copyWith(isActive: !branch.isActive),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20.w,
          color: AppColors.textSecondary,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
