import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/branch_entity.dart';
import '../../domain/usecases/create_branch.dart' as create;
import '../../domain/usecases/delete_branch.dart' as delete;
import '../../domain/usecases/get_active_branches.dart';
import '../../domain/usecases/get_all_branches.dart';
import '../../domain/usecases/get_branch_by_id.dart' as get_by_id;
import '../../domain/usecases/update_branch.dart' as update;
import 'branch_event.dart';
import 'branch_state.dart';

/// Branch BLoC
class BranchBloc extends Bloc<BranchEvent, BranchState> {
  final GetAllBranches _getAllBranches;
  final get_by_id.GetBranchById _getBranchById;
  final create.CreateBranch _createBranch;
  final update.UpdateBranch _updateBranch;
  final delete.DeleteBranch _deleteBranch;
  final GetActiveBranches _getActiveBranches;

  BranchBloc({
    required GetAllBranches getAllBranches,
    required get_by_id.GetBranchById getBranchById,
    required create.CreateBranch createBranch,
    required update.UpdateBranch updateBranch,
    required delete.DeleteBranch deleteBranch,
    required GetActiveBranches getActiveBranches,
  })  : _getAllBranches = getAllBranches,
        _getBranchById = getBranchById,
        _createBranch = createBranch,
        _updateBranch = updateBranch,
        _deleteBranch = deleteBranch,
        _getActiveBranches = getActiveBranches,
        super(const BranchInitialState()) {
    on<GetAllBranchesEvent>(_onGetAllBranches);
    on<GetBranchByIdEvent>(_onGetBranchById);
    on<CreateBranchEvent>(_onCreateBranch);
    on<UpdateBranchEvent>(_onUpdateBranch);
    on<DeleteBranchEvent>(_onDeleteBranch);
    on<GetActiveBranchesEvent>(_onGetActiveBranches);
  }

  /// Handle get all branches event
  Future<void> _onGetAllBranches(
    GetAllBranchesEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    final result = await _getAllBranches();

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (branches) => emit(AllBranchesLoadedState(branches: branches)),
    );
  }

  /// Handle get branch by id event
  Future<void> _onGetBranchById(
    GetBranchByIdEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    final result = await _getBranchById(
      get_by_id.Params(branchId: event.branchId),
    );

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (branch) => emit(BranchLoadedState(branch: branch)),
    );
  }

  /// Handle create branch event
  Future<void> _onCreateBranch(
    CreateBranchEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    // Create a branch entity with a temporary ID
    // The actual ID will be assigned by the repository
    final branch = BranchEntity(
      id: 'temp-id',
      name: event.name,
      address: event.address,
      phoneNumber: event.phoneNumber,
      email: event.email,
      managerId: event.managerId,
      managerName: event.managerName,
      logoUrl: event.logoUrl,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final result = await _createBranch(
      create.Params(branch: branch),
    );

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (createdBranch) => emit(BranchCreatedState(branch: createdBranch)),
    );
  }

  /// Handle update branch event
  Future<void> _onUpdateBranch(
    UpdateBranchEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    final result = await _updateBranch(
      update.Params(branch: event.branch),
    );

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (updatedBranch) => emit(BranchUpdatedState(branch: updatedBranch)),
    );
  }

  /// Handle delete branch event
  Future<void> _onDeleteBranch(
    DeleteBranchEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    final result = await _deleteBranch(
      delete.Params(branchId: event.branchId),
    );

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (_) => emit(const BranchDeletedState()),
    );
  }

  /// Handle get active branches event
  Future<void> _onGetActiveBranches(
    GetActiveBranchesEvent event,
    Emitter<BranchState> emit,
  ) async {
    emit(const BranchLoadingState());

    final result = await _getActiveBranches();

    result.fold(
      (failure) => emit(BranchErrorState(message: failure.message)),
      (branches) => emit(ActiveBranchesLoadedState(branches: branches)),
    );
  }
}
