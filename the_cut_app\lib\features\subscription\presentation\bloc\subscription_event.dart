part of 'subscription_bloc.dart';

/// Base class for subscription events
abstract class SubscriptionEvent extends Equatable {
  /// Constructor
  const SubscriptionEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load all subscription plans
class LoadSubscriptionPlans extends SubscriptionEvent {}

/// Event to load subscription plans for a specific country
class LoadSubscriptionPlansByCountry extends SubscriptionEvent {
  /// Country code
  final String countryCode;

  /// Constructor
  const LoadSubscriptionPlansByCountry(this.countryCode);

  @override
  List<Object?> get props => [countryCode];
}

/// Event to load the current subscriber information for a user
class LoadCurrentSubscriber extends SubscriptionEvent {
  /// User ID
  final String userId;

  /// Constructor
  const LoadCurrentSubscriber(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Event to subscribe to a plan
class SubscribeToPlanEvent extends SubscriptionEvent {
  /// User ID
  final String userId;

  /// Subscription plan ID
  final String subscriptionPlanId;

  /// Payment method
  final String paymentMethod;

  /// Country code
  final String countryCode;

  /// Currency code
  final String currencyCode;

  /// Card token (for card payments)
  final String? cardToken;

  /// Last 4 digits of the card
  final String? cardLast4;

  /// Card brand
  final String? cardBrand;

  /// Card expiry month
  final int? cardExpiryMonth;

  /// Card expiry year
  final int? cardExpiryYear;

  /// Receipt data (for App Store or Play Store purchases)
  final String? receiptData;

  /// Constructor
  const SubscribeToPlanEvent({
    required this.userId,
    required this.subscriptionPlanId,
    required this.paymentMethod,
    required this.countryCode,
    required this.currencyCode,
    this.cardToken,
    this.cardLast4,
    this.cardBrand,
    this.cardExpiryMonth,
    this.cardExpiryYear,
    this.receiptData,
  });

  @override
  List<Object?> get props => [
        userId,
        subscriptionPlanId,
        paymentMethod,
        countryCode,
        currencyCode,
        cardToken,
        cardLast4,
        cardBrand,
        cardExpiryMonth,
        cardExpiryYear,
        receiptData,
      ];
}

/// Event to load all supported countries
class LoadSupportedCountries extends SubscriptionEvent {}

/// Event to select a country
class SelectCountry extends SubscriptionEvent {
  /// Country
  final Country country;

  /// Constructor
  const SelectCountry(this.country);

  @override
  List<Object?> get props => [country];
}
