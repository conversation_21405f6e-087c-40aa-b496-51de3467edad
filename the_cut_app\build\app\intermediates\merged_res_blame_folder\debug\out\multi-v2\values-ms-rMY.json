{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-ms-rMY/values-ms-rMY.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,194,258,344,407,477,536,613,681,747,809", "endColumns": "77,60,63,85,62,69,58,76,67,65,61,65", "endOffsets": "128,189,253,339,402,472,531,608,676,742,804,870"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1679,2485,2627,2691,2777,3056,3590,3784,4032,4338,4682,4988", "endColumns": "77,60,63,85,62,69,58,76,67,65,61,65", "endOffsets": "1752,2541,2686,2772,2835,3121,3644,3856,4095,4399,4739,5049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,448,494,562,661,731,983,1044,1136,1205,1259,1323,1653,1726,1792,1845,1904,1994,2127,2234,2291,2376,2454,2538,2605,2714,3022,3104,3181,3243,3303,3365,3425,3522,3611,3708,3799,3871,3950,4035,4233,4445,4564,4619,5379,5442", "endColumns": "177,214,45,67,98,69,251,60,91,68,53,63,329,72,65,52,58,89,132,106,56,84,77,83,66,108,307,81,76,61,59,61,59,96,88,96,90,71,78,84,197,211,118,54,759,62,54", "endOffsets": "228,443,489,557,656,726,978,1039,1131,1200,1254,1318,1648,1721,1787,1840,1899,1989,2122,2229,2286,2371,2449,2533,2600,2709,3017,3099,3176,3238,3298,3360,3420,3517,3606,3703,3794,3866,3945,4030,4228,4440,4559,4614,5374,5437,5492"}, "to": {"startLines": "84,85,89,90,91,92,93,94,95,109,112,114,120,125,126,133,143,146,147,148,149,150,156,159,164,166,167,168,169,171,173,174,177,182,191,207,208,209,210,211,223,229,230,231,233,234,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6392,6570,7708,7754,7822,7921,7991,8243,8304,10207,10480,10673,11113,11740,11813,12275,13222,13433,13523,13656,13763,13820,14420,14663,15103,15244,15353,15661,15743,15884,16065,16125,16357,17045,18186,19678,19775,19866,19938,20017,21136,21729,21941,22060,22203,22963,24583", "endColumns": "177,214,45,67,98,69,251,60,91,68,53,63,329,72,65,52,58,89,132,106,56,84,77,83,66,108,307,81,76,61,59,61,59,96,88,96,90,71,78,84,197,211,118,54,759,62,54", "endOffsets": "6565,6780,7749,7817,7916,7986,8238,8299,8391,10271,10529,10732,11438,11808,11874,12323,13276,13518,13651,13758,13815,13900,14493,14742,15165,15348,15656,15738,15815,15941,16120,16182,16412,17137,18270,19770,19861,19933,20012,20097,21329,21936,22055,22110,22958,23021,24633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,339,425,482,541,594,677,758,843,941,1039,1123,1201,1282,1365,1462,1550,1615,1700,1786,1872,1978,2060,2146,2226,2326,2407,2506,2592,2674,2762,2870,2948,3032,3152,3224,3289,4075,4849,4924,5044,5145,5200,5300,5393,5459,5549,5641,5698,5781,5832,5914,6012,6084,6157,6204,6288,6386,6434,6479,6555,6613,6680,6868,7043,7169,7238,7321,7398,7494,7576,7659,7734,7826,7900,7984,8070,8124,8250,8304,8364,8452,8515,8587,8652,8726,8814,8885", "endColumns": "67,76,61,76,85,56,58,52,82,80,84,97,97,83,77,80,82,96,87,64,84,85,85,105,81,85,79,99,80,98,85,81,87,107,77,83,119,71,64,785,773,74,119,100,54,99,92,65,89,91,56,82,50,81,97,71,72,46,83,97,47,44,75,57,66,187,174,125,68,82,76,95,81,82,74,91,73,83,85,53,125,53,59,87,62,71,64,73,87,70,82", "endOffsets": "118,195,257,334,420,477,536,589,672,753,838,936,1034,1118,1196,1277,1360,1457,1545,1610,1695,1781,1867,1973,2055,2141,2221,2321,2402,2501,2587,2669,2757,2865,2943,3027,3147,3219,3284,4070,4844,4919,5039,5140,5195,5295,5388,5454,5544,5636,5693,5776,5827,5909,6007,6079,6152,6199,6283,6381,6429,6474,6550,6608,6675,6863,7038,7164,7233,7316,7393,7489,7571,7654,7729,7821,7895,7979,8065,8119,8245,8299,8359,8447,8510,8582,8647,8721,8809,8880,8963"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,86,87,88,97,98,99,100,101,102,103,104,105,106,107,108,115,116,117,118,119,122,127,128,129,130,135,136,137,138,139,140,144,145,154,155,157,158,162,163,165,175,176,225,226,227,228,232,243,244,245,246,247,248,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "588,656,733,795,872,958,1015,1313,1366,1449,1530,1757,1933,2031,2183,2546,2840,3861,4100,4188,4253,4404,4490,4576,4744,5054,5140,5220,5320,5401,5500,5586,5668,5756,5864,6017,6272,6785,6857,6922,8501,9275,9350,9470,9571,9626,9726,9819,9885,9975,10067,10124,10737,10788,10870,10968,11040,11490,11879,11963,12061,12109,12392,12468,12526,12593,12781,12956,13281,13350,14247,14324,14498,14580,14936,15011,15170,16187,16271,21435,21489,21615,21669,22115,24150,24213,24285,24350,24424,24512,25894", "endColumns": "67,76,61,76,85,56,58,52,82,80,84,97,97,83,77,80,82,96,87,64,84,85,85,105,81,85,79,99,80,98,85,81,87,107,77,83,119,71,64,785,773,74,119,100,54,99,92,65,89,91,56,82,50,81,97,71,72,46,83,97,47,44,75,57,66,187,174,125,68,82,76,95,81,82,74,91,73,83,85,53,125,53,59,87,62,71,64,73,87,70,82", "endOffsets": "651,728,790,867,953,1010,1069,1361,1444,1525,1610,1850,2026,2110,2256,2622,2918,3953,4183,4248,4333,4485,4571,4677,4821,5135,5215,5315,5396,5495,5581,5663,5751,5859,5937,6096,6387,6852,6917,7703,9270,9345,9465,9566,9621,9721,9814,9880,9970,10062,10119,10202,10783,10865,10963,11035,11108,11532,11958,12056,12104,12149,12463,12521,12588,12776,12951,13077,13345,13428,14319,14415,14575,14658,15006,15098,15239,16266,16352,21484,21610,21664,21724,22198,24208,24280,24345,24419,24507,24578,25972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,197,265,359,428,489,557,622,687,755,829,889,951,1022,1086,1158,1221,1295,1366,1457,1532,1611,1703,1813,1907,1954,2001,2075,2139,2210,2279,2381,2469,2564", "endColumns": "63,77,67,93,68,60,67,64,64,67,73,59,61,70,63,71,62,73,70,90,74,78,91,109,93,46,46,73,63,70,68,101,87,94,93", "endOffsets": "114,192,260,354,423,484,552,617,682,750,824,884,946,1017,1081,1153,1216,1290,1361,1452,1527,1606,1698,1808,1902,1949,1996,2070,2134,2205,2274,2376,2464,2559,2653"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,110,111,121,131,132,134,141,142,151,152,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1615,1855,2115,2261,2355,2424,2923,2991,3126,3191,3259,3333,3393,3455,3526,3649,3721,3958,4826,4897,5942,6101,6180,10276,10386,11443,12154,12201,12328,13082,13153,13905,14007,14747,14842", "endColumns": "63,77,67,93,68,60,67,64,64,67,73,59,61,70,63,71,62,73,70,90,74,78,91,109,93,46,46,73,63,70,68,101,87,94,93", "endOffsets": "1674,1928,2178,2350,2419,2480,2986,3051,3186,3254,3328,3388,3450,3521,3585,3716,3779,4027,4892,4983,6012,6175,6267,10381,10475,11485,12196,12270,12387,13148,13217,14002,14090,14837,14931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,250,389,494,592,711,896,1094,1250,1339,1444,1537,1636,1805,1904,2003,2275,2383,2477,2537,2600,2681,2782,2886,3127,3193,3277,3364,3432,3511,3582,3658,3786,3879,3976,4052,4125,4185,4291,4409,4519,4649,4748,4820,4904,4994,5068,5188,5307", "endColumns": "89,104,138,104,97,118,184,197,155,88,104,92,98,168,98,98,271,107,93,59,62,80,100,103,240,65,83,86,67,78,70,75,127,92,96,75,72,59,105,117,109,129,98,71,83,89,73,119,118,92", "endOffsets": "140,245,384,489,587,706,891,1089,1245,1334,1439,1532,1631,1800,1899,1998,2270,2378,2472,2532,2595,2676,2777,2881,3122,3188,3272,3359,3427,3506,3577,3653,3781,3874,3971,4047,4120,4180,4286,4404,4514,4644,4743,4815,4899,4989,5063,5183,5302,5395"}, "to": {"startLines": "17,96,113,123,124,172,178,179,180,181,183,184,185,186,187,188,189,190,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,212,213,214,215,216,217,218,219,220,221,222,242,250,251,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1144,8396,10534,11537,11642,15946,16417,16602,16800,16956,17142,17247,17340,17439,17608,17707,17806,18078,18275,18369,18429,18492,18573,18674,18778,19019,19085,19169,19256,19324,19403,19474,19550,20102,20195,20292,20368,20441,20501,20607,20725,20835,20965,21064,24066,24638,24728,24802,24922,25041", "endColumns": "89,104,138,104,97,118,184,197,155,88,104,92,98,168,98,98,271,107,93,59,62,80,100,103,240,65,83,86,67,78,70,75,127,92,96,75,72,59,105,117,109,129,98,71,83,89,73,119,118,92", "endOffsets": "1229,8496,10668,11637,11735,16060,16597,16795,16951,17040,17242,17335,17434,17603,17702,17801,18073,18181,18364,18424,18487,18568,18669,18773,19014,19080,19164,19251,19319,19398,19469,19545,19673,20190,20287,20363,20436,20496,20602,20720,20830,20960,21059,21131,24145,24723,24797,24917,25036,25129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,308,375,441,515", "endColumns": "83,99,68,66,65,73,72", "endOffsets": "134,234,303,370,436,510,583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-ms-rMY\\values-ms-rMY.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,356,420,521,580,635,739,841,1006,1270,1561,1633,1704,1790,1884,2028,2180,2248,2321,2499,2570,2633,2708,2789,2923,3052,3161,3258,3345,3434,3506", "endColumns": "69,78,151,63,100,58,54,103,101,164,263,290,71,70,85,93,143,151,67,72,177,70,62,74,80,133,128,108,96,86,88,71,167", "endOffsets": "120,199,351,415,516,575,630,734,836,1001,1265,1556,1628,1699,1785,1879,2023,2175,2243,2316,2494,2565,2628,2703,2784,2918,3047,3156,3253,3340,3429,3501,3669"}, "to": {"startLines": "16,18,153,170,224,235,236,237,238,239,240,241,255,256,257,258,259,260,261,262,264,265,266,267,268,269,270,271,272,273,274,275,276", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1074,1234,14095,15820,21334,23026,23085,23140,23244,23346,23511,23775,25134,25206,25277,25363,25457,25601,25753,25821,25977,26155,26226,26289,26364,26445,26579,26708,26817,26914,27001,27090,27162", "endColumns": "69,78,151,63,100,58,54,103,101,164,263,290,71,70,85,93,143,151,67,72,177,70,62,74,80,133,128,108,96,86,88,71,167", "endOffsets": "1139,1308,14242,15879,21430,23080,23135,23239,23341,23506,23770,24061,25201,25272,25358,25452,25596,25748,25816,25889,26150,26221,26284,26359,26440,26574,26703,26812,26909,26996,27085,27157,27325"}}]}]}