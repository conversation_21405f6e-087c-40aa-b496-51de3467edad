import 'package:equatable/equatable.dart';

/// Team member entity
class TeamMemberEntity extends Equatable {
  /// Team member ID
  final String id;

  /// Team member name
  final String name;

  /// Team member email
  final String email;

  /// Team member phone number
  final String? phoneNumber;

  /// Team member photo URL
  final String? photoUrl;

  /// Team member role
  final String role;

  /// Team member branch ID
  final String? branchId;

  /// Team member branch name
  final String? branchName;

  /// Team member is active
  final bool isActive;

  /// Team member created at
  final DateTime? createdAt;

  /// Team member updated at
  final DateTime? updatedAt;

  /// Team member last login at
  final DateTime? lastLoginAt;

  /// Constructor
  const TeamMemberEntity({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.photoUrl,
    required this.role,
    this.branchId,
    this.branchName,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
  });

  /// Copy with
  TeamMemberEntity copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    String? role,
    String? branchId,
    String? branchName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
  }) {
    return TeamMemberEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoUrl: photoUrl ?? this.photoUrl,
      role: role ?? this.role,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phoneNumber,
        photoUrl,
        role,
        branchId,
        branchName,
        isActive,
        createdAt,
        updatedAt,
        lastLoginAt,
      ];
}
