import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';
import '../repositories/branch_repository.dart';

/// Get all branches use case
class GetAllBranches {
  final BranchRepository _repository;

  GetAllBranches(this._repository);

  /// Call the use case
  Future<Either<Failure, List<BranchEntity>>> call() {
    return _repository.getAllBranches();
  }
}
