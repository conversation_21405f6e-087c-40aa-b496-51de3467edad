import 'package:flutter/material.dart';

/// App color palette - Kuwait Construction Theme
class AppColors {
  // Primary colors - Construction Blue
  static const Color primary = Color(0xFF1565C0); // Deep Blue for construction
  static const Color primaryLight = Color(0xFF42A5F5); // Light Blue
  static const Color primaryDark = Color(0xFF0D47A1); // Dark Blue

  // Accent colors - Kuwait Gold
  static const Color accent = Color(0xFFFFB300); // Kuwait Gold
  static const Color accentLight = Color(0xFFFFE082); // Light Gold
  static const Color accentDark = Color(0xFFFF8F00); // Dark Gold
  
  // Background colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surface = Colors.white;
  static const Color card = Colors.white;
  static const Color cardDark = Color(0xFF1E1E1E);
  
  // Text colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFFBDBDBD);
  static const Color textDark = Colors.white;
  
  // Status colors - Construction themed
  static const Color success = Color(0xFF2E7D32); // Construction Green
  static const Color warning = Color(0xFFEF6C00); // Construction Orange
  static const Color error = Color(0xFFD32F2F); // Construction Red
  static const Color info = Color(0xFF1976D2); // Construction Info Blue

  // Construction specific colors
  static const Color concrete = Color(0xFF9E9E9E); // Concrete Gray
  static const Color steel = Color(0xFF607D8B); // Steel Blue Gray
  static const Color brick = Color(0xFF8D6E63); // Brick Brown
  static const Color safety = Color(0xFFFF5722); // Safety Orange
  
  // Divider and border colors
  static const Color divider = Color(0xFFE0E0E0);
  static const Color border = Color(0xFFE0E0E0);
  
  // Shimmer effect colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
}
