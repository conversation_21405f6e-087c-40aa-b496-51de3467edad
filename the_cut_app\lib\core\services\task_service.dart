import 'package:flutter/material.dart';
import '../../features/tasks/domain/entities/task_entity.dart';
import '../data/mock_data.dart';

/// Task service
class TaskService extends ChangeNotifier {
  List<TaskEntity> _tasks = [];
  TaskEntity? _selectedTask;
  bool _isLoading = false;
  String? _error;

  /// Get all tasks
  List<TaskEntity> get tasks => _tasks;

  /// Get selected task
  TaskEntity? get selectedTask => _selectedTask;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Load all tasks
  Future<void> loadTasks() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get tasks from mock data
      _tasks = List.from(MockData.tasks);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get tasks by branch ID
  Future<List<TaskEntity>> getTasksByBranchId(String branchId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get tasks from mock data
      final branchTasks = MockData.getTasksByBranchId(branchId);
      _isLoading = false;
      notifyListeners();
      return branchTasks;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Get tasks by assigned to ID
  Future<List<TaskEntity>> getTasksByAssignedToId(String assignedToId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get tasks from mock data
      final assignedTasks = MockData.getTasksByAssignedToId(assignedToId);
      _isLoading = false;
      notifyListeners();
      return assignedTasks;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Get task by ID
  Future<TaskEntity?> getTaskById(String taskId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get task from mock data
      _selectedTask = MockData.getTaskById(taskId);
      _isLoading = false;
      notifyListeners();
      return _selectedTask;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Create task
  Future<TaskEntity?> createTask(TaskEntity task) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Add task to mock data
      final newTask = MockData.addTask(task);
      _tasks.add(newTask);
      _selectedTask = newTask;
      _isLoading = false;
      notifyListeners();
      return newTask;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update task
  Future<TaskEntity?> updateTask(TaskEntity task) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Update task in mock data
      final updatedTask = MockData.updateTask(task);
      
      // Update task in list
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = updatedTask;
      }
      
      _selectedTask = updatedTask;
      _isLoading = false;
      notifyListeners();
      return updatedTask;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Delete task
  Future<bool> deleteTask(String taskId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Delete task from mock data
      MockData.deleteTask(taskId);
      
      // Remove task from list
      _tasks.removeWhere((task) => task.id == taskId);
      
      if (_selectedTask?.id == taskId) {
        _selectedTask = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Add comment to task
  Future<TaskEntity?> addCommentToTask(String taskId, TaskCommentEntity comment) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Add comment to task in mock data
      final updatedTask = MockData.addTaskComment(taskId, comment);
      
      // Update task in list
      final index = _tasks.indexWhere((t) => t.id == taskId);
      if (index != -1) {
        _tasks[index] = updatedTask;
      }
      
      if (_selectedTask?.id == taskId) {
        _selectedTask = updatedTask;
      }
      
      _isLoading = false;
      notifyListeners();
      return updatedTask;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update task status
  Future<TaskEntity?> updateTaskStatus(String taskId, TaskStatus status) async {
    final task = MockData.getTaskById(taskId);
    if (task != null) {
      final updatedTask = task.copyWith(
        status: status,
        completedAt: status == TaskStatus.completed ? DateTime.now() : null,
      );
      return updateTask(updatedTask);
    }
    _error = 'Task not found';
    notifyListeners();
    return null;
  }

  /// Set selected task
  void setSelectedTask(TaskEntity task) {
    _selectedTask = task;
    notifyListeners();
  }

  /// Clear selected task
  void clearSelectedTask() {
    _selectedTask = null;
    notifyListeners();
  }
}
