import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/errors/failures.dart';

/// Firebase authentication service interface
abstract class AuthService {
  /// Get the current user
  User? get currentUser;

  /// Get the auth state changes stream
  Stream<User?> get authStateChanges;

  /// Sign in with email and password
  Future<Either<AuthFailure, User>> signInWithEmailAndPassword(
    String email,
    String password,
  );

  /// Register with email and password
  Future<Either<AuthFailure, User>> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  );

  /// Sign out
  Future<Either<AuthFailure, void>> signOut();

  /// Send password reset email
  Future<Either<AuthFailure, void>> sendPasswordResetEmail(String email);

  /// Update user profile
  Future<Either<AuthFailure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  });

  /// Update email
  Future<Either<AuthFailure, void>> updateEmail(String email);

  /// Update password
  Future<Either<AuthFailure, void>> updatePassword(String password);

  /// Delete account
  Future<Either<AuthFailure, void>> deleteAccount();
}

/// Firebase authentication service implementation
class FirebaseAuthService implements AuthService {
  final FirebaseAuth _firebaseAuth;

  FirebaseAuthService(this._firebaseAuth);

  @override
  User? get currentUser => _firebaseAuth.currentUser;

  @override
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  @override
  Future<Either<AuthFailure, User>> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user == null) {
        return Left(const AuthFailure(
          message: 'User is null after sign in',
          code: 500,
        ));
      }
      
      return Right(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, User>> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user == null) {
        return Left(const AuthFailure(
          message: 'User is null after registration',
          code: 500,
        ));
      }
      
      // Update user profile with name
      await userCredential.user!.updateDisplayName(name);
      
      // Reload user to get updated data
      await userCredential.user!.reload();
      
      return Right(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> signOut() async {
    try {
      await _firebaseAuth.signOut();
      return const Right(null);
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        return Left(const AuthFailure(
          message: 'No user is signed in',
          code: 401,
        ));
      }
      
      await user.updateDisplayName(displayName);
      await user.updatePhotoURL(photoURL);
      
      // Reload user to get updated data
      await user.reload();
      
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> updateEmail(String email) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        return Left(const AuthFailure(
          message: 'No user is signed in',
          code: 401,
        ));
      }
      
      await user.updateEmail(email);
      
      // Reload user to get updated data
      await user.reload();
      
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> updatePassword(String password) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        return Left(const AuthFailure(
          message: 'No user is signed in',
          code: 401,
        ));
      }
      
      await user.updatePassword(password);
      
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        return Left(const AuthFailure(
          message: 'No user is signed in',
          code: 401,
        ));
      }
      
      await user.delete();
      
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(AuthFailure.fromCode(e.code));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }
}
