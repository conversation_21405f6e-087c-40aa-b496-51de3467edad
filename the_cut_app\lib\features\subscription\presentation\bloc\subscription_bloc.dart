import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/country.dart';
import '../../domain/entities/subscriber.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/usecases/get_current_subscriber_usecase.dart';
import '../../domain/usecases/get_subscription_plans_by_country_usecase.dart';
import '../../domain/usecases/get_subscription_plans_usecase.dart';
import '../../domain/usecases/get_supported_countries_usecase.dart';
import '../../domain/usecases/subscribe_to_plan_usecase.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

/// BLoC for subscription feature
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetSubscriptionPlansUseCase getSubscriptionPlans;
  final GetSubscriptionPlansByCountryUseCase getSubscriptionPlansByCountry;
  final GetCurrentSubscriberUseCase getCurrentSubscriber;
  final SubscribeToPlanUseCase subscribeToPlan;
  final GetSupportedCountriesUseCase getSupportedCountries;

  /// Constructor
  SubscriptionBloc({
    required this.getSubscriptionPlans,
    required this.getSubscriptionPlansByCountry,
    required this.getCurrentSubscriber,
    required this.subscribeToPlan,
    required this.getSupportedCountries,
  }) : super(SubscriptionInitial()) {
    on<LoadSubscriptionPlans>(_onLoadSubscriptionPlans);
    on<LoadSubscriptionPlansByCountry>(_onLoadSubscriptionPlansByCountry);
    on<LoadCurrentSubscriber>(_onLoadCurrentSubscriber);
    on<SubscribeToPlanEvent>(_onSubscribeToPlan);
    on<LoadSupportedCountries>(_onLoadSupportedCountries);
    on<SelectCountry>(_onSelectCountry);
  }

  /// Handle LoadSubscriptionPlans event
  Future<void> _onLoadSubscriptionPlans(
    LoadSubscriptionPlans event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscriptionPlansLoading());
    final result = await getSubscriptionPlans(NoParams());
    result.fold(
      (failure) => emit(SubscriptionPlansError(_mapFailureToMessage(failure))),
      (plans) => emit(SubscriptionPlansLoaded(plans)),
    );
  }

  /// Handle LoadSubscriptionPlansByCountry event
  Future<void> _onLoadSubscriptionPlansByCountry(
    LoadSubscriptionPlansByCountry event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscriptionPlansLoading());
    final result = await getSubscriptionPlansByCountry(
        CountryParams(countryCode: event.countryCode));
    result.fold(
      (failure) => emit(SubscriptionPlansError(_mapFailureToMessage(failure))),
      (plans) => emit(SubscriptionPlansLoaded(plans)),
    );
  }

  /// Handle LoadCurrentSubscriber event
  Future<void> _onLoadCurrentSubscriber(
    LoadCurrentSubscriber event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(CurrentSubscriberLoading());
    final result =
        await getCurrentSubscriber(UserParams(userId: event.userId));
    result.fold(
      (failure) =>
          emit(CurrentSubscriberError(_mapFailureToMessage(failure))),
      (subscriber) => emit(CurrentSubscriberLoaded(subscriber)),
    );
  }

  /// Handle SubscribeToPlanEvent event
  Future<void> _onSubscribeToPlan(
    SubscribeToPlanEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SubscribingToPlan());
    final result = await subscribeToPlan(SubscribeParams(
      userId: event.userId,
      subscriptionPlanId: event.subscriptionPlanId,
      paymentMethod: event.paymentMethod,
      countryCode: event.countryCode,
      currencyCode: event.currencyCode,
      cardToken: event.cardToken,
      cardLast4: event.cardLast4,
      cardBrand: event.cardBrand,
      cardExpiryMonth: event.cardExpiryMonth,
      cardExpiryYear: event.cardExpiryYear,
      receiptData: event.receiptData,
    ));
    result.fold(
      (failure) => emit(SubscriptionError(_mapFailureToMessage(failure))),
      (subscriber) => emit(SubscribedToPlan(subscriber)),
    );
  }

  /// Handle LoadSupportedCountries event
  Future<void> _onLoadSupportedCountries(
    LoadSupportedCountries event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(SupportedCountriesLoading());
    final result = await getSupportedCountries(NoParams());
    result.fold(
      (failure) =>
          emit(SupportedCountriesError(_mapFailureToMessage(failure))),
      (countries) => emit(SupportedCountriesLoaded(countries)),
    );
  }

  /// Handle SelectCountry event
  void _onSelectCountry(
    SelectCountry event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(CountrySelected(event.country));
    add(LoadSubscriptionPlansByCountry(event.country.code));
  }

  /// Map failure to message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Server failure';
      case CacheFailure:
        return 'Cache failure';
      case NetworkFailure:
        return 'Network failure';
      case NotFoundFailure:
        return 'Not found';
      default:
        return 'Unexpected error';
    }
  }
}
