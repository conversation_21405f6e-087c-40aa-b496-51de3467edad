import 'package:flutter/material.dart';
import '../../features/team/domain/entities/team_member_entity.dart';
import '../data/mock_data.dart';

/// Team service
class TeamService extends ChangeNotifier {
  List<TeamMemberEntity> _teamMembers = [];
  TeamMemberEntity? _selectedTeamMember;
  bool _isLoading = false;
  String? _error;

  /// Get all team members
  List<TeamMemberEntity> get teamMembers => _teamMembers;

  /// Get selected team member
  TeamMemberEntity? get selectedTeamMember => _selectedTeamMember;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Load all team members
  Future<void> loadTeamMembers() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get team members from mock data
      _teamMembers = List.from(MockData.teamMembers);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get team members by branch ID
  Future<List<TeamMemberEntity>> getTeamMembersByBranchId(String branchId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get team members from mock data
      final branchTeamMembers = MockData.getTeamMembersByBranchId(branchId);
      _isLoading = false;
      notifyListeners();
      return branchTeamMembers;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Get team member by ID
  Future<TeamMemberEntity?> getTeamMemberById(String teamMemberId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get team member from mock data
      _selectedTeamMember = MockData.getTeamMemberById(teamMemberId);
      _isLoading = false;
      notifyListeners();
      return _selectedTeamMember;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Create team member
  Future<TeamMemberEntity?> createTeamMember(TeamMemberEntity teamMember) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Add team member to mock data
      final newTeamMember = MockData.addTeamMember(teamMember);
      _teamMembers.add(newTeamMember);
      _selectedTeamMember = newTeamMember;
      _isLoading = false;
      notifyListeners();
      return newTeamMember;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update team member
  Future<TeamMemberEntity?> updateTeamMember(TeamMemberEntity teamMember) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Update team member in mock data
      final updatedTeamMember = MockData.updateTeamMember(teamMember);
      
      // Update team member in list
      final index = _teamMembers.indexWhere((m) => m.id == teamMember.id);
      if (index != -1) {
        _teamMembers[index] = updatedTeamMember;
      }
      
      _selectedTeamMember = updatedTeamMember;
      _isLoading = false;
      notifyListeners();
      return updatedTeamMember;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Delete team member
  Future<bool> deleteTeamMember(String teamMemberId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Delete team member from mock data
      MockData.deleteTeamMember(teamMemberId);
      
      // Remove team member from list
      _teamMembers.removeWhere((member) => member.id == teamMemberId);
      
      if (_selectedTeamMember?.id == teamMemberId) {
        _selectedTeamMember = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Get active team members
  Future<List<TeamMemberEntity>> getActiveTeamMembers() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get active team members from mock data
      final activeTeamMembers = MockData.getActiveTeamMembers();
      _isLoading = false;
      notifyListeners();
      return activeTeamMembers;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Set selected team member
  void setSelectedTeamMember(TeamMemberEntity teamMember) {
    _selectedTeamMember = teamMember;
    notifyListeners();
  }

  /// Clear selected team member
  void clearSelectedTeamMember() {
    _selectedTeamMember = null;
    notifyListeners();
  }
}
