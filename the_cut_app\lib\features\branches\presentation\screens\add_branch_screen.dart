import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/branch_entity.dart';

/// Add branch screen
class AddBranchScreen extends StatefulWidget {
  const AddBranchScreen({super.key});

  @override
  State<AddBranchScreen> createState() => _AddBranchScreenState();
}

class _AddBranchScreenState extends State<AddBranchScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _managerNameController = TextEditingController();
  bool _isActive = true;

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _managerNameController.dispose();
    super.dispose();
  }

  Future<void> _saveBranch() async {
    if (_formKey.currentState!.validate()) {
      final branchService = Provider.of<BranchService>(context, listen: false);
      
      final branch = BranchEntity(
        id: '', // Will be generated by the service
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        managerName: _managerNameController.text.trim(),
        isActive: _isActive,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final newBranch = await branchService.createBranch(branch);
      
      if (newBranch != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Branch created successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(branchService.error ?? 'Failed to create branch'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final branchService = Provider.of<BranchService>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Branch'),
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Form title
                Text(
                  'Branch Information',
                  style: AppTextStyles.h4,
                ),
                SizedBox(height: 24.h),
                
                // Branch name
                CustomTextField(
                  controller: _nameController,
                  hintText: 'Branch Name',
                  labelText: 'Branch Name',
                  prefixIcon: Icons.store_outlined,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter branch name';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                
                // Branch address
                CustomTextField(
                  controller: _addressController,
                  hintText: 'Address',
                  labelText: 'Address',
                  prefixIcon: Icons.location_on_outlined,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter branch address';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                
                // Branch phone
                CustomTextField(
                  controller: _phoneController,
                  hintText: 'Phone Number',
                  labelText: 'Phone Number',
                  prefixIcon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                ),
                SizedBox(height: 16.h),
                
                // Branch email
                CustomTextField(
                  controller: _emailController,
                  hintText: 'Email',
                  labelText: 'Email',
                  prefixIcon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                
                // Branch manager
                CustomTextField(
                  controller: _managerNameController,
                  hintText: 'Manager Name',
                  labelText: 'Manager Name',
                  prefixIcon: Icons.person_outline,
                  textInputAction: TextInputAction.done,
                ),
                SizedBox(height: 24.h),
                
                // Branch status
                Row(
                  children: [
                    Text(
                      'Branch Status:',
                      style: AppTextStyles.bodyMedium,
                    ),
                    SizedBox(width: 16.w),
                    Switch(
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                      activeColor: AppColors.primary,
                    ),
                    Text(
                      _isActive ? 'Active' : 'Inactive',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: _isActive ? AppColors.success : AppColors.error,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 32.h),
                
                // Save button
                CustomButton(
                  text: 'Save Branch',
                  isLoading: branchService.isLoading,
                  onPressed: _saveBranch,
                ),
                SizedBox(height: 16.h),
                
                // Cancel button
                CustomButton(
                  text: 'Cancel',
                  color: Colors.grey.shade200,
                  textColor: AppColors.textPrimary,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
