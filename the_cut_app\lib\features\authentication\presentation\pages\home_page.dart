import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_routes.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../domain/entities/user_entity.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_state.dart';

/// Home page
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;

  final List<String> _tabTitles = [
    'Dashboard',
    'Branches',
    'Team',
    'Tasks',
    'Finance',
  ];

  final List<IconData> _tabIcons = [
    Icons.dashboard_outlined,
    Icons.business_outlined,
    Icons.people_outline,
    Icons.task_outlined,
    Icons.attach_money_outlined,
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthenticatedState) {
          return _buildAuthenticatedContent(state.user);
        }
        
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }

  Widget _buildAuthenticatedContent(UserEntity user) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_tabTitles[_currentIndex]),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications page
            },
          ),
          IconButton(
            icon: CircleAvatar(
              radius: 14.r,
              backgroundColor: AppColors.primary,
              child: user.photoUrl != null
                  ? null
                  : Text(
                      user.name?.isNotEmpty == true
                          ? user.name![0].toUpperCase()
                          : user.email[0].toUpperCase(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
            ),
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRoutes.profile,
                arguments: user,
              );
            },
          ),
          SizedBox(width: 8.w),
        ],
      ),
      body: _buildTabContent(_currentIndex),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: List.generate(
          _tabTitles.length,
          (index) => BottomNavigationBarItem(
            icon: Icon(_tabIcons[index]),
            label: _tabTitles[index],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(_currentIndex),
    );
  }

  Widget _buildTabContent(int index) {
    switch (index) {
      case 0:
        return _buildDashboardTab();
      case 1:
        return _buildPlaceholderTab('Branches');
      case 2:
        return _buildPlaceholderTab('Team');
      case 3:
        return _buildPlaceholderTab('Tasks');
      case 4:
        return _buildPlaceholderTab('Finance');
      default:
        return _buildDashboardTab();
    }
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome message
          Text(
            'Welcome to The Cut',
            style: AppTextStyles.h3,
          ),
          SizedBox(height: 4.h),
          Text(
            'Your B2B Project Management Solution',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 24.h),
          
          // Stats cards
          Row(
            children: [
              _buildStatCard(
                'Projects',
                '12',
                Icons.business_center_outlined,
                AppColors.primary,
              ),
              SizedBox(width: 16.w),
              _buildStatCard(
                'Tasks',
                '48',
                Icons.task_outlined,
                AppColors.accent,
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              _buildStatCard(
                'Team',
                '24',
                Icons.people_outline,
                AppColors.info,
              ),
              SizedBox(width: 16.w),
              _buildStatCard(
                'Branches',
                '3',
                Icons.business_outlined,
                AppColors.success,
              ),
            ],
          ),
          SizedBox(height: 24.h),
          
          // Recent activities
          Text(
            'Recent Activities',
            style: AppTextStyles.h4,
          ),
          SizedBox(height: 16.h),
          _buildActivityItem(
            'New task assigned',
            'Design homepage mockup',
            '2 hours ago',
            Icons.task_outlined,
            AppColors.primary,
          ),
          _buildActivityItem(
            'Project updated',
            'Website Redesign',
            '4 hours ago',
            Icons.update_outlined,
            AppColors.info,
          ),
          _buildActivityItem(
            'New team member',
            'Ahmed joined the team',
            'Yesterday',
            Icons.person_add_outlined,
            AppColors.success,
          ),
          _buildActivityItem(
            'Payment received',
            '\$2,500 from Client XYZ',
            'Yesterday',
            Icons.payment_outlined,
            AppColors.accent,
          ),
          SizedBox(height: 24.h),
          
          // Upcoming deadlines
          Text(
            'Upcoming Deadlines',
            style: AppTextStyles.h4,
          ),
          SizedBox(height: 16.h),
          _buildDeadlineItem(
            'Website Redesign',
            'Final delivery',
            'Tomorrow',
            0.8,
          ),
          _buildDeadlineItem(
            'Mobile App Development',
            'Phase 1 completion',
            '3 days left',
            0.6,
          ),
          _buildDeadlineItem(
            'Marketing Campaign',
            'Content creation',
            '1 week left',
            0.4,
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderTab(String title) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction_outlined,
            size: 80.r,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            '$title Coming Soon',
            style: AppTextStyles.h3,
          ),
          SizedBox(height: 8.h),
          Text(
            'This feature is under development',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Icon(
                  icon,
                  color: color,
                  size: 24.r,
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.h2.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String description,
    String time,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20.r,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          Text(
            time,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeadlineItem(
    String title,
    String description,
    String timeLeft,
    double progress,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 8.w,
                  vertical: 4.h,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  timeLeft,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            description,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4.r),
                  child: LinearProgressIndicator(
                    value: progress,
                    backgroundColor: AppColors.divider,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      progress < 0.3
                          ? AppColors.error
                          : progress < 0.7
                              ? AppColors.warning
                              : AppColors.success,
                    ),
                    minHeight: 8.h,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '${(progress * 100).toInt()}%',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton(int index) {
    switch (index) {
      case 1: // Branches
        return FloatingActionButton(
          onPressed: () {
            // TODO: Navigate to add branch page
          },
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add),
        );
      case 2: // Team
        return FloatingActionButton(
          onPressed: () {
            // TODO: Navigate to add team member page
          },
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add),
        );
      case 3: // Tasks
        return FloatingActionButton(
          onPressed: () {
            // TODO: Navigate to add task page
          },
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add),
        );
      case 4: // Finance
        return FloatingActionButton(
          onPressed: () {
            // TODO: Navigate to add transaction page
          },
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add),
        );
      default:
        return null;
    }
  }
}
