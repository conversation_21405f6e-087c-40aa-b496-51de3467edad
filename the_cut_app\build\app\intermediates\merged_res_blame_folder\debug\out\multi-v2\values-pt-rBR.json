{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,147,241,379,480,571,686,857,1040,1180,1272,1378,1473,1572,1740,1850,1950,2228,2341,2442,2503,2569,2651,2751,2861,3074,3142,3216,3301,3363,3442,3513,3581,3696,3783,3880,3955,4024,4084,4189,4296,4400,4530,4633,4704,4776,4861,4933,5045,5158", "endColumns": "91,93,137,100,90,114,170,182,139,91,105,94,98,167,109,99,277,112,100,60,65,81,99,109,212,67,73,84,61,78,70,67,114,86,96,74,68,59,104,106,103,129,102,70,71,84,71,111,112,86", "endOffsets": "142,236,374,475,566,681,852,1035,1175,1267,1373,1468,1567,1735,1845,1945,2223,2336,2437,2498,2564,2646,2746,2856,3069,3137,3211,3296,3358,3437,3508,3576,3691,3778,3875,3950,4019,4079,4184,4291,4395,4525,4628,4699,4771,4856,4928,5040,5153,5240"}, "to": {"startLines": "169,248,265,275,276,324,330,331,332,333,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,364,365,366,367,368,369,370,371,372,373,374,394,402,403,404,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15254,22327,24367,25368,25469,29664,30144,30315,30498,30638,30830,30936,31031,31130,31298,31408,31508,31786,31989,32090,32151,32217,32299,32399,32509,32722,32790,32864,32949,33011,33090,33161,33229,33784,33871,33968,34043,34112,34172,34277,34384,34488,34618,34721,37593,38169,38254,38326,38438,38551", "endColumns": "91,93,137,100,90,114,170,182,139,91,105,94,98,167,109,99,277,112,100,60,65,81,99,109,212,67,73,84,61,78,70,67,114,86,96,74,68,59,104,106,103,129,102,70,71,84,71,111,112,86", "endOffsets": "15341,22416,24500,25464,25555,29774,30310,30493,30633,30725,30931,31026,31125,31293,31403,31503,31781,31894,32085,32146,32212,32294,32394,32504,32717,32785,32859,32944,33006,33085,33156,33224,33339,33866,33963,34038,34107,34167,34272,34379,34483,34613,34716,34787,37660,38249,38321,38433,38546,38633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "69,76,144,148,429,433,434", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7044,7657,13254,13551,40724,41149,41236", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "7109,7739,13328,13697,40888,41231,41312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14140,14227,14335,14402,14470,14536,14610", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "14222,14330,14397,14465,14531,14605,14674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "82,435", "startColumns": "4,4", "startOffsets": "8227,41317", "endColumns": "60,79", "endOffsets": "8283,41392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15777,16595,16737,16799,16881,17169,17701,17892,18132,18466,18818,19115", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "15854,16651,16794,16876,16940,17237,17755,17958,18202,18529,18875,19182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,422,469,540,641,715,935,995,1087,1159,1216,1280,1600,1672,1739,1793,1850,1934,2066,2180,2238,2327,2408,2497,2563,2664,2967,3044,3122,3185,3246,3308,3369,3469,3559,3662,3759,3834,3913,3999,4213,4424,4539,4595,5247,5312", "endColumns": "152,213,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,99,89,102,96,74,78,85,213,210,114,55,651,64,54", "endOffsets": "203,417,464,535,636,710,930,990,1082,1154,1211,1275,1595,1667,1734,1788,1845,1929,2061,2175,2233,2322,2403,2492,2558,2659,2962,3039,3117,3180,3241,3303,3364,3464,3554,3657,3754,3829,3908,3994,4208,4419,4534,4590,5242,5307,5362"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,298,299,300,301,302,308,311,316,318,319,320,321,323,325,326,329,334,343,359,360,361,362,363,375,381,382,383,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20470,20623,21662,21709,21780,21881,21955,22175,22235,24041,24310,24505,24950,25560,25632,26101,27032,27240,27324,27456,27570,27628,28181,28421,28847,28986,29087,29390,29467,29601,29779,29840,30083,30730,31899,33344,33447,33544,33619,33698,34792,35371,35582,35697,35820,36472,38114", "endColumns": "152,213,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,99,89,102,96,74,78,85,213,210,114,55,651,64,54", "endOffsets": "20618,20832,21704,21775,21876,21950,22170,22230,22322,24108,24362,24564,25265,25627,25694,26150,27084,27319,27451,27565,27623,27712,28257,28505,28908,29082,29385,29462,29540,29659,29835,29897,30139,30825,31984,33442,33539,33614,33693,33779,35001,35577,35692,35748,36467,36532,38164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4706,7425,7744", "endColumns": "75,81,73", "endOffsets": "4777,7502,7813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4782,4887,5035,5162,5270,5437,5567,5689,5939,6109,6217,6381,6511,6668,6825,6894,6960", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4882,5030,5157,5265,5432,5562,5684,5789,6104,6212,6376,6506,6663,6820,6889,6955,7039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,215,339,395,487,547,610,688,806,975,1244,1543,1619,1692,1780,1867,1981,2114,2182,2253,2416,2486,2543,2620,2699,2826,2952,3066,3154,3231,3315,3386", "endColumns": "76,82,123,55,91,59,62,77,117,168,268,298,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,113,87,76,83,70,163", "endOffsets": "127,210,334,390,482,542,605,683,801,970,1239,1538,1614,1687,1775,1862,1976,2109,2177,2248,2411,2481,2538,2615,2694,2821,2947,3061,3149,3226,3310,3381,3545"}, "to": {"startLines": "168,170,305,322,376,387,388,389,390,391,392,393,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,425,426,427,428", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15177,15346,27893,29545,35006,36537,36597,36660,36738,36856,37025,37294,38638,38714,38787,38875,38962,39076,39209,39277,39427,39590,39660,39717,39794,39873,40000,40126,40240,40328,40405,40489,40560", "endColumns": "76,82,123,55,91,59,62,77,117,168,268,298,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,113,87,76,83,70,163", "endOffsets": "15249,15424,28012,29596,35093,36592,36655,36733,36851,37020,37289,37588,38709,38782,38870,38957,39071,39204,39272,39343,39585,39655,39712,39789,39868,39995,40121,40235,40323,40400,40484,40555,40719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,303,304,312,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15711,15961,16231,16377,16463,16534,17029,17102,17242,17304,17372,17442,17502,17563,17637,17760,17829,18054,18962,19027,20046,20203,20282,24113,24219,25270,25978,26026,26155,26901,26963,27717,27804,28510,28606", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "15772,16038,16294,16458,16529,16590,17097,17164,17299,17367,17437,17497,17558,17632,17696,17824,17887,18127,19022,19110,20121,20277,20363,24214,24305,25315,26021,26096,26214,26958,27027,27799,27888,28601,28682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7114,7910,8009,8121", "endColumns": "114,98,111,105", "endOffsets": "7224,8004,8116,8222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,13877", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,13958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3192,3257,3947,4612,4690,4807,4922,4977,5071,5157,5229,5319,5416,5473,5567,5618,5696,5807,5878,5948,5996,6080,6178,6228,6275,6340,6398,6461,6653,6818,6957,7022,7108,7183,7272,7354,7431,7500,7591,7664,7750,7845,7898,8014,8064,8118,8185,8257,8330,8399,8474,8564,8634", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,85,94,52,115,49,53,66,71,72,68,74,89,69,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3187,3252,3942,4607,4685,4802,4917,4972,5066,5152,5224,5314,5411,5468,5562,5613,5691,5802,5873,5943,5991,6075,6173,6223,6270,6335,6393,6456,6648,6813,6952,7017,7103,7178,7267,7349,7426,7495,7586,7659,7745,7840,7893,8009,8059,8113,8180,8252,8325,8394,8469,8559,8629,8708"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,306,307,309,310,314,315,317,327,328,377,378,379,380,384,395,396,397,398,399,400,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14679,14753,14836,14898,14972,15055,15118,15429,15483,15559,15632,15859,16043,16145,16299,16656,16945,17963,18207,18302,18374,18534,18622,18710,18880,19187,19279,19358,19457,19531,19617,19704,19787,19870,19973,20126,20368,20837,20907,20972,22421,23086,23164,23281,23396,23451,23545,23631,23703,23793,23890,23947,24569,24620,24698,24809,24880,25320,25699,25783,25881,25931,26219,26284,26342,26405,26597,26762,27089,27154,28017,28092,28262,28344,28687,28756,28913,29902,29988,35098,35151,35267,35317,35753,37665,37737,37810,37879,37954,38044,39348", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,85,94,52,115,49,53,66,71,72,68,74,89,69,78", "endOffsets": "14748,14831,14893,14967,15050,15113,15172,15478,15554,15627,15706,15956,16140,16226,16372,16732,17024,18049,18297,18369,18461,18617,18705,18813,18957,19274,19353,19452,19526,19612,19699,19782,19865,19968,20041,20198,20465,20902,20967,21657,23081,23159,23276,23391,23446,23540,23626,23698,23788,23885,23942,24036,24615,24693,24804,24875,24945,25363,25778,25876,25926,25973,26279,26337,26400,26592,26757,26896,27149,27235,28087,28176,28339,28416,28751,28842,28981,29983,30078,35146,35262,35312,35366,35815,37732,37805,37874,37949,38039,38109,39422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7593,7818,8288,8547,8607,8697,8761,8832,8895,8970,9034,9088,9215,9273,9335,9389,9468,9609,9696,9778,9917,10000,10084,10223,10310,10390,10446,10497,10563,10637,10717,10804,10887,10960,11037,11106,11180,11282,11370,11447,11540,11636,11710,11790,11887,11939,12023,12089,12176,12264,12326,12390,12453,12521,12633,12744,12851,12961,13021,13474", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7652,7905,8362,8602,8692,8756,8827,8890,8965,9029,9083,9210,9268,9330,9384,9463,9604,9691,9773,9912,9995,10079,10218,10305,10385,10441,10492,10558,10632,10712,10799,10882,10955,11032,11101,11175,11277,11365,11442,11535,11631,11705,11785,11882,11934,12018,12084,12171,12259,12321,12385,12448,12516,12628,12739,12846,12956,13016,13071,13546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,14039", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,14135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5794", "endColumns": "144", "endOffsets": "5934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,430,431,432", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7229,7326,7507,8367,8450,13076,13167,13333,13405,13702,13787,13963,40893,40969,41036", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7321,7420,7588,8445,8542,13162,13249,13400,13469,13782,13872,14034,40964,41031,41144"}}]}]}