import 'package:equatable/equatable.dart';

/// Base failure class
abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

/// Server failure
class ServerFailure extends Failure {
  const ServerFailure({required super.message, super.code});
}

/// Cache failure
class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.code});
}

/// Network failure
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.code});
}

/// Authentication failure
class AuthFailure extends Failure {
  const AuthFailure({required super.message, super.code});

  factory AuthFailure.fromCode(String code) {
    switch (code) {
      case 'user-not-found':
        return const AuthFailure(
          message: 'No user found with this email.',
          code: 404,
        );
      case 'wrong-password':
        return const AuthFailure(
          message: 'Wrong password provided.',
          code: 401,
        );
      case 'invalid-email':
        return const AuthFailure(
          message: 'The email address is not valid.',
          code: 400,
        );
      case 'user-disabled':
        return const AuthFailure(
          message: 'This user has been disabled.',
          code: 403,
        );
      case 'email-already-in-use':
        return const AuthFailure(
          message: 'The email address is already in use.',
          code: 409,
        );
      case 'operation-not-allowed':
        return const AuthFailure(
          message: 'This operation is not allowed.',
          code: 403,
        );
      case 'weak-password':
        return const AuthFailure(
          message: 'The password is too weak.',
          code: 400,
        );
      default:
        return const AuthFailure(
          message: 'An authentication error occurred.',
          code: 500,
        );
    }
  }
}

/// Permission failure
class PermissionFailure extends Failure {
  const PermissionFailure({required super.message, super.code});
}

/// Validation failure
class ValidationFailure extends Failure {
  final Map<String, String>? errors;

  const ValidationFailure({
    required super.message,
    super.code,
    this.errors,
  });

  @override
  List<Object?> get props => [message, code, errors];
}

/// Unknown failure
class UnknownFailure extends Failure {
  const UnknownFailure(
      {super.message = 'An unknown error occurred.', int? code})
      : super(code: code ?? 500);
}
