import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/branch_repository.dart';

/// Delete branch use case
class DeleteBranch {
  final BranchRepository _repository;

  DeleteBranch(this._repository);

  /// Call the use case
  Future<Either<Failure, void>> call(Params params) {
    return _repository.deleteBranch(params.branchId);
  }
}

/// Delete branch parameters
class Params extends Equatable {
  final String branchId;

  const Params({
    required this.branchId,
  });

  @override
  List<Object?> get props => [branchId];
}
