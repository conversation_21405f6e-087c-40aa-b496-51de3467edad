import '../../features/authentication/domain/entities/user_entity.dart';

/// Mock data for testing and development
class MockData {
  static final List<UserEntity> users = [
    UserEntity(
      id: 'user1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '+96512345678',
      roles: ['owner'],
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    UserEntity(
      id: 'user2',
      name: 'فاطمة علي',
      email: '<EMAIL>',
      phone: '+96587654321',
      roles: ['owner'],
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    ),
  ];

  /// Get user by email and password for mock authentication
  static UserEntity? getUserByEmailAndPassword(String email, String password) {
    // For demo purposes, accept any password for existing users
    try {
      return users.firstWhere((user) => user.email == email);
    } catch (e) {
      return null;
    }
  }

  /// Check if email already exists
  static bool emailExists(String email) {
    return users.any((user) => user.email == email);
  }

  /// Add new user
  static void addUser(UserEntity user) {
    users.add(user);
  }
}
