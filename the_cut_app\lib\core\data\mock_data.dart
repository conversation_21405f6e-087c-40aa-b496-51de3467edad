import '../../features/authentication/domain/entities/user_entity.dart';
import '../../features/branches/domain/entities/branch_entity.dart';
import '../../features/team/domain/entities/team_member_entity.dart';
import '../../features/tasks/domain/entities/task_entity.dart';
import '../../features/finance/domain/entities/transaction_entity.dart';

/// Mock data for development purposes
/// This will be replaced with Firebase data in production
class MockData {
  /// Mock users
  static final List<UserEntity> users = [
    const UserEntity(
      id: 'user1',
      email: '<EMAIL>',
      name: 'Admin User',
      phoneNumber: '+1234567890',
      isEmailVerified: true,
      roles: ['admin'],
      isActive: true,
      createdAt: null,
      lastLoginAt: null,
    ),
    const UserEntity(
      id: 'user2',
      email: '<EMAIL>',
      name: 'Manager User',
      phoneNumber: '+1234567891',
      isEmailVerified: true,
      roles: ['manager'],
      branchId: 'branch1',
      isActive: true,
      createdAt: null,
      lastLoginAt: null,
    ),
    const UserEntity(
      id: 'user3',
      email: '<EMAIL>',
      name: 'Employee User',
      phoneNumber: '+1234567892',
      isEmailVerified: true,
      roles: ['employee'],
      branchId: 'branch2',
      isActive: true,
      createdAt: null,
      lastLoginAt: null,
    ),
  ];

  /// Mock branches
  static final List<BranchEntity> branches = [
    BranchEntity(
      id: 'branch1',
      name: 'Main Branch',
      address: '123 Main St, City',
      phoneNumber: '+1234567890',
      email: '<EMAIL>',
      managerId: 'user2',
      managerName: 'Manager User',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 100)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    BranchEntity(
      id: 'branch2',
      name: 'Downtown Branch',
      address: '456 Downtown Ave, City',
      phoneNumber: '+1234567891',
      email: '<EMAIL>',
      managerId: 'user3',
      managerName: 'Employee User',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 50)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    BranchEntity(
      id: 'branch3',
      name: 'Uptown Branch',
      address: '789 Uptown Blvd, City',
      phoneNumber: '+1234567892',
      email: '<EMAIL>',
      isActive: false,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  /// Get user by email and password
  static UserEntity? getUserByEmailAndPassword(String email, String password) {
    // In a real app, we would check the password hash
    // For mock data, we'll just check the email
    return users.firstWhere(
      (user) => user.email == email,
      orElse: () => throw Exception('User not found'),
    );
  }

  /// Get user by id
  static UserEntity? getUserById(String id) {
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get branch by id
  static BranchEntity? getBranchById(String id) {
    try {
      return branches.firstWhere((branch) => branch.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get branches by manager id
  static List<BranchEntity> getBranchesByManagerId(String managerId) {
    return branches.where((branch) => branch.managerId == managerId).toList();
  }

  /// Get active branches
  static List<BranchEntity> getActiveBranches() {
    return branches.where((branch) => branch.isActive).toList();
  }

  /// Add branch
  static BranchEntity addBranch(BranchEntity branch) {
    final newBranch = branch.copyWith(
      id: 'branch${branches.length + 1}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    branches.add(newBranch);
    return newBranch;
  }

  /// Update branch
  static BranchEntity updateBranch(BranchEntity branch) {
    final index = branches.indexWhere((b) => b.id == branch.id);
    if (index != -1) {
      final updatedBranch = branch.copyWith(
        updatedAt: DateTime.now(),
      );
      branches[index] = updatedBranch;
      return updatedBranch;
    }
    throw Exception('Branch not found');
  }

  /// Delete branch
  static void deleteBranch(String branchId) {
    branches.removeWhere((branch) => branch.id == branchId);
  }

  /// Mock team members
  static final List<TeamMemberEntity> teamMembers = [
    TeamMemberEntity(
      id: 'member1',
      name: 'John Doe',
      email: '<EMAIL>',
      phoneNumber: '+1234567893',
      role: 'Manager',
      branchId: 'branch1',
      branchName: 'Main Branch',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    TeamMemberEntity(
      id: 'member2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phoneNumber: '+1234567894',
      role: 'Stylist',
      branchId: 'branch1',
      branchName: 'Main Branch',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    TeamMemberEntity(
      id: 'member3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phoneNumber: '+1234567895',
      role: 'Receptionist',
      branchId: 'branch2',
      branchName: 'Downtown Branch',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    TeamMemberEntity(
      id: 'member4',
      name: 'Sarah Williams',
      email: '<EMAIL>',
      phoneNumber: '+1234567896',
      role: 'Stylist',
      branchId: 'branch2',
      branchName: 'Downtown Branch',
      isActive: false,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
  ];

  /// Get team members by branch id
  static List<TeamMemberEntity> getTeamMembersByBranchId(String branchId) {
    return teamMembers.where((member) => member.branchId == branchId).toList();
  }

  /// Get active team members
  static List<TeamMemberEntity> getActiveTeamMembers() {
    return teamMembers.where((member) => member.isActive).toList();
  }

  /// Get team member by id
  static TeamMemberEntity? getTeamMemberById(String id) {
    try {
      return teamMembers.firstWhere((member) => member.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Add team member
  static TeamMemberEntity addTeamMember(TeamMemberEntity member) {
    final newMember = member.copyWith(
      id: 'member${teamMembers.length + 1}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    teamMembers.add(newMember);
    return newMember;
  }

  /// Update team member
  static TeamMemberEntity updateTeamMember(TeamMemberEntity member) {
    final index = teamMembers.indexWhere((m) => m.id == member.id);
    if (index != -1) {
      final updatedMember = member.copyWith(
        updatedAt: DateTime.now(),
      );
      teamMembers[index] = updatedMember;
      return updatedMember;
    }
    throw Exception('Team member not found');
  }

  /// Delete team member
  static void deleteTeamMember(String memberId) {
    teamMembers.removeWhere((member) => member.id == memberId);
  }

  /// Mock tasks
  static final List<TaskEntity> tasks = [
    TaskEntity(
      id: 'task1',
      title: 'Order new supplies',
      description: 'Order new hair products and styling tools',
      status: TaskStatus.completed,
      priority: TaskPriority.high,
      dueDate: DateTime.now().add(const Duration(days: 2)),
      assignedToId: 'member1',
      assignedToName: 'John Doe',
      createdById: 'user1',
      createdByName: 'Admin User',
      branchId: 'branch1',
      branchName: 'Main Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      completedAt: DateTime.now().subtract(const Duration(days: 1)),
      comments: [
        TaskCommentEntity(
          id: 'comment1',
          text: 'Ordered from our regular supplier',
          createdById: 'member1',
          createdByName: 'John Doe',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ],
    ),
    TaskEntity(
      id: 'task2',
      title: 'Interview new stylist',
      description: 'Interview candidates for the new stylist position',
      status: TaskStatus.pending,
      priority: TaskPriority.medium,
      dueDate: DateTime.now().add(const Duration(days: 5)),
      assignedToId: 'member1',
      assignedToName: 'John Doe',
      createdById: 'user1',
      createdByName: 'Admin User',
      branchId: 'branch1',
      branchName: 'Main Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    TaskEntity(
      id: 'task3',
      title: 'Fix broken dryer',
      description: 'Call technician to fix the broken hair dryer',
      status: TaskStatus.inProgress,
      priority: TaskPriority.high,
      dueDate: DateTime.now().add(const Duration(days: 1)),
      assignedToId: 'member3',
      assignedToName: 'Mike Johnson',
      createdById: 'user2',
      createdByName: 'Manager User',
      branchId: 'branch2',
      branchName: 'Downtown Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
      comments: [
        TaskCommentEntity(
          id: 'comment2',
          text: 'Technician scheduled for tomorrow',
          createdById: 'member3',
          createdByName: 'Mike Johnson',
          createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        ),
      ],
    ),
  ];

  /// Get tasks by branch id
  static List<TaskEntity> getTasksByBranchId(String branchId) {
    return tasks.where((task) => task.branchId == branchId).toList();
  }

  /// Get tasks by assigned to id
  static List<TaskEntity> getTasksByAssignedToId(String assignedToId) {
    return tasks.where((task) => task.assignedToId == assignedToId).toList();
  }

  /// Get task by id
  static TaskEntity? getTaskById(String id) {
    try {
      return tasks.firstWhere((task) => task.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Add task
  static TaskEntity addTask(TaskEntity task) {
    final newTask = task.copyWith(
      id: 'task${tasks.length + 1}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    tasks.add(newTask);
    return newTask;
  }

  /// Update task
  static TaskEntity updateTask(TaskEntity task) {
    final index = tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      final updatedTask = task.copyWith(
        updatedAt: DateTime.now(),
      );
      tasks[index] = updatedTask;
      return updatedTask;
    }
    throw Exception('Task not found');
  }

  /// Delete task
  static void deleteTask(String taskId) {
    tasks.removeWhere((task) => task.id == taskId);
  }

  /// Add task comment
  static TaskEntity addTaskComment(String taskId, TaskCommentEntity comment) {
    final task = getTaskById(taskId);
    if (task != null) {
      final newComment = comment.copyWith(
        id: 'comment${task.comments?.length ?? 0 + 1}',
        createdAt: DateTime.now(),
      );

      final comments = List<TaskCommentEntity>.from(task.comments ?? []);
      comments.add(newComment);

      final updatedTask = task.copyWith(
        comments: comments,
        updatedAt: DateTime.now(),
      );

      return updateTask(updatedTask);
    }
    throw Exception('Task not found');
  }

  /// Mock transactions
  static final List<TransactionEntity> transactions = [
    TransactionEntity(
      id: 'transaction1',
      title: 'Haircut Service',
      description: 'Haircut service for customer John',
      amount: 50.0,
      type: TransactionType.income,
      category: TransactionCategory.services,
      date: DateTime.now().subtract(const Duration(days: 1)),
      createdById: 'user2',
      createdByName: 'Manager User',
      branchId: 'branch1',
      branchName: 'Main Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    TransactionEntity(
      id: 'transaction2',
      title: 'Hair Products Purchase',
      description: 'Purchased new hair products from supplier',
      amount: 200.0,
      type: TransactionType.expense,
      category: TransactionCategory.supplies,
      date: DateTime.now().subtract(const Duration(days: 2)),
      createdById: 'user2',
      createdByName: 'Manager User',
      branchId: 'branch1',
      branchName: 'Main Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    TransactionEntity(
      id: 'transaction3',
      title: 'Coloring Service',
      description: 'Hair coloring service for customer Sarah',
      amount: 120.0,
      type: TransactionType.income,
      category: TransactionCategory.services,
      date: DateTime.now().subtract(const Duration(days: 3)),
      createdById: 'user3',
      createdByName: 'Employee User',
      branchId: 'branch2',
      branchName: 'Downtown Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    TransactionEntity(
      id: 'transaction4',
      title: 'Rent Payment',
      description: 'Monthly rent payment for Downtown Branch',
      amount: 1500.0,
      type: TransactionType.expense,
      category: TransactionCategory.rent,
      date: DateTime.now().subtract(const Duration(days: 5)),
      createdById: 'user1',
      createdByName: 'Admin User',
      branchId: 'branch2',
      branchName: 'Downtown Branch',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];

  /// Get transactions by branch id
  static List<TransactionEntity> getTransactionsByBranchId(String branchId) {
    return transactions
        .where((transaction) => transaction.branchId == branchId)
        .toList();
  }

  /// Get transactions by type
  static List<TransactionEntity> getTransactionsByType(TransactionType type) {
    return transactions
        .where((transaction) => transaction.type == type)
        .toList();
  }

  /// Get transaction by id
  static TransactionEntity? getTransactionById(String id) {
    try {
      return transactions.firstWhere((transaction) => transaction.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Add transaction
  static TransactionEntity addTransaction(TransactionEntity transaction) {
    final newTransaction = transaction.copyWith(
      id: 'transaction${transactions.length + 1}',
      createdAt: DateTime.now(),
    );
    transactions.add(newTransaction);
    return newTransaction;
  }

  /// Update transaction
  static TransactionEntity updateTransaction(TransactionEntity transaction) {
    final index = transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      final updatedTransaction = transaction.copyWith(
        updatedAt: DateTime.now(),
      );
      transactions[index] = updatedTransaction;
      return updatedTransaction;
    }
    throw Exception('Transaction not found');
  }

  /// Delete transaction
  static void deleteTransaction(String transactionId) {
    transactions.removeWhere((transaction) => transaction.id == transactionId);
  }
}
