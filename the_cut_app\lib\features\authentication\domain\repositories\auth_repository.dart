import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';

/// Authentication repository interface
abstract class AuthRepository {
  /// Get the current user
  Future<Either<AuthFailure, UserEntity?>> getCurrentUser();

  /// Get the auth state changes stream
  Stream<UserEntity?> get authStateChanges;

  /// Sign in with email and password
  Future<Either<AuthFailure, UserEntity>> signInWithEmailAndPassword(
    String email,
    String password,
  );

  /// Register with email and password
  Future<Either<AuthFailure, UserEntity>> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  );

  /// Sign out
  Future<Either<AuthFailure, void>> signOut();

  /// Send password reset email
  Future<Either<AuthFailure, void>> sendPasswordResetEmail(String email);

  /// Update user profile
  Future<Either<AuthFailure, UserEntity>> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  });

  /// Update email
  Future<Either<AuthFailure, void>> updateEmail(String email);

  /// Update password
  Future<Either<AuthFailure, void>> updatePassword(String password);

  /// Delete account
  Future<Either<AuthFailure, void>> deleteAccount();
  
  /// Update user role
  Future<Either<AuthFailure, UserEntity>> updateUserRole(
    String userId,
    List<String> roles,
  );
  
  /// Update user branch
  Future<Either<AuthFailure, UserEntity>> updateUserBranch(
    String userId,
    String branchId,
  );
  
  /// Get all users
  Future<Either<AuthFailure, List<UserEntity>>> getAllUsers();
  
  /// Get user by id
  Future<Either<AuthFailure, UserEntity>> getUserById(String userId);
}
