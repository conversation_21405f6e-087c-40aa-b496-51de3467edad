import '../../features/real_estate_projects/domain/entities/project_entity.dart';
import '../../features/real_estate_projects/domain/entities/phase_entity.dart';
import '../../features/real_estate_projects/domain/entities/project_file_entity.dart';
import '../../features/real_estate_projects/domain/entities/schedule_entity.dart';

/// بيانات وهمية للمشاريع العقارية
class MockRealEstateData {
  /// المشاريع الوهمية
  static final List<ProjectEntity> projects = [
    ProjectEntity(
      id: 'project1',
      name: 'مجمع الياسمين السكني',
      description: 'مجمع سكني متكامل يضم 50 وحدة سكنية مع جميع الخدمات',
      propertyType: PropertyType.residential,
      currentPhase: ConstructionPhase.civilWork,
      status: ProjectStatus.inProgress,
      location: 'الرياض - حي النرجس',
      ownerId: 'owner1',
      ownerName: 'أحمد محمد العلي',
      supervisorId: 'supervisor1',
      supervisorName: 'م. خالد السعد',
      startDate: DateTime.now().subtract(const Duration(days: 120)),
      expectedEndDate: DateTime.now().add(const Duration(days: 300)),
      totalBudget: 15000000.0,
      spentAmount: 6000000.0,
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    ProjectEntity(
      id: 'project2',
      name: 'برج التجارة المركزي',
      description: 'برج تجاري من 20 طابق في وسط المدينة',
      propertyType: PropertyType.commercial,
      currentPhase: ConstructionPhase.blackWork,
      status: ProjectStatus.inProgress,
      location: 'الرياض - وسط المدينة',
      ownerId: 'owner2',
      ownerName: 'شركة الأعمال المتقدمة',
      supervisorId: 'supervisor2',
      supervisorName: 'م. سارة أحمد',
      startDate: DateTime.now().subtract(const Duration(days: 60)),
      expectedEndDate: DateTime.now().add(const Duration(days: 540)),
      totalBudget: 45000000.0,
      spentAmount: 12000000.0,
      createdAt: DateTime.now().subtract(const Duration(days: 80)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    ProjectEntity(
      id: 'project3',
      name: 'فيلا العائلة الذهبية',
      description: 'فيلا فاخرة مع حديقة ومسبح',
      propertyType: PropertyType.residential,
      currentPhase: ConstructionPhase.finishing,
      status: ProjectStatus.inProgress,
      location: 'الرياض - حي الملقا',
      ownerId: 'owner3',
      ownerName: 'فهد عبدالله الشمري',
      supervisorId: 'supervisor1',
      supervisorName: 'م. خالد السعد',
      startDate: DateTime.now().subtract(const Duration(days: 200)),
      expectedEndDate: DateTime.now().add(const Duration(days: 45)),
      totalBudget: 3500000.0,
      spentAmount: 3200000.0,
      createdAt: DateTime.now().subtract(const Duration(days: 220)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
    ),
  ];

  /// المراحل الوهمية
  static final List<PhaseEntity> phases = [
    // مراحل المشروع الأول
    PhaseEntity(
      id: 'phase1_1',
      projectId: 'project1',
      phaseType: ConstructionPhase.blackWork,
      name: 'مرحلة الأسود',
      description: 'أعمال الحفر والأساسات والخرسانة',
      status: PhaseStatus.completed,
      startDate: DateTime.now().subtract(const Duration(days: 120)),
      expectedEndDate: DateTime.now().subtract(const Duration(days: 80)),
      actualEndDate: DateTime.now().subtract(const Duration(days: 75)),
      progressPercentage: 100.0,
      allocatedBudget: 5000000.0,
      spentAmount: 4800000.0,
      availableSections: [
        PhaseSection.plans,
        PhaseSection.licenses,
        PhaseSection.images,
        PhaseSection.documents,
        PhaseSection.schedules,
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
      updatedAt: DateTime.now().subtract(const Duration(days: 75)),
    ),
    PhaseEntity(
      id: 'phase1_2',
      projectId: 'project1',
      phaseType: ConstructionPhase.civilWork,
      name: 'الأعمال المدنية',
      description: 'أعمال الكهرباء والسباكة والتكييف',
      status: PhaseStatus.inProgress,
      startDate: DateTime.now().subtract(const Duration(days: 70)),
      expectedEndDate: DateTime.now().add(const Duration(days: 60)),
      progressPercentage: 65.0,
      allocatedBudget: 6000000.0,
      spentAmount: 3900000.0,
      availableSections: [
        PhaseSection.plans,
        PhaseSection.images,
        PhaseSection.videos,
        PhaseSection.documents,
        PhaseSection.schedules,
        PhaseSection.reports,
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 80)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    PhaseEntity(
      id: 'phase1_3',
      projectId: 'project1',
      phaseType: ConstructionPhase.finishing,
      name: 'أعمال التشطيب',
      description: 'أعمال البلاط والدهان والتشطيبات النهائية',
      status: PhaseStatus.notStarted,
      expectedEndDate: DateTime.now().add(const Duration(days: 180)),
      progressPercentage: 0.0,
      allocatedBudget: 4000000.0,
      spentAmount: 0.0,
      availableSections: [
        PhaseSection.plans,
        PhaseSection.images,
        PhaseSection.documents,
        PhaseSection.schedules,
      ],
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
    ),
  ];

  /// الملفات الوهمية
  static final List<ProjectFileEntity> files = [
    ProjectFileEntity(
      id: 'file1',
      projectId: 'project1',
      phaseId: 'phase1_1',
      section: PhaseSection.plans,
      fileName: 'architectural_plan_v1.pdf',
      originalFileName: 'المخطط المعماري النسخة الأولى.pdf',
      fileType: FileType.pdf,
      fileSize: 2048576, // 2MB
      fileUrl: 'https://example.com/files/architectural_plan_v1.pdf',
      description: 'المخطط المعماري للمشروع - النسخة الأولى',
      uploadDate: DateTime.now().subtract(const Duration(days: 140)),
      uploadedById: 'supervisor1',
      uploadedByName: 'م. خالد السعد',
      tags: ['مخطط', 'معماري', 'أساسي'],
    ),
    ProjectFileEntity(
      id: 'file2',
      projectId: 'project1',
      phaseId: 'phase1_1',
      section: PhaseSection.images,
      fileName: 'foundation_progress_1.jpg',
      originalFileName: 'تقدم أعمال الأساسات 1.jpg',
      fileType: FileType.image,
      fileSize: 1536000, // 1.5MB
      fileUrl: 'https://example.com/files/foundation_progress_1.jpg',
      thumbnailUrl: 'https://example.com/files/foundation_progress_1_thumb.jpg',
      description: 'صورة تقدم أعمال الأساسات',
      uploadDate: DateTime.now().subtract(const Duration(days: 100)),
      uploadedById: 'supervisor1',
      uploadedByName: 'م. خالد السعد',
      tags: ['أساسات', 'تقدم', 'صورة'],
      isFavorite: true,
    ),
    ProjectFileEntity(
      id: 'file3',
      projectId: 'project1',
      phaseId: 'phase1_2',
      section: PhaseSection.videos,
      fileName: 'electrical_work_inspection.mp4',
      originalFileName: 'معاينة الأعمال الكهربائية.mp4',
      fileType: FileType.video,
      fileSize: 15728640, // 15MB
      fileUrl: 'https://example.com/files/electrical_work_inspection.mp4',
      thumbnailUrl: 'https://example.com/files/electrical_work_inspection_thumb.jpg',
      description: 'فيديو معاينة الأعمال الكهربائية',
      uploadDate: DateTime.now().subtract(const Duration(days: 30)),
      uploadedById: 'supervisor1',
      uploadedByName: 'م. خالد السعد',
      tags: ['كهرباء', 'معاينة', 'فيديو'],
    ),
  ];

  /// المواعيد الوهمية
  static final List<ScheduleEntity> schedules = [
    ScheduleEntity(
      id: 'schedule1',
      projectId: 'project1',
      phaseId: 'phase1_2',
      title: 'اجتماع مراجعة التقدم الأسبوعي',
      description: 'مراجعة تقدم أعمال الكهرباء والسباكة',
      type: ScheduleType.meeting,
      status: ScheduleStatus.scheduled,
      priority: SchedulePriority.high,
      scheduledDateTime: DateTime.now().add(const Duration(days: 2, hours: 10)),
      durationMinutes: 90,
      location: 'موقع المشروع - مكتب الإشراف',
      createdById: 'supervisor1',
      createdByName: 'م. خالد السعد',
      participantNames: ['أحمد محمد العلي', 'مقاول الكهرباء', 'مقاول السباكة'],
      reminderMinutes: [60, 1440], // ساعة و يوم
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    ScheduleEntity(
      id: 'schedule2',
      projectId: 'project1',
      phaseId: 'phase1_2',
      title: 'معاينة أعمال التكييف',
      description: 'معاينة تركيب وحدات التكييف المركزي',
      type: ScheduleType.inspection,
      status: ScheduleStatus.confirmed,
      priority: SchedulePriority.medium,
      scheduledDateTime: DateTime.now().add(const Duration(days: 5, hours: 14)),
      durationMinutes: 120,
      location: 'موقع المشروع - الطابق الأرضي',
      createdById: 'supervisor1',
      createdByName: 'م. خالد السعد',
      participantNames: ['مقاول التكييف', 'مهندس التكييف'],
      reminderMinutes: [120], // ساعتين
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  /// الحصول على مشروع بالمعرف
  static ProjectEntity? getProjectById(String id) {
    try {
      return projects.firstWhere((project) => project.id == id);
    } catch (e) {
      return null;
    }
  }

  /// إضافة مشروع جديد
  static ProjectEntity addProject(ProjectEntity project) {
    final newProject = project.copyWith(
      id: 'project${projects.length + 1}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    projects.add(newProject);
    return newProject;
  }

  /// تحديث مشروع
  static ProjectEntity updateProject(ProjectEntity project) {
    final index = projects.indexWhere((p) => p.id == project.id);
    if (index != -1) {
      final updatedProject = project.copyWith(
        updatedAt: DateTime.now(),
      );
      projects[index] = updatedProject;
      return updatedProject;
    }
    throw Exception('المشروع غير موجود');
  }

  /// حذف مشروع
  static void deleteProject(String projectId) {
    projects.removeWhere((project) => project.id == projectId);
    phases.removeWhere((phase) => phase.projectId == projectId);
    files.removeWhere((file) => file.projectId == projectId);
    schedules.removeWhere((schedule) => schedule.projectId == projectId);
  }

  /// الحصول على مراحل مشروع
  static List<PhaseEntity> getPhasesByProjectId(String projectId) {
    return phases.where((phase) => phase.projectId == projectId).toList();
  }

  /// الحصول على ملفات مشروع
  static List<ProjectFileEntity> getFilesByProjectId(String projectId, {String? phaseId}) {
    var projectFiles = files.where((file) => file.projectId == projectId);
    if (phaseId != null) {
      projectFiles = projectFiles.where((file) => file.phaseId == phaseId);
    }
    return projectFiles.toList();
  }

  /// الحصول على مواعيد مشروع
  static List<ScheduleEntity> getSchedulesByProjectId(String projectId) {
    return schedules.where((schedule) => schedule.projectId == projectId).toList();
  }

  /// إضافة ملف جديد
  static ProjectFileEntity addFile(ProjectFileEntity file) {
    final newFile = file.copyWith(
      id: 'file${files.length + 1}',
      uploadDate: DateTime.now(),
    );
    files.add(newFile);
    return newFile;
  }

  /// إضافة موعد جديد
  static ScheduleEntity addSchedule(ScheduleEntity schedule) {
    final newSchedule = schedule.copyWith(
      id: 'schedule${schedules.length + 1}',
      createdAt: DateTime.now(),
    );
    schedules.add(newSchedule);
    return newSchedule;
  }
}
