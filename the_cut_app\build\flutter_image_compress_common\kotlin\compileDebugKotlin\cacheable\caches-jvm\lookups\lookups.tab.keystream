  Context android.content  cacheDir android.content.Context  getCACHEDir android.content.Context  getCacheDir android.content.Context  setCacheDir android.content.Context  Bitmap android.graphics  
BitmapFactory android.graphics  Matrix android.graphics  Bitmap android.graphics.Bitmap  ByteArrayOutputStream android.graphics.Bitmap  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  Matrix android.graphics.Bitmap  	calcScale android.graphics.Bitmap  compress android.graphics.Bitmap  convertFormatIndexToFormat android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  getCALCScale android.graphics.Bitmap  getCOMPRESS android.graphics.Bitmap  getCONVERTFormatIndexToFormat android.graphics.Bitmap  getCalcScale android.graphics.Bitmap  getCompress android.graphics.Bitmap  getConvertFormatIndexToFormat android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  getLOG android.graphics.Bitmap  getLog android.graphics.Bitmap  getMAX android.graphics.Bitmap  getMIN android.graphics.Bitmap  getMax android.graphics.Bitmap  getMin android.graphics.Bitmap  	getROTATE android.graphics.Bitmap  	getRotate android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  log android.graphics.Bitmap  max android.graphics.Bitmap  min android.graphics.Bitmap  rotate android.graphics.Bitmap  	setHeight android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  PNG &android.graphics.Bitmap.CompressFormat  WEBP &android.graphics.Bitmap.CompressFormat  equals &android.graphics.Bitmap.CompressFormat  RGB_565 android.graphics.Bitmap.Config  Options android.graphics.BitmapFactory  decodeByteArray android.graphics.BitmapFactory  
decodeFile android.graphics.BitmapFactory  inDither &android.graphics.BitmapFactory.Options  inJustDecodeBounds &android.graphics.BitmapFactory.Options  inPreferredConfig &android.graphics.BitmapFactory.Options  inSampleSize &android.graphics.BitmapFactory.Options  	setRotate android.graphics.Matrix  Build 
android.os  Handler 
android.os  Looper 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  post android.os.Handler  
getMainLooper android.os.Looper  Log android.util  SparseArray android.util  i android.util.Log  append android.util.SparseArray  get android.util.SparseArray  
ExifInterface androidx.exifinterface.media  getROTATIONDegrees *androidx.exifinterface.media.ExifInterface  getRotationDegrees *androidx.exifinterface.media.ExifInterface  rotationDegrees *androidx.exifinterface.media.ExifInterface  setRotationDegrees *androidx.exifinterface.media.ExifInterface  
HeifWriter androidx.heifwriter  Builder androidx.heifwriter.HeifWriter  INPUT_MODE_BITMAP androidx.heifwriter.HeifWriter  	addBitmap androidx.heifwriter.HeifWriter  close androidx.heifwriter.HeifWriter  start androidx.heifwriter.HeifWriter  stop androidx.heifwriter.HeifWriter  build &androidx.heifwriter.HeifWriter.Builder  setMaxImages &androidx.heifwriter.HeifWriter.Builder  
setQuality &androidx.heifwriter.HeifWriter.Builder  Boolean )com.fluttercandies.flutter_image_compress  Build )com.fluttercandies.flutter_image_compress  
CommonHandler )com.fluttercandies.flutter_image_compress  CompressFileHandler )com.fluttercandies.flutter_image_compress  CompressListHandler )com.fluttercandies.flutter_image_compress  FormatRegister )com.fluttercandies.flutter_image_compress  HeifHandler )com.fluttercandies.flutter_image_compress  ImageCompressPlugin )com.fluttercandies.flutter_image_compress  Int )com.fluttercandies.flutter_image_compress  
MethodChannel )com.fluttercandies.flutter_image_compress  showLog )com.fluttercandies.flutter_image_compress  Boolean =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  Build =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
CommonHandler =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  CompressFileHandler =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  CompressListHandler =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  Context =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
FlutterPlugin =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  FormatRegister =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  HeifHandler =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  Int =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
MethodCall =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
MethodChannel =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  Result =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  channel =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  context =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
getSHOWLog =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  
getShowLog =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  	handleLog =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  showLog =com.fluttercandies.flutter_image_compress.ImageCompressPlugin  Boolean Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  Build Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  
CommonHandler Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  CompressFileHandler Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  CompressListHandler Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  Context Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  
FlutterPlugin Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  FormatRegister Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  HeifHandler Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  Int Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  
MethodCall Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  
MethodChannel Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  Result Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  showLog Gcom.fluttercandies.flutter_image_compress.ImageCompressPlugin.Companion  Any .com.fluttercandies.flutter_image_compress.core  Boolean .com.fluttercandies.flutter_image_compress.core  	ByteArray .com.fluttercandies.flutter_image_compress.core  ByteArrayOutputStream .com.fluttercandies.flutter_image_compress.core  CompressFileHandler .com.fluttercandies.flutter_image_compress.core  CompressListHandler .com.fluttercandies.flutter_image_compress.core  	Exception .com.fluttercandies.flutter_image_compress.core  	Executors .com.fluttercandies.flutter_image_compress.core  Exif .com.fluttercandies.flutter_image_compress.core  File .com.fluttercandies.flutter_image_compress.core  FormatRegister .com.fluttercandies.flutter_image_compress.core  Handler .com.fluttercandies.flutter_image_compress.core  ImageCompressPlugin .com.fluttercandies.flutter_image_compress.core  Int .com.fluttercandies.flutter_image_compress.core  	JvmStatic .com.fluttercandies.flutter_image_compress.core  List .com.fluttercandies.flutter_image_compress.core  Looper .com.fluttercandies.flutter_image_compress.core  
ResultHandler .com.fluttercandies.flutter_image_compress.core  String .com.fluttercandies.flutter_image_compress.core  Suppress .com.fluttercandies.flutter_image_compress.core  handler .com.fluttercandies.flutter_image_compress.core  log .com.fluttercandies.flutter_image_compress.core  outputStream .com.fluttercandies.flutter_image_compress.core  	readBytes .com.fluttercandies.flutter_image_compress.core  
threadPool .com.fluttercandies.flutter_image_compress.core  Any Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Boolean Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  ByteArrayOutputStream Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Context Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  	Exception Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Exif Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  File Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  FormatRegister Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  ImageCompressPlugin Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Int Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  List Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
MethodCall Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
MethodChannel Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  OutputStream Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  String Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Suppress Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  call Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getLOG Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getLog Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getOUTPUTStream Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getOutputStream Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getREADBytes Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  getReadBytes Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
getTHREADPool Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
getThreadPool Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  handle Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
handleGetFile Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  log Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  outputStream Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  	readBytes Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  reply Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  
threadPool Bcom.fluttercandies.flutter_image_compress.core.CompressFileHandler  Any Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Boolean Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  	ByteArray Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  ByteArrayOutputStream Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
CompressError Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Context Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  	Exception Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Exif Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  FormatRegister Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  ImageCompressPlugin Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Int Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  List Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
MethodCall Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
MethodChannel Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Suppress Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  call Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  getLOG Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  getLog Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
getTHREADPool Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
getThreadPool Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  handle Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  log Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  reply Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  
threadPool Bcom.fluttercandies.flutter_image_compress.core.CompressListHandler  Any <com.fluttercandies.flutter_image_compress.core.ResultHandler  Boolean <com.fluttercandies.flutter_image_compress.core.ResultHandler  	ByteArray <com.fluttercandies.flutter_image_compress.core.ResultHandler  ByteArrayOutputStream <com.fluttercandies.flutter_image_compress.core.ResultHandler  
CompressError <com.fluttercandies.flutter_image_compress.core.ResultHandler  Context <com.fluttercandies.flutter_image_compress.core.ResultHandler  	Exception <com.fluttercandies.flutter_image_compress.core.ResultHandler  ExecutorService <com.fluttercandies.flutter_image_compress.core.ResultHandler  	Executors <com.fluttercandies.flutter_image_compress.core.ResultHandler  Exif <com.fluttercandies.flutter_image_compress.core.ResultHandler  File <com.fluttercandies.flutter_image_compress.core.ResultHandler  FormatRegister <com.fluttercandies.flutter_image_compress.core.ResultHandler  Handler <com.fluttercandies.flutter_image_compress.core.ResultHandler  ImageCompressPlugin <com.fluttercandies.flutter_image_compress.core.ResultHandler  Int <com.fluttercandies.flutter_image_compress.core.ResultHandler  	JvmStatic <com.fluttercandies.flutter_image_compress.core.ResultHandler  List <com.fluttercandies.flutter_image_compress.core.ResultHandler  Looper <com.fluttercandies.flutter_image_compress.core.ResultHandler  
MethodCall <com.fluttercandies.flutter_image_compress.core.ResultHandler  
MethodChannel <com.fluttercandies.flutter_image_compress.core.ResultHandler  OutputStream <com.fluttercandies.flutter_image_compress.core.ResultHandler  String <com.fluttercandies.flutter_image_compress.core.ResultHandler  Suppress <com.fluttercandies.flutter_image_compress.core.ResultHandler  
getHANDLER <com.fluttercandies.flutter_image_compress.core.ResultHandler  
getHandler <com.fluttercandies.flutter_image_compress.core.ResultHandler  handle <com.fluttercandies.flutter_image_compress.core.ResultHandler  
handleGetFile <com.fluttercandies.flutter_image_compress.core.ResultHandler  handler <com.fluttercandies.flutter_image_compress.core.ResultHandler  isReply <com.fluttercandies.flutter_image_compress.core.ResultHandler  log <com.fluttercandies.flutter_image_compress.core.ResultHandler  outputStream <com.fluttercandies.flutter_image_compress.core.ResultHandler  	readBytes <com.fluttercandies.flutter_image_compress.core.ResultHandler  reply <com.fluttercandies.flutter_image_compress.core.ResultHandler  result <com.fluttercandies.flutter_image_compress.core.ResultHandler  
threadPool <com.fluttercandies.flutter_image_compress.core.ResultHandler  Any Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  ByteArrayOutputStream Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  ExecutorService Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  	Executors Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  Exif Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  File Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  FormatRegister Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  Handler Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  ImageCompressPlugin Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  	JvmStatic Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  Looper Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  
MethodChannel Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  String Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getLOG Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getLog Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getOUTPUTStream Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getOutputStream Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getREADBytes Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  getReadBytes Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  handler Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  log Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  outputStream Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  	readBytes Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  
threadPool Fcom.fluttercandies.flutter_image_compress.core.ResultHandler.Companion  
CompressError 3com.fluttercandies.flutter_image_compress.exception  String 3com.fluttercandies.flutter_image_compress.exception  String Acom.fluttercandies.flutter_image_compress.exception.CompressError  message Acom.fluttercandies.flutter_image_compress.exception.CompressError  printStackTrace Acom.fluttercandies.flutter_image_compress.exception.CompressError  	ByteArray .com.fluttercandies.flutter_image_compress.exif  ByteArrayInputStream .com.fluttercandies.flutter_image_compress.exif  	Exception .com.fluttercandies.flutter_image_compress.exif  Exif .com.fluttercandies.flutter_image_compress.exif  
ExifInterface .com.fluttercandies.flutter_image_compress.exif  
ExifKeeper .com.fluttercandies.flutter_image_compress.exif  Int .com.fluttercandies.flutter_image_compress.exif  	ByteArray 3com.fluttercandies.flutter_image_compress.exif.Exif  ByteArrayInputStream 3com.fluttercandies.flutter_image_compress.exif.Exif  	Exception 3com.fluttercandies.flutter_image_compress.exif.Exif  
ExifInterface 3com.fluttercandies.flutter_image_compress.exif.Exif  File 3com.fluttercandies.flutter_image_compress.exif.Exif  Int 3com.fluttercandies.flutter_image_compress.exif.Exif  getFromExifInterface 3com.fluttercandies.flutter_image_compress.exif.Exif  getRotationDegrees 3com.fluttercandies.flutter_image_compress.exif.Exif  writeToOutputStream 9com.fluttercandies.flutter_image_compress.exif.ExifKeeper  Any -com.fluttercandies.flutter_image_compress.ext  Bitmap -com.fluttercandies.flutter_image_compress.ext  	ByteArray -com.fluttercandies.flutter_image_compress.ext  ByteArrayOutputStream -com.fluttercandies.flutter_image_compress.ext  Float -com.fluttercandies.flutter_image_compress.ext  Int -com.fluttercandies.flutter_image_compress.ext  Matrix -com.fluttercandies.flutter_image_compress.ext  	calcScale -com.fluttercandies.flutter_image_compress.ext  compress -com.fluttercandies.flutter_image_compress.ext  convertFormatIndexToFormat -com.fluttercandies.flutter_image_compress.ext  log -com.fluttercandies.flutter_image_compress.ext  max -com.fluttercandies.flutter_image_compress.ext  min -com.fluttercandies.flutter_image_compress.ext  println -com.fluttercandies.flutter_image_compress.ext  rotate -com.fluttercandies.flutter_image_compress.ext  FormatRegister 0com.fluttercandies.flutter_image_compress.format  Int 0com.fluttercandies.flutter_image_compress.format  SparseArray 0com.fluttercandies.flutter_image_compress.format  
FormatHandler ?com.fluttercandies.flutter_image_compress.format.FormatRegister  Int ?com.fluttercandies.flutter_image_compress.format.FormatRegister  SparseArray ?com.fluttercandies.flutter_image_compress.format.FormatRegister  
findFormat ?com.fluttercandies.flutter_image_compress.format.FormatRegister  	formatMap ?com.fluttercandies.flutter_image_compress.format.FormatRegister  registerFormat ?com.fluttercandies.flutter_image_compress.format.FormatRegister  Boolean 0com.fluttercandies.flutter_image_compress.handle  	ByteArray 0com.fluttercandies.flutter_image_compress.handle  
FormatHandler 0com.fluttercandies.flutter_image_compress.handle  Int 0com.fluttercandies.flutter_image_compress.handle  String 0com.fluttercandies.flutter_image_compress.handle  Boolean >com.fluttercandies.flutter_image_compress.handle.FormatHandler  	ByteArray >com.fluttercandies.flutter_image_compress.handle.FormatHandler  Context >com.fluttercandies.flutter_image_compress.handle.FormatHandler  Int >com.fluttercandies.flutter_image_compress.handle.FormatHandler  OutputStream >com.fluttercandies.flutter_image_compress.handle.FormatHandler  String >com.fluttercandies.flutter_image_compress.handle.FormatHandler  equals >com.fluttercandies.flutter_image_compress.handle.FormatHandler  handleByteArray >com.fluttercandies.flutter_image_compress.handle.FormatHandler  
handleFile >com.fluttercandies.flutter_image_compress.handle.FormatHandler  type >com.fluttercandies.flutter_image_compress.handle.FormatHandler  Bitmap 7com.fluttercandies.flutter_image_compress.handle.common  
BitmapFactory 7com.fluttercandies.flutter_image_compress.handle.common  Boolean 7com.fluttercandies.flutter_image_compress.handle.common  	ByteArray 7com.fluttercandies.flutter_image_compress.handle.common  ByteArrayOutputStream 7com.fluttercandies.flutter_image_compress.handle.common  
CommonHandler 7com.fluttercandies.flutter_image_compress.handle.common  
ExifKeeper 7com.fluttercandies.flutter_image_compress.handle.common  Int 7com.fluttercandies.flutter_image_compress.handle.common  OutOfMemoryError 7com.fluttercandies.flutter_image_compress.handle.common  String 7com.fluttercandies.flutter_image_compress.handle.common  Suppress 7com.fluttercandies.flutter_image_compress.handle.common  System 7com.fluttercandies.flutter_image_compress.handle.common  android 7com.fluttercandies.flutter_image_compress.handle.common  	calcScale 7com.fluttercandies.flutter_image_compress.handle.common  compress 7com.fluttercandies.flutter_image_compress.handle.common  count 7com.fluttercandies.flutter_image_compress.handle.common  log 7com.fluttercandies.flutter_image_compress.handle.common  rotate 7com.fluttercandies.flutter_image_compress.handle.common  Bitmap Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  
BitmapFactory Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  Boolean Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  	ByteArray Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  ByteArrayOutputStream Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  Context Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  
ExifKeeper Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  Int Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  OutOfMemoryError Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  OutputStream Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  String Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  Suppress Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  System Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  android Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  bitmapFormat Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  	calcScale Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  compress Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  count Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  
getANDROID Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  
getAndroid Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCALCScale Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCOMPRESS Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCOUNT Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCalcScale Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCompress Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getCount Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getLOG Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  getLog Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  	getROTATE Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  	getRotate Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  
handleFile Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  log Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  rotate Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  type Ecom.fluttercandies.flutter_image_compress.handle.common.CommonHandler  Bitmap 5com.fluttercandies.flutter_image_compress.handle.heif  
BitmapFactory 5com.fluttercandies.flutter_image_compress.handle.heif  Boolean 5com.fluttercandies.flutter_image_compress.handle.heif  Build 5com.fluttercandies.flutter_image_compress.handle.heif  	ByteArray 5com.fluttercandies.flutter_image_compress.handle.heif  HeifHandler 5com.fluttercandies.flutter_image_compress.handle.heif  
HeifWriter 5com.fluttercandies.flutter_image_compress.handle.heif  Int 5com.fluttercandies.flutter_image_compress.handle.heif  String 5com.fluttercandies.flutter_image_compress.handle.heif  Suppress 5com.fluttercandies.flutter_image_compress.handle.heif  TmpFileUtil 5com.fluttercandies.flutter_image_compress.handle.heif  	calcScale 5com.fluttercandies.flutter_image_compress.handle.heif  count 5com.fluttercandies.flutter_image_compress.handle.heif  log 5com.fluttercandies.flutter_image_compress.handle.heif  	readBytes 5com.fluttercandies.flutter_image_compress.handle.heif  rotate 5com.fluttercandies.flutter_image_compress.handle.heif  Bitmap Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  
BitmapFactory Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Boolean Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Build Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  	ByteArray Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Context Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  
HeifWriter Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Int Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  OutputStream Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  String Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Suppress Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  TmpFileUtil Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  	calcScale Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  compress Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  
convertToHeif Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  count Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getCALCScale Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getCOUNT Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getCalcScale Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getCount Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getLOG Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getLog Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getREADBytes Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  	getROTATE Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  getReadBytes Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  	getRotate Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  log Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  
makeOption Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  	readBytes Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  rotate Acom.fluttercandies.flutter_image_compress.handle.heif.HeifHandler  Any 0com.fluttercandies.flutter_image_compress.logger  Log 0com.fluttercandies.flutter_image_compress.logger  log 0com.fluttercandies.flutter_image_compress.logger  File .com.fluttercandies.flutter_image_compress.util  TmpFileUtil .com.fluttercandies.flutter_image_compress.util  UUID .com.fluttercandies.flutter_image_compress.util  Context :com.fluttercandies.flutter_image_compress.util.TmpFileUtil  File :com.fluttercandies.flutter_image_compress.util.TmpFileUtil  UUID :com.fluttercandies.flutter_image_compress.util.TmpFileUtil  
createTmpFile :com.fluttercandies.flutter_image_compress.util.TmpFileUtil  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ByteArrayInputStream java.io  ByteArrayOutputStream java.io  File java.io  FileOutputStream java.io  OutputStream java.io  close java.io.ByteArrayOutputStream  toByteArray java.io.ByteArrayOutputStream  write java.io.ByteArrayOutputStream  absolutePath java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getOUTPUTStream java.io.File  getOutputStream java.io.File  getREADBytes java.io.File  getReadBytes java.io.File  outputStream java.io.File  	readBytes java.io.File  setAbsolutePath java.io.File  close java.io.OutputStream  toByteArray java.io.OutputStream  write java.io.OutputStream  Bitmap 	java.lang  
BitmapFactory 	java.lang  Build 	java.lang  ByteArrayInputStream 	java.lang  ByteArrayOutputStream 	java.lang  
CommonHandler 	java.lang  CompressFileHandler 	java.lang  CompressListHandler 	java.lang  	Exception 	java.lang  	Executors 	java.lang  Exif 	java.lang  
ExifInterface 	java.lang  
ExifKeeper 	java.lang  File 	java.lang  FormatRegister 	java.lang  Handler 	java.lang  HeifHandler 	java.lang  
HeifWriter 	java.lang  ImageCompressPlugin 	java.lang  Log 	java.lang  Looper 	java.lang  Matrix 	java.lang  
MethodChannel 	java.lang  OutOfMemoryError 	java.lang  SparseArray 	java.lang  System 	java.lang  TmpFileUtil 	java.lang  UUID 	java.lang  android 	java.lang  	calcScale 	java.lang  compress 	java.lang  convertFormatIndexToFormat 	java.lang  count 	java.lang  handler 	java.lang  log 	java.lang  max 	java.lang  min 	java.lang  outputStream 	java.lang  println 	java.lang  	readBytes 	java.lang  rotate 	java.lang  showLog 	java.lang  
threadPool 	java.lang  String java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  gc java.lang.System  File 	java.util  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  ExecutorService java.util.concurrent  	Executors java.util.concurrent  execute $java.util.concurrent.ExecutorService  newFixedThreadPool java.util.concurrent.Executors  Any kotlin  Bitmap kotlin  
BitmapFactory kotlin  Boolean kotlin  Build kotlin  	ByteArray kotlin  ByteArrayInputStream kotlin  ByteArrayOutputStream kotlin  
CommonHandler kotlin  CompressFileHandler kotlin  CompressListHandler kotlin  	Exception kotlin  	Executors kotlin  Exif kotlin  
ExifInterface kotlin  
ExifKeeper kotlin  File kotlin  Float kotlin  FormatRegister kotlin  	Function0 kotlin  Handler kotlin  HeifHandler kotlin  
HeifWriter kotlin  ImageCompressPlugin kotlin  Int kotlin  	JvmStatic kotlin  Log kotlin  Looper kotlin  Matrix kotlin  
MethodChannel kotlin  Nothing kotlin  OutOfMemoryError kotlin  SparseArray kotlin  String kotlin  Suppress kotlin  System kotlin  TmpFileUtil kotlin  UUID kotlin  Unit kotlin  android kotlin  	calcScale kotlin  compress kotlin  convertFormatIndexToFormat kotlin  count kotlin  handler kotlin  log kotlin  max kotlin  min kotlin  outputStream kotlin  println kotlin  	readBytes kotlin  rotate kotlin  showLog kotlin  
threadPool kotlin  getCOUNT kotlin.ByteArray  getCount kotlin.ByteArray  Bitmap kotlin.annotation  
BitmapFactory kotlin.annotation  Build kotlin.annotation  ByteArrayInputStream kotlin.annotation  ByteArrayOutputStream kotlin.annotation  
CommonHandler kotlin.annotation  CompressFileHandler kotlin.annotation  CompressListHandler kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  Exif kotlin.annotation  
ExifInterface kotlin.annotation  
ExifKeeper kotlin.annotation  File kotlin.annotation  FormatRegister kotlin.annotation  Handler kotlin.annotation  HeifHandler kotlin.annotation  
HeifWriter kotlin.annotation  ImageCompressPlugin kotlin.annotation  	JvmStatic kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  Matrix kotlin.annotation  
MethodChannel kotlin.annotation  OutOfMemoryError kotlin.annotation  SparseArray kotlin.annotation  System kotlin.annotation  TmpFileUtil kotlin.annotation  UUID kotlin.annotation  android kotlin.annotation  	calcScale kotlin.annotation  compress kotlin.annotation  convertFormatIndexToFormat kotlin.annotation  count kotlin.annotation  handler kotlin.annotation  log kotlin.annotation  max kotlin.annotation  min kotlin.annotation  outputStream kotlin.annotation  println kotlin.annotation  	readBytes kotlin.annotation  rotate kotlin.annotation  showLog kotlin.annotation  
threadPool kotlin.annotation  Bitmap kotlin.collections  
BitmapFactory kotlin.collections  Build kotlin.collections  ByteArrayInputStream kotlin.collections  ByteArrayOutputStream kotlin.collections  
CommonHandler kotlin.collections  CompressFileHandler kotlin.collections  CompressListHandler kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  Exif kotlin.collections  
ExifInterface kotlin.collections  
ExifKeeper kotlin.collections  File kotlin.collections  FormatRegister kotlin.collections  Handler kotlin.collections  HeifHandler kotlin.collections  
HeifWriter kotlin.collections  ImageCompressPlugin kotlin.collections  	JvmStatic kotlin.collections  List kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Matrix kotlin.collections  
MethodChannel kotlin.collections  OutOfMemoryError kotlin.collections  SparseArray kotlin.collections  System kotlin.collections  TmpFileUtil kotlin.collections  UUID kotlin.collections  android kotlin.collections  	calcScale kotlin.collections  compress kotlin.collections  convertFormatIndexToFormat kotlin.collections  count kotlin.collections  handler kotlin.collections  log kotlin.collections  max kotlin.collections  min kotlin.collections  outputStream kotlin.collections  println kotlin.collections  	readBytes kotlin.collections  rotate kotlin.collections  showLog kotlin.collections  
threadPool kotlin.collections  Bitmap kotlin.comparisons  
BitmapFactory kotlin.comparisons  Build kotlin.comparisons  ByteArrayInputStream kotlin.comparisons  ByteArrayOutputStream kotlin.comparisons  
CommonHandler kotlin.comparisons  CompressFileHandler kotlin.comparisons  CompressListHandler kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  Exif kotlin.comparisons  
ExifInterface kotlin.comparisons  
ExifKeeper kotlin.comparisons  File kotlin.comparisons  FormatRegister kotlin.comparisons  Handler kotlin.comparisons  HeifHandler kotlin.comparisons  
HeifWriter kotlin.comparisons  ImageCompressPlugin kotlin.comparisons  	JvmStatic kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  Matrix kotlin.comparisons  
MethodChannel kotlin.comparisons  OutOfMemoryError kotlin.comparisons  SparseArray kotlin.comparisons  System kotlin.comparisons  TmpFileUtil kotlin.comparisons  UUID kotlin.comparisons  android kotlin.comparisons  	calcScale kotlin.comparisons  compress kotlin.comparisons  convertFormatIndexToFormat kotlin.comparisons  count kotlin.comparisons  handler kotlin.comparisons  log kotlin.comparisons  max kotlin.comparisons  min kotlin.comparisons  outputStream kotlin.comparisons  println kotlin.comparisons  	readBytes kotlin.comparisons  rotate kotlin.comparisons  showLog kotlin.comparisons  
threadPool kotlin.comparisons  Bitmap 	kotlin.io  
BitmapFactory 	kotlin.io  Build 	kotlin.io  ByteArrayInputStream 	kotlin.io  ByteArrayOutputStream 	kotlin.io  
CommonHandler 	kotlin.io  CompressFileHandler 	kotlin.io  CompressListHandler 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  Exif 	kotlin.io  
ExifInterface 	kotlin.io  
ExifKeeper 	kotlin.io  File 	kotlin.io  FormatRegister 	kotlin.io  Handler 	kotlin.io  HeifHandler 	kotlin.io  
HeifWriter 	kotlin.io  ImageCompressPlugin 	kotlin.io  	JvmStatic 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  Matrix 	kotlin.io  
MethodChannel 	kotlin.io  OutOfMemoryError 	kotlin.io  SparseArray 	kotlin.io  System 	kotlin.io  TmpFileUtil 	kotlin.io  UUID 	kotlin.io  android 	kotlin.io  	calcScale 	kotlin.io  compress 	kotlin.io  convertFormatIndexToFormat 	kotlin.io  count 	kotlin.io  handler 	kotlin.io  log 	kotlin.io  max 	kotlin.io  min 	kotlin.io  outputStream 	kotlin.io  println 	kotlin.io  	readBytes 	kotlin.io  rotate 	kotlin.io  showLog 	kotlin.io  
threadPool 	kotlin.io  Bitmap 
kotlin.jvm  
BitmapFactory 
kotlin.jvm  Build 
kotlin.jvm  ByteArrayInputStream 
kotlin.jvm  ByteArrayOutputStream 
kotlin.jvm  
CommonHandler 
kotlin.jvm  CompressFileHandler 
kotlin.jvm  CompressListHandler 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  Exif 
kotlin.jvm  
ExifInterface 
kotlin.jvm  
ExifKeeper 
kotlin.jvm  File 
kotlin.jvm  FormatRegister 
kotlin.jvm  Handler 
kotlin.jvm  HeifHandler 
kotlin.jvm  
HeifWriter 
kotlin.jvm  ImageCompressPlugin 
kotlin.jvm  	JvmStatic 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  Matrix 
kotlin.jvm  
MethodChannel 
kotlin.jvm  OutOfMemoryError 
kotlin.jvm  SparseArray 
kotlin.jvm  System 
kotlin.jvm  TmpFileUtil 
kotlin.jvm  UUID 
kotlin.jvm  android 
kotlin.jvm  	calcScale 
kotlin.jvm  compress 
kotlin.jvm  convertFormatIndexToFormat 
kotlin.jvm  count 
kotlin.jvm  handler 
kotlin.jvm  log 
kotlin.jvm  max 
kotlin.jvm  min 
kotlin.jvm  outputStream 
kotlin.jvm  println 
kotlin.jvm  	readBytes 
kotlin.jvm  rotate 
kotlin.jvm  showLog 
kotlin.jvm  
threadPool 
kotlin.jvm  max kotlin.math  min kotlin.math  Bitmap 
kotlin.ranges  
BitmapFactory 
kotlin.ranges  Build 
kotlin.ranges  ByteArrayInputStream 
kotlin.ranges  ByteArrayOutputStream 
kotlin.ranges  
CommonHandler 
kotlin.ranges  CompressFileHandler 
kotlin.ranges  CompressListHandler 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  Exif 
kotlin.ranges  
ExifInterface 
kotlin.ranges  
ExifKeeper 
kotlin.ranges  File 
kotlin.ranges  FormatRegister 
kotlin.ranges  Handler 
kotlin.ranges  HeifHandler 
kotlin.ranges  
HeifWriter 
kotlin.ranges  ImageCompressPlugin 
kotlin.ranges  	JvmStatic 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  Matrix 
kotlin.ranges  
MethodChannel 
kotlin.ranges  OutOfMemoryError 
kotlin.ranges  SparseArray 
kotlin.ranges  System 
kotlin.ranges  TmpFileUtil 
kotlin.ranges  UUID 
kotlin.ranges  android 
kotlin.ranges  	calcScale 
kotlin.ranges  compress 
kotlin.ranges  convertFormatIndexToFormat 
kotlin.ranges  count 
kotlin.ranges  handler 
kotlin.ranges  log 
kotlin.ranges  max 
kotlin.ranges  min 
kotlin.ranges  outputStream 
kotlin.ranges  println 
kotlin.ranges  	readBytes 
kotlin.ranges  rotate 
kotlin.ranges  showLog 
kotlin.ranges  
threadPool 
kotlin.ranges  Bitmap kotlin.sequences  
BitmapFactory kotlin.sequences  Build kotlin.sequences  ByteArrayInputStream kotlin.sequences  ByteArrayOutputStream kotlin.sequences  
CommonHandler kotlin.sequences  CompressFileHandler kotlin.sequences  CompressListHandler kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  Exif kotlin.sequences  
ExifInterface kotlin.sequences  
ExifKeeper kotlin.sequences  File kotlin.sequences  FormatRegister kotlin.sequences  Handler kotlin.sequences  HeifHandler kotlin.sequences  
HeifWriter kotlin.sequences  ImageCompressPlugin kotlin.sequences  	JvmStatic kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  Matrix kotlin.sequences  
MethodChannel kotlin.sequences  OutOfMemoryError kotlin.sequences  SparseArray kotlin.sequences  System kotlin.sequences  TmpFileUtil kotlin.sequences  UUID kotlin.sequences  android kotlin.sequences  	calcScale kotlin.sequences  compress kotlin.sequences  convertFormatIndexToFormat kotlin.sequences  count kotlin.sequences  handler kotlin.sequences  log kotlin.sequences  max kotlin.sequences  min kotlin.sequences  outputStream kotlin.sequences  println kotlin.sequences  	readBytes kotlin.sequences  rotate kotlin.sequences  showLog kotlin.sequences  
threadPool kotlin.sequences  Bitmap kotlin.text  
BitmapFactory kotlin.text  Build kotlin.text  ByteArrayInputStream kotlin.text  ByteArrayOutputStream kotlin.text  
CommonHandler kotlin.text  CompressFileHandler kotlin.text  CompressListHandler kotlin.text  	Exception kotlin.text  	Executors kotlin.text  Exif kotlin.text  
ExifInterface kotlin.text  
ExifKeeper kotlin.text  File kotlin.text  FormatRegister kotlin.text  Handler kotlin.text  HeifHandler kotlin.text  
HeifWriter kotlin.text  ImageCompressPlugin kotlin.text  	JvmStatic kotlin.text  Log kotlin.text  Looper kotlin.text  Matrix kotlin.text  
MethodChannel kotlin.text  OutOfMemoryError kotlin.text  SparseArray kotlin.text  System kotlin.text  TmpFileUtil kotlin.text  UUID kotlin.text  android kotlin.text  	calcScale kotlin.text  compress kotlin.text  convertFormatIndexToFormat kotlin.text  count kotlin.text  handler kotlin.text  log kotlin.text  max kotlin.text  min kotlin.text  outputStream kotlin.text  println kotlin.text  	readBytes kotlin.text  rotate kotlin.text  showLog kotlin.text  
threadPool kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           