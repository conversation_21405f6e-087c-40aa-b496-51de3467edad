import '../../domain/entities/subscription_plan.dart';

/// Subscription plan model
class SubscriptionPlanModel extends SubscriptionPlan {
  /// Constructor
  const SubscriptionPlanModel({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currencyCode,
    required super.durationMonths,
    required super.features,
    super.isPopular = false,
    required super.productId,
    super.discountPercentage = 0,
    required super.originalPrice,
    super.hasFreeTrial = false,
    super.freeTrialDays = 0,
    required super.maxBranches,
    required super.maxTeamMembers,
    required super.maxClients,
    super.includesFinancialReports = false,
    super.includesAdvancedAnalytics = false,
    super.includesPrioritySupport = false,
    super.includesCustomBranding = false,
    super.includesApiAccess = false,
    super.includesOfflineMode = false,
    super.includesDataExport = false,
    super.includesMultiDeviceSync = false,
    super.includesAppointmentScheduling = false,
    super.includesClientManagement = false,
    super.includesInventoryManagement = false,
    super.includesMarketingTools = false,
    super.includesSmsNotifications = false,
    super.includesEmailMarketing = false,
    super.includesWebsiteIntegration = false,
    super.includesOnlineBooking = false,
    super.includesPosIntegration = false,
    super.includesLoyaltyProgram = false,
    super.includesGiftCards = false,
    super.includesCustomerFeedback = false,
    super.includesStaffManagement = false,
    super.includesPayrollManagement = false,
    super.includesCommissionTracking = false,
    super.includesExpenseTracking = false,
    super.includesTaxReporting = false,
    super.includesMultiLocationSupport = false,
    super.includesCustomRolesPermissions = false,
    super.includesWhiteLabelApp = false,
  });

  /// Create a model from a JSON map
  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      currencyCode: json['currency_code'],
      durationMonths: json['duration_months'],
      features: List<String>.from(json['features']),
      isPopular: json['is_popular'] ?? false,
      productId: json['product_id'],
      discountPercentage: json['discount_percentage']?.toDouble() ?? 0,
      originalPrice: json['original_price'].toDouble(),
      hasFreeTrial: json['has_free_trial'] ?? false,
      freeTrialDays: json['free_trial_days'] ?? 0,
      maxBranches: json['max_branches'],
      maxTeamMembers: json['max_team_members'],
      maxClients: json['max_clients'],
      includesFinancialReports: json['includes_financial_reports'] ?? false,
      includesAdvancedAnalytics: json['includes_advanced_analytics'] ?? false,
      includesPrioritySupport: json['includes_priority_support'] ?? false,
      includesCustomBranding: json['includes_custom_branding'] ?? false,
      includesApiAccess: json['includes_api_access'] ?? false,
      includesOfflineMode: json['includes_offline_mode'] ?? false,
      includesDataExport: json['includes_data_export'] ?? false,
      includesMultiDeviceSync: json['includes_multi_device_sync'] ?? false,
      includesAppointmentScheduling: json['includes_appointment_scheduling'] ?? false,
      includesClientManagement: json['includes_client_management'] ?? false,
      includesInventoryManagement: json['includes_inventory_management'] ?? false,
      includesMarketingTools: json['includes_marketing_tools'] ?? false,
      includesSmsNotifications: json['includes_sms_notifications'] ?? false,
      includesEmailMarketing: json['includes_email_marketing'] ?? false,
      includesWebsiteIntegration: json['includes_website_integration'] ?? false,
      includesOnlineBooking: json['includes_online_booking'] ?? false,
      includesPosIntegration: json['includes_pos_integration'] ?? false,
      includesLoyaltyProgram: json['includes_loyalty_program'] ?? false,
      includesGiftCards: json['includes_gift_cards'] ?? false,
      includesCustomerFeedback: json['includes_customer_feedback'] ?? false,
      includesStaffManagement: json['includes_staff_management'] ?? false,
      includesPayrollManagement: json['includes_payroll_management'] ?? false,
      includesCommissionTracking: json['includes_commission_tracking'] ?? false,
      includesExpenseTracking: json['includes_expense_tracking'] ?? false,
      includesTaxReporting: json['includes_tax_reporting'] ?? false,
      includesMultiLocationSupport: json['includes_multi_location_support'] ?? false,
      includesCustomRolesPermissions: json['includes_custom_roles_permissions'] ?? false,
      includesWhiteLabelApp: json['includes_white_label_app'] ?? false,
    );
  }

  /// Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency_code': currencyCode,
      'duration_months': durationMonths,
      'features': features,
      'is_popular': isPopular,
      'product_id': productId,
      'discount_percentage': discountPercentage,
      'original_price': originalPrice,
      'has_free_trial': hasFreeTrial,
      'free_trial_days': freeTrialDays,
      'max_branches': maxBranches,
      'max_team_members': maxTeamMembers,
      'max_clients': maxClients,
      'includes_financial_reports': includesFinancialReports,
      'includes_advanced_analytics': includesAdvancedAnalytics,
      'includes_priority_support': includesPrioritySupport,
      'includes_custom_branding': includesCustomBranding,
      'includes_api_access': includesApiAccess,
      'includes_offline_mode': includesOfflineMode,
      'includes_data_export': includesDataExport,
      'includes_multi_device_sync': includesMultiDeviceSync,
      'includes_appointment_scheduling': includesAppointmentScheduling,
      'includes_client_management': includesClientManagement,
      'includes_inventory_management': includesInventoryManagement,
      'includes_marketing_tools': includesMarketingTools,
      'includes_sms_notifications': includesSmsNotifications,
      'includes_email_marketing': includesEmailMarketing,
      'includes_website_integration': includesWebsiteIntegration,
      'includes_online_booking': includesOnlineBooking,
      'includes_pos_integration': includesPosIntegration,
      'includes_loyalty_program': includesLoyaltyProgram,
      'includes_gift_cards': includesGiftCards,
      'includes_customer_feedback': includesCustomerFeedback,
      'includes_staff_management': includesStaffManagement,
      'includes_payroll_management': includesPayrollManagement,
      'includes_commission_tracking': includesCommissionTracking,
      'includes_expense_tracking': includesExpenseTracking,
      'includes_tax_reporting': includesTaxReporting,
      'includes_multi_location_support': includesMultiLocationSupport,
      'includes_custom_roles_permissions': includesCustomRolesPermissions,
      'includes_white_label_app': includesWhiteLabelApp,
    };
  }
}
