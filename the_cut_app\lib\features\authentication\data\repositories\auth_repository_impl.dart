import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';

/// Authentication repository implementation
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;

  AuthRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<AuthFailure, UserEntity?>> getCurrentUser() async {
    try {
      final user = await _remoteDataSource.getCurrentUser();
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Stream<UserEntity?> get authStateChanges {
    return _remoteDataSource.authStateChanges;
  }

  @override
  Future<Either<AuthFailure, UserEntity>> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final user = await _remoteDataSource.signInWithEmailAndPassword(
        email,
        password,
      );
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    try {
      final user = await _remoteDataSource.registerWithEmailAndPassword(
        email,
        password,
        name,
      );
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> signOut() async {
    try {
      await _remoteDataSource.signOut();
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> sendPasswordResetEmail(String email) async {
    try {
      await _remoteDataSource.sendPasswordResetEmail(email);
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  }) async {
    try {
      final user = await _remoteDataSource.updateProfile(
        displayName: displayName,
        photoURL: photoURL,
        phoneNumber: phoneNumber,
      );
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> updateEmail(String email) async {
    try {
      await _remoteDataSource.updateEmail(email);
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> updatePassword(String password) async {
    try {
      await _remoteDataSource.updatePassword(password);
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, void>> deleteAccount() async {
    try {
      await _remoteDataSource.deleteAccount();
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> updateUserRole(
    String userId,
    List<String> roles,
  ) async {
    try {
      final user = await _remoteDataSource.updateUserRole(userId, roles);
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> updateUserBranch(
    String userId,
    String branchId,
  ) async {
    try {
      final user = await _remoteDataSource.updateUserBranch(userId, branchId);
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, List<UserEntity>>> getAllUsers() async {
    try {
      final users = await _remoteDataSource.getAllUsers();
      return Right(users);
    } on AuthException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<AuthFailure, UserEntity>> getUserById(String userId) async {
    try {
      final user = await _remoteDataSource.getUserById(userId);
      return Right(user);
    } on NotFoundException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(AuthFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(AuthFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }
}
