import 'package:equatable/equatable.dart';

/// Transaction type
enum TransactionType {
  /// Income transaction
  income,

  /// Expense transaction
  expense,
}

/// Transaction category
enum TransactionCategory {
  /// Sales transaction
  sales,

  /// Services transaction
  services,

  /// Salary transaction
  salary,

  /// Rent transaction
  rent,

  /// Utilities transaction
  utilities,

  /// Supplies transaction
  supplies,

  /// Equipment transaction
  equipment,

  /// Marketing transaction
  marketing,

  /// Taxes transaction
  taxes,

  /// Insurance transaction
  insurance,

  /// Other transaction
  other,
}

/// Transaction entity
class TransactionEntity extends Equatable {
  /// Transaction ID
  final String id;

  /// Transaction title
  final String title;

  /// Transaction description
  final String? description;

  /// Transaction amount
  final double amount;

  /// Transaction type
  final TransactionType type;

  /// Transaction category
  final TransactionCategory category;

  /// Transaction date
  final DateTime date;

  /// Transaction created by user ID
  final String createdById;

  /// Transaction created by user name
  final String createdByName;

  /// Transaction branch ID
  final String? branchId;

  /// Transaction branch name
  final String? branchName;

  /// Transaction created at
  final DateTime createdAt;

  /// Transaction updated at
  final DateTime? updatedAt;

  /// Transaction attachments
  final List<String>? attachments;

  /// Constructor
  const TransactionEntity({
    required this.id,
    required this.title,
    this.description,
    required this.amount,
    required this.type,
    required this.category,
    required this.date,
    required this.createdById,
    required this.createdByName,
    this.branchId,
    this.branchName,
    required this.createdAt,
    this.updatedAt,
    this.attachments,
  });

  /// Copy with
  TransactionEntity copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    TransactionType? type,
    TransactionCategory? category,
    DateTime? date,
    String? createdById,
    String? createdByName,
    String? branchId,
    String? branchName,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? attachments,
  }) {
    return TransactionEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      date: date ?? this.date,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        amount,
        type,
        category,
        date,
        createdById,
        createdByName,
        branchId,
        branchName,
        createdAt,
        updatedAt,
        attachments,
      ];
}
