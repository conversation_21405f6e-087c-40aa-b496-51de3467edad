{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5575", "endColumns": "128", "endOffsets": "5699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "82,429", "startColumns": "4,4", "startOffsets": "7891,39350", "endColumns": "60,74", "endOffsets": "7947,39420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "25734", "endColumns": "79", "endOffsets": "25809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2785,2877,2955,3009,3062,3128,3198,3276,3362,3442,3514,3592,3661,3730,3828,3910,3998,4091,4185,4259,4328,4423,4475,4558,4626,4711,4799,4861,4925,4988,5058,5158,5254,5351,5444,5502,5559", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2780,2872,2950,3004,3057,3123,3193,3271,3357,3437,3509,3587,3656,3725,3823,3905,3993,4086,4180,4254,4323,4418,4470,4553,4621,4706,4794,4856,4920,4983,5053,5153,5249,5346,5439,5497,5554,5631"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,7265,7493,7952,8192,8252,8339,8405,8470,8531,8595,8656,8710,8811,8872,8932,8986,9056,9167,9254,9335,9478,9557,9639,9771,9863,9941,9995,10048,10114,10184,10262,10348,10428,10500,10578,10647,10716,10814,10896,10984,11077,11171,11245,11314,11409,11461,11544,11612,11697,11785,11847,11911,11974,12044,12144,12240,12337,12430,12488,12925", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,7330,7578,8017,8247,8334,8400,8465,8526,8590,8651,8705,8806,8867,8927,8981,9051,9162,9249,9330,9473,9552,9634,9766,9858,9936,9990,10043,10109,10179,10257,10343,10423,10495,10573,10642,10711,10809,10891,10979,11072,11166,11240,11309,11404,11456,11539,11607,11692,11780,11842,11906,11969,12039,12139,12235,12332,12425,12483,12540,12997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6799,7583,7682,7793", "endColumns": "102,98,110,97", "endOffsets": "6897,7677,7788,7886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "168,171,174,176,177,178,185,186,188,189,190,191,192,193,194,196,197,200,211,212,224,226,227,255,256,266,276,277,279,286,287,297,298,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14568,14825,15111,15272,15357,15424,15938,16003,16137,16202,16275,16347,16416,16477,16546,16669,16736,16984,17931,17996,19025,19175,19253,22742,22858,23847,24541,24588,24709,25402,25461,26247,26336,27018,27107", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "14628,14902,15174,15352,15419,15480,15998,16060,16197,16270,16342,16411,16472,16541,16605,16731,16795,17070,17991,18090,19096,19248,19333,22853,22923,23892,24583,24651,24768,25456,25533,26331,26416,27102,27187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4711,4858,4981,5088,5224,5348,5467,5704,5848,5953,6100,6222,6362,6513,6577,6645", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4706,4853,4976,5083,5219,5343,5462,5570,5843,5948,6095,6217,6357,6508,6572,6640,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,199,324,383,468,522,579,662,762,923,1164,1420,1494,1564,1634,1725,1842,1967,2038,2109,2286,2358,2420,2493,2568,2690,2800,2900,2986,3063,3146,3211", "endColumns": "68,74,124,58,84,53,56,82,99,160,240,255,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,99,85,76,82,64,137", "endOffsets": "119,194,319,378,463,517,574,657,757,918,1159,1415,1489,1559,1629,1720,1837,1962,2033,2104,2281,2353,2415,2488,2563,2685,2795,2895,2981,3058,3141,3206,3344"}, "to": {"startLines": "161,163,299,316,370,381,382,383,384,385,386,387,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14056,14208,26421,28005,33327,34791,34845,34902,34985,35085,35246,35487,36748,36822,36892,36962,37053,37170,37295,37366,37516,37693,37765,37827,37900,37975,38097,38207,38307,38393,38470,38553,38618", "endColumns": "68,74,124,58,84,53,56,82,99,160,240,255,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,99,85,76,82,64,137", "endOffsets": "14120,14278,26541,28059,33407,34840,34897,34980,35080,35241,35482,35738,36817,36887,36957,37048,37165,37290,37361,37432,37688,37760,37822,37895,37970,38092,38202,38302,38388,38465,38548,38613,38751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "69,76,144,148,423,427,428", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6729,7335,12714,13002,38756,39192,39272", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6794,7416,12789,13136,38919,39267,39345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,424,425,426", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4456,6902,6999,7177,8022,8107,12545,12631,12794,12859,13141,13227,13398,38924,39002,39069", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4451,4528,6994,7095,7260,8102,8187,12626,12709,12854,12920,13222,13311,13466,38997,39064,39187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4533,7100,7421", "endColumns": "71,76,71", "endOffsets": "4600,7172,7488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3276,3342,3916,4469,4544,4650,4744,4799,4886,4970,5036,5120,5203,5260,5334,5383,5466,5564,5633,5704,5749,5824,5918,5968,6014,6084,6142,6207,6374,6527,6643,6708,6786,6856,6943,7020,7100,7169,7263,7333,7413,7499,7549,7659,7707,7764,7837,7899,7967,8033,8105,8188,8252", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,79,85,49,109,47,56,72,61,67,65,71,82,63,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3271,3337,3911,4464,4539,4645,4739,4794,4881,4965,5031,5115,5198,5255,5329,5378,5461,5559,5628,5699,5744,5819,5913,5963,6009,6079,6137,6202,6369,6522,6638,6703,6781,6851,6938,7015,7095,7164,7258,7328,7408,7494,7544,7654,7702,7759,7832,7894,7962,8028,8100,8183,8247,8326"}, "to": {"startLines": "154,155,156,157,158,159,160,164,165,166,167,170,172,173,175,180,184,199,202,203,204,206,207,208,210,214,215,216,217,218,219,220,221,222,223,225,228,231,232,233,242,243,244,245,246,247,248,249,250,251,252,253,260,261,262,263,264,267,272,273,274,275,280,281,282,283,284,285,289,290,300,301,303,304,308,309,311,321,322,371,372,373,374,378,389,390,391,392,393,394,409", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13572,13641,13719,13781,13849,13926,13988,14283,14338,14413,14489,14715,14907,15017,15179,15545,15846,16878,17149,17251,17322,17487,17581,17669,17844,18166,18265,18343,18449,18519,18608,18694,18777,18858,18957,19101,19338,19814,19882,19948,21247,21800,21875,21981,22075,22130,22217,22301,22367,22451,22534,22591,23169,23218,23301,23399,23468,23897,24276,24351,24445,24495,24773,24843,24901,24966,25133,25286,25591,25656,26546,26616,26777,26854,27192,27261,27423,28375,28455,33412,33462,33572,33620,34060,35820,35882,35950,36016,36088,36171,37437", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,79,85,49,109,47,56,72,61,67,65,71,82,63,78", "endOffsets": "13636,13714,13776,13844,13921,13983,14051,14333,14408,14484,14563,14820,15012,15106,15267,15628,15933,16979,17246,17317,17416,17576,17664,17780,17926,18260,18338,18444,18514,18603,18689,18772,18853,18952,19020,19170,19434,19877,19943,20517,21795,21870,21976,22070,22125,22212,22296,22362,22446,22529,22586,22660,23213,23296,23394,23463,23534,23937,24346,24440,24490,24536,24838,24896,24961,25128,25281,25397,25651,25729,26611,26698,26849,26929,27256,27350,27488,28450,28536,33457,33567,33615,33672,34128,35877,35945,36011,36083,36166,36230,37511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,13471", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,13567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13316", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,430,479,547,636,707,919,979,1067,1144,1199,1263,1571,1639,1709,1762,1815,1892,2007,2103,2171,2248,2322,2406,2474,2570,2834,2908,2986,3045,3107,3168,3230,3325,3408,3527,3630,3703,3782,3885,4088,4300,4417,4471,5067,5129", "endColumns": "159,214,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,94,82,118,102,72,78,102,202,211,116,53,595,61,54", "endOffsets": "210,425,474,542,631,702,914,974,1062,1139,1194,1258,1566,1634,1704,1757,1810,1887,2002,2098,2166,2243,2317,2401,2469,2565,2829,2903,2981,3040,3102,3163,3225,3320,3403,3522,3625,3698,3777,3880,4083,4295,4412,4466,5062,5124,5179"}, "to": {"startLines": "229,230,234,235,236,237,238,239,240,254,257,259,265,270,271,278,288,292,293,294,295,296,302,305,310,312,313,314,315,317,319,320,323,328,337,353,354,355,356,357,369,375,376,377,379,380,395", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19439,19599,20522,20571,20639,20728,20799,21011,21071,22665,22928,23105,23539,24138,24206,24656,25538,25814,25891,26006,26102,26170,26703,26934,27355,27493,27589,27853,27927,28064,28252,28314,28541,29218,30298,31675,31794,31897,31970,32049,33124,33677,33889,34006,34133,34729,36235", "endColumns": "159,214,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,94,82,118,102,72,78,102,202,211,116,53,595,61,54", "endOffsets": "19594,19809,20566,20634,20723,20794,21006,21066,21154,22737,22978,23164,23842,24201,24271,24704,25586,25886,26001,26097,26165,26242,26772,27013,27418,27584,27848,27922,28000,28118,28309,28370,28598,29308,30376,31789,31892,31965,32044,32147,33322,33884,34001,34055,34724,34786,36285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,226,348,451,544,673,858,1051,1200,1288,1384,1477,1578,1724,1828,1925,2158,2273,2364,2422,2485,2568,2660,2764,2946,3013,3091,3177,3240,3314,3388,3457,3567,3658,3749,3819,3887,3947,4047,4153,4256,4379,4470,4539,4616,4700,4773,4878,4988", "endColumns": "82,87,121,102,92,128,184,192,148,87,95,92,100,145,103,96,232,114,90,57,62,82,91,103,181,66,77,85,62,73,73,68,109,90,90,69,67,59,99,105,102,122,90,68,76,83,72,104,109,85", "endOffsets": "133,221,343,446,539,668,853,1046,1195,1283,1379,1472,1573,1719,1823,1920,2153,2268,2359,2417,2480,2563,2655,2759,2941,3008,3086,3172,3235,3309,3383,3452,3562,3653,3744,3814,3882,3942,4042,4148,4251,4374,4465,4534,4611,4695,4768,4873,4983,5069"}, "to": {"startLines": "162,241,258,268,269,318,324,325,326,327,329,330,331,332,333,334,335,336,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,358,359,360,361,362,363,364,365,366,367,368,388,396,397,398,399,400", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14125,21159,22983,23942,24045,28123,28603,28788,28981,29130,29313,29409,29502,29603,29749,29853,29950,30183,30381,30472,30530,30593,30676,30768,30872,31054,31121,31199,31285,31348,31422,31496,31565,32152,32243,32334,32404,32472,32532,32632,32738,32841,32964,33055,35743,36290,36374,36447,36552,36662", "endColumns": "82,87,121,102,92,128,184,192,148,87,95,92,100,145,103,96,232,114,90,57,62,82,91,103,181,66,77,85,62,73,73,68,109,90,90,69,67,59,99,105,102,122,90,68,76,83,72,104,109,85", "endOffsets": "14203,21242,23100,24040,24133,28247,28783,28976,29125,29213,29404,29497,29598,29744,29848,29945,30178,30293,30467,30525,30588,30671,30763,30867,31049,31116,31194,31280,31343,31417,31491,31560,31670,32238,32329,32399,32467,32527,32627,32733,32836,32959,33050,33119,35815,36369,36442,36547,36657,36743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "169,179,181,182,183,187,195,198,201,205,209,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14633,15485,15633,15697,15782,16065,16610,16800,17075,17421,17785,18095", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "14710,15540,15692,15777,15841,16132,16664,16873,17144,17482,17839,18161"}}]}]}