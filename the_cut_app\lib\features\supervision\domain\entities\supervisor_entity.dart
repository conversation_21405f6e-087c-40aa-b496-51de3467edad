import 'package:equatable/equatable.dart';

/// نوع المشرف
enum SupervisorType {
  architect,        // مهندس معماري
  civil,           // مهندس مدني
  electrical,      // مهندس كهربائي
  mechanical,      // مهندس ميكانيكي
  projectManager,  // مدير مشروع
  consultant,      // استشاري
  contractor,      // مقاول
  other,          // أخرى
}

/// حالة المشرف
enum SupervisorStatus {
  active,         // نشط
  inactive,       // غير نشط
  suspended,      // معلق
  pending,        // في انتظار الموافقة
}

/// كيان المشرف
class SupervisorEntity extends Equatable {
  /// معرف المشرف
  final String id;

  /// الاسم الكامل
  final String fullName;

  /// البريد الإلكتروني
  final String email;

  /// رقم الهاتف
  final String? phoneNumber;

  /// نوع المشرف
  final SupervisorType type;

  /// التخصص
  final String? specialization;

  /// سنوات الخبرة
  final int? experienceYears;

  /// رقم الترخيص المهني
  final String? licenseNumber;

  /// الشركة أو المؤسسة
  final String? company;

  /// المنصب
  final String? position;

  /// صورة الملف الشخصي
  final String? profileImageUrl;

  /// السيرة الذاتية
  final String? bio;

  /// المهارات
  final List<String>? skills;

  /// الشهادات
  final List<String>? certifications;

  /// حالة المشرف
  final SupervisorStatus status;

  /// التقييم (من 1 إلى 5)
  final double? rating;

  /// عدد المشاريع المكتملة
  final int completedProjects;

  /// عدد المشاريع الحالية
  final int currentProjects;

  /// تاريخ التسجيل
  final DateTime registrationDate;

  /// تاريخ آخر تسجيل دخول
  final DateTime? lastLoginAt;

  /// هل تم التحقق من الحساب
  final bool isVerified;

  /// هل تم التحقق من البريد الإلكتروني
  final bool isEmailVerified;

  /// هل تم التحقق من رقم الهاتف
  final bool isPhoneVerified;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// Constructor
  const SupervisorEntity({
    required this.id,
    required this.fullName,
    required this.email,
    this.phoneNumber,
    required this.type,
    this.specialization,
    this.experienceYears,
    this.licenseNumber,
    this.company,
    this.position,
    this.profileImageUrl,
    this.bio,
    this.skills,
    this.certifications,
    this.status = SupervisorStatus.pending,
    this.rating,
    this.completedProjects = 0,
    this.currentProjects = 0,
    required this.registrationDate,
    this.lastLoginAt,
    this.isVerified = false,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة محدثة من المشرف
  SupervisorEntity copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    SupervisorType? type,
    String? specialization,
    int? experienceYears,
    String? licenseNumber,
    String? company,
    String? position,
    String? profileImageUrl,
    String? bio,
    List<String>? skills,
    List<String>? certifications,
    SupervisorStatus? status,
    double? rating,
    int? completedProjects,
    int? currentProjects,
    DateTime? registrationDate,
    DateTime? lastLoginAt,
    bool? isVerified,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return SupervisorEntity(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      type: type ?? this.type,
      specialization: specialization ?? this.specialization,
      experienceYears: experienceYears ?? this.experienceYears,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      company: company ?? this.company,
      position: position ?? this.position,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      skills: skills ?? this.skills,
      certifications: certifications ?? this.certifications,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      completedProjects: completedProjects ?? this.completedProjects,
      currentProjects: currentProjects ?? this.currentProjects,
      registrationDate: registrationDate ?? this.registrationDate,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isVerified: isVerified ?? this.isVerified,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// الحصول على الأحرف الأولى من الاسم
  String get initials {
    final names = fullName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'S';
  }

  /// التحقق من كون المشرف متاح للعمل
  bool get isAvailable {
    return status == SupervisorStatus.active && isVerified;
  }

  @override
  List<Object?> get props => [
        id,
        fullName,
        email,
        phoneNumber,
        type,
        specialization,
        experienceYears,
        licenseNumber,
        company,
        position,
        profileImageUrl,
        bio,
        skills,
        certifications,
        status,
        rating,
        completedProjects,
        currentProjects,
        registrationDate,
        lastLoginAt,
        isVerified,
        isEmailVerified,
        isPhoneVerified,
        updatedAt,
        metadata,
      ];
}

/// امتدادات مساعدة لنوع المشرف
extension SupervisorTypeExtension on SupervisorType {
  String get nameAr {
    switch (this) {
      case SupervisorType.architect:
        return 'مهندس معماري';
      case SupervisorType.civil:
        return 'مهندس مدني';
      case SupervisorType.electrical:
        return 'مهندس كهربائي';
      case SupervisorType.mechanical:
        return 'مهندس ميكانيكي';
      case SupervisorType.projectManager:
        return 'مدير مشروع';
      case SupervisorType.consultant:
        return 'استشاري';
      case SupervisorType.contractor:
        return 'مقاول';
      case SupervisorType.other:
        return 'أخرى';
    }
  }

  String get nameEn {
    switch (this) {
      case SupervisorType.architect:
        return 'Architect';
      case SupervisorType.civil:
        return 'Civil Engineer';
      case SupervisorType.electrical:
        return 'Electrical Engineer';
      case SupervisorType.mechanical:
        return 'Mechanical Engineer';
      case SupervisorType.projectManager:
        return 'Project Manager';
      case SupervisorType.consultant:
        return 'Consultant';
      case SupervisorType.contractor:
        return 'Contractor';
      case SupervisorType.other:
        return 'Other';
    }
  }

  String get iconName {
    switch (this) {
      case SupervisorType.architect:
        return 'architecture';
      case SupervisorType.civil:
        return 'engineering';
      case SupervisorType.electrical:
        return 'electrical_services';
      case SupervisorType.mechanical:
        return 'precision_manufacturing';
      case SupervisorType.projectManager:
        return 'manage_accounts';
      case SupervisorType.consultant:
        return 'support_agent';
      case SupervisorType.contractor:
        return 'construction';
      case SupervisorType.other:
        return 'person';
    }
  }
}

/// امتدادات مساعدة لحالة المشرف
extension SupervisorStatusExtension on SupervisorStatus {
  String get nameAr {
    switch (this) {
      case SupervisorStatus.active:
        return 'نشط';
      case SupervisorStatus.inactive:
        return 'غير نشط';
      case SupervisorStatus.suspended:
        return 'معلق';
      case SupervisorStatus.pending:
        return 'في انتظار الموافقة';
    }
  }

  String get nameEn {
    switch (this) {
      case SupervisorStatus.active:
        return 'Active';
      case SupervisorStatus.inactive:
        return 'Inactive';
      case SupervisorStatus.suspended:
        return 'Suspended';
      case SupervisorStatus.pending:
        return 'Pending';
    }
  }
}
