-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:1:1-4:12
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:1:1-4:12
	package
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:2:3-51
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:3:3-76
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml:3:20-74
uses-sdk
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\src\main\AndroidManifest.xml
