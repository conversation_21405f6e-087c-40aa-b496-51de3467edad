{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,209,337,405,504,562,618,704,802,980,1262,1560,1648,1721,1810,1907,2035,2180,2251,2322,2486,2551,2616,2689,2768,2913,3035,3145,3235,3323,3408,3484", "endColumns": "74,78,127,67,98,57,55,85,97,177,281,297,87,72,88,96,127,144,70,70,163,64,64,72,78,144,121,109,89,87,84,75,166", "endOffsets": "125,204,332,400,499,557,613,699,797,975,1257,1555,1643,1716,1805,1902,2030,2175,2246,2317,2481,2546,2611,2684,2763,2908,3030,3140,3230,3318,3403,3479,3646"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15337,15501,28579,30364,35947,37622,37680,37736,37822,37920,38098,38380,39741,39829,39902,39991,40088,40216,40361,40432,40575,40739,40804,40869,40942,41021,41166,41288,41398,41488,41576,41661,41737", "endColumns": "74,78,127,67,98,57,55,85,97,177,281,297,87,72,88,96,127,144,70,70,163,64,64,72,78,144,121,109,89,87,84,75,166", "endOffsets": "15407,15575,28702,30427,36041,37675,37731,37817,37915,38093,38375,38673,39824,39897,39986,40083,40211,40356,40427,40498,40734,40799,40864,40937,41016,41161,41283,41393,41483,41571,41656,41732,41899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4720,7513,7846", "endColumns": "77,82,77", "endOffsets": "4793,7591,7919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15974,16846,16991,17053,17134,17422,17957,18150,18407,18741,19093,19391", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "16062,16902,17048,17129,17193,17492,18013,18226,18477,18804,19150,19458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14296,14383,14487,14553,14620,14686,14768", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "14378,14482,14548,14615,14681,14763,14831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5834", "endColumns": "159", "endOffsets": "5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,7312,7410,7596,8482,8561,13225,13317,13485,13558,13858,13944,14118,42073,42155,42225", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4633,4715,7405,7508,7680,8556,8649,13312,13399,13553,13623,13939,14030,14190,42150,42220,42341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4798,4906,5069,5200,5308,5469,5602,5724,5994,6186,6295,6460,6592,6757,6914,6981,7050", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4901,5064,5195,5303,5464,5597,5719,5829,6181,6290,6455,6587,6752,6909,6976,7045,7130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "27812", "endColumns": "84", "endOffsets": "27892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,7685,7924,8401,8654,8717,8806,8870,8939,9002,9076,9140,9196,9314,9372,9434,9490,9570,9709,9798,9880,10021,10102,10182,10333,10423,10503,10559,10615,10681,10760,10842,10930,11019,11093,11170,11240,11319,11419,11503,11587,11679,11779,11853,11934,12036,12089,12174,12241,12334,12423,12485,12549,12612,12680,12793,12900,13004,13105,13165,13628", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,7745,8014,8477,8712,8801,8865,8934,8997,9071,9135,9191,9309,9367,9429,9485,9565,9704,9793,9875,10016,10097,10177,10328,10418,10498,10554,10610,10676,10755,10837,10925,11014,11088,11165,11235,11314,11414,11498,11582,11674,11774,11848,11929,12031,12084,12169,12236,12329,12418,12480,12544,12607,12675,12788,12895,12999,13100,13160,13220,13706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7135,7750,13404,13711,41904,42346,42434", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "7200,7841,13480,13853,42068,42429,42511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15907,16180,16474,16620,16713,16785,17282,17355,17497,17559,17627,17698,17758,17819,17893,18018,18087,18332,19237,19310,20386,20555,20641,24632,24749,25784,26491,26551,26707,27456,27526,28379,28490,29259,29363", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "15969,16268,16537,16708,16780,16841,17350,17417,17554,17622,17693,17753,17814,17888,17952,18082,18145,18402,19305,19386,20458,20636,20729,24744,24835,25829,26546,26634,26766,27521,27590,28485,28574,29358,29461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,14195", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,14291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,247,381,475,567,689,861,1050,1206,1305,1405,1502,1602,1803,1908,2009,2280,2395,2489,2550,2616,2697,2803,2908,3126,3194,3266,3350,3425,3519,3590,3658,3775,3863,3962,4039,4108,4169,4275,4386,4486,4625,4724,4795,4876,4966,5033,5150,5256", "endColumns": "88,102,133,93,91,121,171,188,155,98,99,96,99,200,104,100,270,114,93,60,65,80,105,104,217,67,71,83,74,93,70,67,116,87,98,76,68,60,105,110,99,138,98,70,80,89,66,116,105,84", "endOffsets": "139,242,376,470,562,684,856,1045,1201,1300,1400,1497,1597,1798,1903,2004,2275,2390,2484,2545,2611,2692,2798,2903,3121,3189,3261,3345,3420,3514,3585,3653,3770,3858,3957,4034,4103,4164,4270,4381,4481,4620,4719,4790,4871,4961,5028,5145,5251,5336"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15412,22748,24897,25882,25976,30513,31015,31187,31376,31532,31741,31841,31938,32038,32239,32344,32445,32716,32921,33015,33076,33142,33223,33329,33434,33652,33720,33792,33876,33951,34045,34116,34184,34745,34833,34932,35009,35078,35139,35245,35356,35456,35595,35694,38678,39276,39366,39433,39550,39656", "endColumns": "88,102,133,93,91,121,171,188,155,98,99,96,99,200,104,100,270,114,93,60,65,80,105,104,217,67,71,83,74,93,70,67,116,87,98,76,68,60,105,110,99,138,98,70,80,89,66,116,105,84", "endOffsets": "15496,22846,25026,25971,26063,30630,31182,31371,31527,31626,31836,31933,32033,32234,32339,32440,32711,32826,33010,33071,33137,33218,33324,33429,33647,33715,33787,33871,33946,34040,34111,34179,34296,34828,34927,35004,35073,35134,35240,35351,35451,35590,35689,35760,38754,39361,39428,39545,39651,39736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8340,42516", "endColumns": "60,77", "endOffsets": "8396,42589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3363,3431,4142,4833,4912,5042,5149,5204,5302,5394,5479,5589,5702,5761,5847,5898,5978,6088,6166,6239,6287,6368,6474,6520,6570,6641,6699,6763,6963,7110,7255,7327,7413,7497,7592,7682,7780,7858,7951,8032,8117,8214,8269,8389,8440,8502,8578,8648,8727,8797,8868,8962,9037", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,47,80,105,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,84,96,54,119,50,61,75,69,78,69,70,93,74,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3358,3426,4137,4828,4907,5037,5144,5199,5297,5389,5474,5584,5697,5756,5842,5893,5973,6083,6161,6234,6282,6363,6469,6515,6565,6636,6694,6758,6958,7105,7250,7322,7408,7492,7587,7677,7775,7853,7946,8027,8112,8209,8264,8384,8435,8497,8573,8643,8722,8792,8863,8957,9032,9104"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14836,14911,14995,15057,15133,15218,15278,15580,15638,15732,15813,16067,16273,16386,16542,16907,17198,18231,18482,18577,18649,18809,18897,18985,19155,19463,19555,19634,19733,19811,19909,20003,20094,20192,20310,20463,20734,21201,21277,21345,22851,23542,23621,23751,23858,23913,24011,24103,24188,24298,24411,24470,25095,25146,25226,25336,25414,25834,26208,26289,26395,26441,26771,26842,26900,26964,27164,27311,27654,27726,28707,28791,28968,29058,29466,29544,29707,30772,30857,36046,36101,36221,36272,36710,38759,38829,38908,38978,39049,39143,40503", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,47,80,105,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,84,96,54,119,50,61,75,69,78,69,70,93,74,71", "endOffsets": "14906,14990,15052,15128,15213,15273,15332,15633,15727,15808,15902,16175,16381,16469,16615,16986,17277,18327,18572,18644,18736,18892,18980,19088,19232,19550,19629,19728,19806,19904,19998,20089,20187,20305,20381,20550,20832,21272,21340,22051,23537,23616,23746,23853,23908,24006,24098,24183,24293,24406,24465,24551,25141,25221,25331,25409,25482,25877,26284,26390,26436,26486,26837,26895,26959,27159,27306,27451,27721,27807,28786,28881,29053,29151,29539,29632,29783,30852,30949,36096,36216,36267,36329,36781,38824,38903,38973,39044,39138,39213,40570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,419,465,537,640,715,956,1018,1111,1187,1244,1308,1605,1678,1745,1813,1872,1960,2095,2207,2265,2354,2436,2539,2609,2709,3030,3107,3185,3266,3341,3403,3464,3574,3664,3773,3867,3943,4022,4108,4290,4495,4604,4666,5437,5502", "endColumns": "157,205,45,71,102,74,240,61,92,75,56,63,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,109,89,108,93,75,78,85,181,204,108,61,770,64,57", "endOffsets": "208,414,460,532,635,710,951,1013,1106,1182,1239,1303,1600,1673,1740,1808,1867,1955,2090,2202,2260,2349,2431,2534,2604,2704,3025,3102,3180,3261,3336,3398,3459,3569,3659,3768,3862,3938,4017,4103,4285,4490,4599,4661,5432,5497,5555"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20837,20995,22056,22102,22174,22277,22352,22593,22655,24556,24840,25031,25487,26068,26141,26639,27595,27897,27985,28120,28232,28290,28886,29156,29637,29788,29888,30209,30286,30432,30635,30710,30954,31631,32831,34301,34410,34504,34580,34659,35765,36334,36539,36648,36786,37557,39218", "endColumns": "157,205,45,71,102,74,240,61,92,75,56,63,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,109,89,108,93,75,78,85,181,204,108,61,770,64,57", "endOffsets": "20990,21196,22097,22169,22272,22347,22588,22650,22743,24627,24892,25090,25779,26136,26203,26702,27649,27980,28115,28227,28285,28374,28963,29254,29702,29883,30204,30281,30359,30508,30705,30767,31010,31736,32916,34405,34499,34575,34654,34740,35942,36534,36643,36705,37552,37617,39271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7205,8019,8120,8235", "endColumns": "106,100,114,104", "endOffsets": "7307,8115,8230,8335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,14035", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14113"}}]}]}