import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/finance_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../domain/entities/transaction_entity.dart';

/// Finance screen
class FinanceScreen extends StatefulWidget {
  const FinanceScreen({super.key});

  @override
  State<FinanceScreen> createState() => _FinanceScreenState();
}

class _FinanceScreenState extends State<FinanceScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FinanceService>(context, listen: false).loadTransactions();
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final financeService = Provider.of<FinanceService>(context);
    final transactions = financeService.transactions;
    
    final incomeTransactions = transactions.where((transaction) => 
      transaction.type == TransactionType.income
    ).toList();
    
    final expenseTransactions = transactions.where((transaction) => 
      transaction.type == TransactionType.expense
    ).toList();
    
    final totalIncome = financeService.calculateTotalIncome();
    final totalExpenses = financeService.calculateTotalExpenses();
    final balance = financeService.calculateBalance();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Finance'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Income'),
            Tab(text: 'Expenses'),
          ],
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-transaction');
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
      body: financeService.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                // Summary card
                _buildSummaryCard(totalIncome, totalExpenses, balance),
                
                // Transactions
                Expanded(
                  child: transactions.isEmpty
                      ? _buildEmptyState()
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            // Income transactions
                            incomeTransactions.isEmpty
                                ? _buildEmptyTabState('No income transactions')
                                : _buildTransactionsList(incomeTransactions),
                            
                            // Expense transactions
                            expenseTransactions.isEmpty
                                ? _buildEmptyTabState('No expense transactions')
                                : _buildTransactionsList(expenseTransactions),
                          ],
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildSummaryCard(double totalIncome, double totalExpenses, double balance) {
    return Card(
      margin: EdgeInsets.all(16.w),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Text(
              'Financial Summary',
              style: AppTextStyles.h4,
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'Income',
                    amount: totalIncome,
                    color: AppColors.success,
                    icon: Icons.arrow_upward,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildSummaryItem(
                    title: 'Expenses',
                    amount: totalExpenses,
                    color: AppColors.error,
                    icon: Icons.arrow_downward,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Divider(height: 1, thickness: 1, color: AppColors.border),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Balance',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '\$${balance.toStringAsFixed(2)}',
                  style: AppTextStyles.h3.copyWith(
                    color: balance >= 0 ? AppColors.success : AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required double amount,
    required Color color,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: 4.h),
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 16.w,
                color: color,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '\$${amount.toStringAsFixed(2)}',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.attach_money_outlined,
              size: 80.w,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'No Transactions Yet',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Add your first transaction to get started',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: 'Add Transaction',
              icon: Icons.add,
              onPressed: () {
                Navigator.pushNamed(context, '/add-transaction');
              },
              width: 200.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyTabState(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.attach_money_outlined,
              size: 60.w,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              message,
              style: AppTextStyles.h4,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(List<TransactionEntity> transactions) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildTransactionCard(TransactionEntity transaction) {
    final Color typeColor = transaction.type == TransactionType.income
        ? AppColors.success
        : AppColors.error;
    
    final IconData typeIcon = transaction.type == TransactionType.income
        ? Icons.arrow_upward
        : Icons.arrow_downward;
    
    final String categoryText = _getCategoryText(transaction.category);
    
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            '/transaction-details',
            arguments: transaction.id,
          );
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and amount
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      transaction.title,
                      style: AppTextStyles.h4,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(4.w),
                        decoration: BoxDecoration(
                          color: typeColor.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          typeIcon,
                          size: 16.w,
                          color: typeColor,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '\$${transaction.amount.toStringAsFixed(2)}',
                        style: AppTextStyles.bodyLarge.copyWith(
                          fontWeight: FontWeight.bold,
                          color: typeColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              
              // Description
              if (transaction.description != null) ...[
                Text(
                  transaction.description!,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),
              ],
              
              // Category and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      categoryText,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 16.w,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatDate(transaction.date),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              
              // Created by and branch
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.person_outline,
                        size: 16.w,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        transaction.createdByName,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  
                  if (transaction.branchName != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.store_outlined,
                          size: 16.w,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          transaction.branchName!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCategoryText(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.sales:
        return 'Sales';
      case TransactionCategory.services:
        return 'Services';
      case TransactionCategory.salary:
        return 'Salary';
      case TransactionCategory.rent:
        return 'Rent';
      case TransactionCategory.utilities:
        return 'Utilities';
      case TransactionCategory.supplies:
        return 'Supplies';
      case TransactionCategory.equipment:
        return 'Equipment';
      case TransactionCategory.marketing:
        return 'Marketing';
      case TransactionCategory.taxes:
        return 'Taxes';
      case TransactionCategory.insurance:
        return 'Insurance';
      case TransactionCategory.other:
        return 'Other';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
