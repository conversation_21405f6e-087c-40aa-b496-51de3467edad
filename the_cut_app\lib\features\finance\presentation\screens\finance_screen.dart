import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';

/// شاشة إدارة المالية
class FinanceScreen extends StatefulWidget {
  const FinanceScreen({super.key});

  @override
  State<FinanceScreen> createState() => _FinanceScreenState();
}

class _FinanceScreenState extends State<FinanceScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  DateTime _selectedMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'إدارة المالية',
          style: AppTextStyles.h4.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        elevation: 2,
        shadowColor: AppColors.primary.withValues(alpha: 0.3),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: () => _showAddTransactionDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showFinancialReports(),
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          indicatorColor: AppColors.accent,
          isScrollable: true,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'الإيرادات'),
            Tab(text: 'المصروفات'),
            Tab(text: 'التقارير'),
          ],
        ),
      ),
      body: Column(
        children: [
          // ملخص مالي سريع
          _buildFinancialSummary(),
          
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTransactionsList(),
                _buildTransactionsList(),
                _buildTransactionsList(),
                _buildReportsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddTransactionDialog(),
        backgroundColor: AppColors.primary,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'إضافة معاملة',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildFinancialSummary() {
    final totalIncome = 25000.0;
    final totalExpenses = 18000.0;
    final netProfit = totalIncome - totalExpenses;
    final monthlyBudget = 50000.0;

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primaryLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'الملخص المالي - ${_getMonthName(_selectedMonth)}',
            style: AppTextStyles.h5.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي الإيرادات',
                  value: '${totalIncome.toStringAsFixed(0)} د.ك',
                  icon: Icons.trending_up,
                  color: AppColors.success,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي المصروفات',
                  value: '${totalExpenses.toStringAsFixed(0)} د.ك',
                  icon: Icons.trending_down,
                  color: AppColors.error,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'صافي الربح',
                  value: '${netProfit.toStringAsFixed(0)} د.ك',
                  icon: netProfit >= 0 ? Icons.attach_money : Icons.money_off,
                  color: netProfit >= 0 ? AppColors.success : AppColors.error,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildSummaryCard(
                  title: 'الميزانية المتبقية',
                  value: '${(monthlyBudget - totalExpenses).toStringAsFixed(0)} د.ك',
                  icon: Icons.account_balance_wallet,
                  color: AppColors.info,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24.w,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.h5.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64.w,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد معاملات مالية',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على زر "إضافة معاملة" لبدء تسجيل المعاملات المالية',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التقارير المالية',
                    style: AppTextStyles.h5.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  _buildReportItem(
                    title: 'تقرير الإيرادات الشهرية',
                    description: 'تفصيل جميع الإيرادات للشهر الحالي',
                    icon: Icons.trending_up,
                    onTap: () => _showFeatureComingSoon('تقرير الإيرادات'),
                  ),
                  _buildReportItem(
                    title: 'تقرير المصروفات الشهرية',
                    description: 'تفصيل جميع المصروفات للشهر الحالي',
                    icon: Icons.trending_down,
                    onTap: () => _showFeatureComingSoon('تقرير المصروفات'),
                  ),
                  _buildReportItem(
                    title: 'تقرير الربح والخسارة',
                    description: 'تحليل الأرباح والخسائر للمشاريع',
                    icon: Icons.analytics,
                    onTap: () => _showFeatureComingSoon('تقرير الربح والخسارة'),
                  ),
                  _buildReportItem(
                    title: 'تقرير التدفق النقدي',
                    description: 'تتبع التدفق النقدي الداخل والخارج',
                    icon: Icons.account_balance,
                    onTap: () => _showFeatureComingSoon('تقرير التدفق النقدي'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportItem({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
        size: 24.w,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        description,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16.w,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  void _showAddTransactionDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة معاملة مالية - قريباً')),
    );
  }

  void _showFinancialReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التقارير المالية - قريباً')),
    );
  }

  void _showSearchDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('البحث في المعاملات - قريباً')),
    );
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$feature - قريباً')),
    );
  }

  String _getMonthName(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[date.month - 1];
  }
}
