import 'package:equatable/equatable.dart';

/// User entity
class UserEntity extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? photoUrl;
  final String? phoneNumber;
  final bool isEmailVerified;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;
  final List<String>? roles;
  final String? branchId;
  final bool isActive;

  const UserEntity({
    required this.id,
    required this.email,
    this.name,
    this.photoUrl,
    this.phoneNumber,
    this.isEmailVerified = false,
    this.createdAt,
    this.lastLoginAt,
    this.roles,
    this.branchId,
    this.isActive = true,
  });

  /// Check if user is admin
  bool get isAdmin => roles?.contains('admin') ?? false;

  /// Check if user is manager
  bool get isManager => roles?.contains('manager') ?? false;

  /// Check if user is employee
  bool get isEmployee => roles?.contains('employee') ?? false;

  /// Create a copy of this user with the given fields replaced
  UserEntity copyWith({
    String? id,
    String? email,
    String? name,
    String? photoUrl,
    String? phoneNumber,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<String>? roles,
    String? branchId,
    bool? isActive,
  }) {
    return UserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      roles: roles ?? this.roles,
      branchId: branchId ?? this.branchId,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        photoUrl,
        phoneNumber,
        isEmailVerified,
        createdAt,
        lastLoginAt,
        roles,
        branchId,
        isActive,
      ];
}
