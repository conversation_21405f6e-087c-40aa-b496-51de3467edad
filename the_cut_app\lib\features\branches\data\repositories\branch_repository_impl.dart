import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/branch_entity.dart';
import '../../domain/repositories/branch_repository.dart';
import '../datasources/branch_remote_data_source.dart';
import '../models/branch_model.dart';

/// Branch repository implementation
class BranchRepositoryImpl implements BranchRepository {
  final BranchRemoteDataSource _remoteDataSource;

  BranchRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, List<BranchEntity>>> getAllBranches() async {
    try {
      final branches = await _remoteDataSource.getAllBranches();
      return Right(branches);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, BranchEntity>> getBranchById(String branchId) async {
    try {
      final branch = await _remoteDataSource.getBranchById(branchId);
      return Right(branch);
    } on NotFoundException catch (e) {
      return Left(NotFoundFailure(
        message: e.message,
        code: e.code,
      ));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, BranchEntity>> createBranch(BranchEntity branch) async {
    try {
      final branchModel = BranchModel(
        id: branch.id,
        name: branch.name,
        address: branch.address,
        phoneNumber: branch.phoneNumber,
        email: branch.email,
        managerId: branch.managerId,
        managerName: branch.managerName,
        logoUrl: branch.logoUrl,
        isActive: branch.isActive,
        createdAt: branch.createdAt,
        updatedAt: branch.updatedAt,
        metadata: branch.metadata,
      );
      
      final createdBranch = await _remoteDataSource.createBranch(branchModel);
      return Right(createdBranch);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, BranchEntity>> updateBranch(BranchEntity branch) async {
    try {
      final branchModel = BranchModel(
        id: branch.id,
        name: branch.name,
        address: branch.address,
        phoneNumber: branch.phoneNumber,
        email: branch.email,
        managerId: branch.managerId,
        managerName: branch.managerName,
        logoUrl: branch.logoUrl,
        isActive: branch.isActive,
        createdAt: branch.createdAt,
        updatedAt: branch.updatedAt,
        metadata: branch.metadata,
      );
      
      final updatedBranch = await _remoteDataSource.updateBranch(branchModel);
      return Right(updatedBranch);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBranch(String branchId) async {
    try {
      await _remoteDataSource.deleteBranch(branchId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, List<BranchEntity>>> getBranchesByManagerId(String managerId) async {
    try {
      final branches = await _remoteDataSource.getBranchesByManagerId(managerId);
      return Right(branches);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }

  @override
  Future<Either<Failure, List<BranchEntity>>> getActiveBranches() async {
    try {
      final branches = await _remoteDataSource.getActiveBranches();
      return Right(branches);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        code: e.code,
      ));
    } catch (e) {
      return Left(ServerFailure(
        message: e.toString(),
        code: 500,
      ));
    }
  }
}
