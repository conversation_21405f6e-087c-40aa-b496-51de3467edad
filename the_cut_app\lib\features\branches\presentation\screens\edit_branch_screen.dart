import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Edit branch screen
class EditBranchScreen extends StatefulWidget {
  /// Branch ID
  final String branchId;

  /// Constructor
  const EditBranchScreen({
    super.key,
    required this.branchId,
  });

  @override
  State<EditBranchScreen> createState() => _EditBranchScreenState();
}

class _EditBranchScreenState extends State<EditBranchScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _managerNameController;
  bool _isActive = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _addressController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _managerNameController = TextEditingController();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBranch();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _managerNameController.dispose();
    super.dispose();
  }

  Future<void> _loadBranch() async {
    final branchService = Provider.of<BranchService>(context, listen: false);
    final branch = await branchService.getBranchById(widget.branchId);
    
    if (branch != null && mounted) {
      setState(() {
        _nameController.text = branch.name;
        _addressController.text = branch.address ?? '';
        _phoneController.text = branch.phoneNumber ?? '';
        _emailController.text = branch.email ?? '';
        _managerNameController.text = branch.managerName ?? '';
        _isActive = branch.isActive;
        _isLoading = false;
      });
    } else if (mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Branch not found'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _updateBranch() async {
    if (_formKey.currentState!.validate()) {
      final branchService = Provider.of<BranchService>(context, listen: false);
      final currentBranch = branchService.selectedBranch;
      
      if (currentBranch == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Branch not found'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final updatedBranch = currentBranch.copyWith(
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        managerName: _managerNameController.text.trim(),
        isActive: _isActive,
        updatedAt: DateTime.now(),
      );
      
      final result = await branchService.updateBranch(updatedBranch);
      
      if (result != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Branch updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(branchService.error ?? 'Failed to update branch'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final branchService = Provider.of<BranchService>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Branch'),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SafeArea(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Form title
                      Text(
                        'Branch Information',
                        style: AppTextStyles.h4,
                      ),
                      SizedBox(height: 24.h),
                      
                      // Branch name
                      CustomTextField(
                        controller: _nameController,
                        hintText: 'Branch Name',
                        labelText: 'Branch Name',
                        prefixIcon: Icons.store_outlined,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter branch name';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch address
                      CustomTextField(
                        controller: _addressController,
                        hintText: 'Address',
                        labelText: 'Address',
                        prefixIcon: Icons.location_on_outlined,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter branch address';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch phone
                      CustomTextField(
                        controller: _phoneController,
                        hintText: 'Phone Number',
                        labelText: 'Phone Number',
                        prefixIcon: Icons.phone_outlined,
                        keyboardType: TextInputType.phone,
                        textInputAction: TextInputAction.next,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch email
                      CustomTextField(
                        controller: _emailController,
                        hintText: 'Email',
                        labelText: 'Email',
                        prefixIcon: Icons.email_outlined,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Please enter a valid email';
                            }
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch manager
                      CustomTextField(
                        controller: _managerNameController,
                        hintText: 'Manager Name',
                        labelText: 'Manager Name',
                        prefixIcon: Icons.person_outline,
                        textInputAction: TextInputAction.done,
                      ),
                      SizedBox(height: 24.h),
                      
                      // Branch status
                      Row(
                        children: [
                          Text(
                            'Branch Status:',
                            style: AppTextStyles.bodyMedium,
                          ),
                          SizedBox(width: 16.w),
                          Switch(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value;
                              });
                            },
                            activeColor: AppColors.primary,
                          ),
                          Text(
                            _isActive ? 'Active' : 'Inactive',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: _isActive ? AppColors.success : AppColors.error,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 32.h),
                      
                      // Update button
                      CustomButton(
                        text: 'Update Branch',
                        isLoading: branchService.isLoading,
                        onPressed: _updateBranch,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Cancel button
                      CustomButton(
                        text: 'Cancel',
                        color: Colors.grey.shade200,
                        textColor: AppColors.textPrimary,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
