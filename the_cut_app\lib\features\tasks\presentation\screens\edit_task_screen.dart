import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/services/task_service.dart';
import '../../../../core/services/team_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/task_entity.dart';

/// Edit task screen
class EditTaskScreen extends StatefulWidget {
  /// Task ID
  final String taskId;

  /// Constructor
  const EditTaskScreen({
    super.key,
    required this.taskId,
  });

  @override
  State<EditTaskScreen> createState() => _EditTaskScreenState();
}

class _EditTaskScreenState extends State<EditTaskScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  DateTime? _dueDate;
  TaskPriority _priority = TaskPriority.medium;
  TaskStatus _status = TaskStatus.pending;
  String? _selectedBranchId;
  String? _selectedBranchName;
  String? _selectedAssigneeId;
  String? _selectedAssigneeName;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTask();
      Provider.of<BranchService>(context, listen: false).loadBranches();
      Provider.of<TeamService>(context, listen: false).loadTeamMembers();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadTask() async {
    final taskService = Provider.of<TaskService>(context, listen: false);
    final task = await taskService.getTaskById(widget.taskId);
    
    if (task != null && mounted) {
      setState(() {
        _titleController.text = task.title;
        _descriptionController.text = task.description ?? '';
        _dueDate = task.dueDate;
        _priority = task.priority;
        _status = task.status;
        _selectedBranchId = task.branchId;
        _selectedBranchName = task.branchName;
        _selectedAssigneeId = task.assignedToId;
        _selectedAssigneeName = task.assignedToName;
        _isLoading = false;
      });
    } else if (mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Task not found'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _selectDueDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (pickedDate != null) {
      setState(() {
        _dueDate = pickedDate;
      });
    }
  }

  Future<void> _updateTask() async {
    if (_formKey.currentState!.validate()) {
      final taskService = Provider.of<TaskService>(context, listen: false);
      final currentTask = taskService.selectedTask;
      
      if (currentTask == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task not found'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final updatedTask = currentTask.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        status: _status,
        priority: _priority,
        dueDate: _dueDate,
        assignedToId: _selectedAssigneeId,
        assignedToName: _selectedAssigneeName,
        branchId: _selectedBranchId,
        branchName: _selectedBranchName,
        updatedAt: DateTime.now(),
        completedAt: _status == TaskStatus.completed ? DateTime.now() : currentTask.completedAt,
      );
      
      final result = await taskService.updateTask(updatedTask);
      
      if (result != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(taskService.error ?? 'Failed to update task'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final taskService = Provider.of<TaskService>(context);
    final branchService = Provider.of<BranchService>(context);
    final teamService = Provider.of<TeamService>(context);
    final branches = branchService.branches;
    final teamMembers = teamService.teamMembers;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Task'),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SafeArea(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Form title
                      Text(
                        'Task Information',
                        style: AppTextStyles.h4,
                      ),
                      SizedBox(height: 24.h),
                      
                      // Task title
                      CustomTextField(
                        controller: _titleController,
                        hintText: 'Task Title',
                        labelText: 'Task Title',
                        prefixIcon: Icons.task_outlined,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter task title';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Task description
                      CustomTextField(
                        controller: _descriptionController,
                        hintText: 'Description',
                        labelText: 'Description',
                        prefixIcon: Icons.description_outlined,
                        maxLines: 5,
                        textInputAction: TextInputAction.newline,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Status
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Status',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.border,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<TaskStatus>(
                                isExpanded: true,
                                value: _status,
                                items: TaskStatus.values.map((status) {
                                  final String text;
                                  final Color color;
                                  
                                  switch (status) {
                                    case TaskStatus.pending:
                                      text = 'Pending';
                                      color = Colors.orange;
                                      break;
                                    case TaskStatus.inProgress:
                                      text = 'In Progress';
                                      color = Colors.blue;
                                      break;
                                    case TaskStatus.completed:
                                      text = 'Completed';
                                      color = AppColors.success;
                                      break;
                                    case TaskStatus.cancelled:
                                      text = 'Cancelled';
                                      color = AppColors.error;
                                      break;
                                  }
                                  
                                  return DropdownMenuItem<TaskStatus>(
                                    value: status,
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 12.w,
                                          height: 12.w,
                                          decoration: BoxDecoration(
                                            color: color,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        SizedBox(width: 8.w),
                                        Text(
                                          text,
                                          style: AppTextStyles.bodyMedium,
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _status = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Due date
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Due Date',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          InkWell(
                            onTap: _selectDueDate,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 12.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 20.w,
                                    color: AppColors.textSecondary,
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    _dueDate == null
                                        ? 'Select Due Date'
                                        : '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year}',
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: _dueDate == null
                                          ? AppColors.textSecondary
                                          : AppColors.textPrimary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Priority
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Priority',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.border,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<TaskPriority>(
                                isExpanded: true,
                                value: _priority,
                                items: TaskPriority.values.map((priority) {
                                  final String text;
                                  final Color color;
                                  
                                  switch (priority) {
                                    case TaskPriority.low:
                                      text = 'Low';
                                      color = Colors.green;
                                      break;
                                    case TaskPriority.medium:
                                      text = 'Medium';
                                      color = Colors.orange;
                                      break;
                                    case TaskPriority.high:
                                      text = 'High';
                                      color = Colors.red;
                                      break;
                                  }
                                  
                                  return DropdownMenuItem<TaskPriority>(
                                    value: priority,
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 12.w,
                                          height: 12.w,
                                          decoration: BoxDecoration(
                                            color: color,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        SizedBox(width: 8.w),
                                        Text(
                                          text,
                                          style: AppTextStyles.bodyMedium,
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _priority = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch
                      if (branchService.isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else if (branches.isEmpty)
                        Text(
                          'No branches available. Please create a branch first.',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Branch',
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  isExpanded: true,
                                  hint: const Text('Select Branch'),
                                  value: _selectedBranchId,
                                  items: branches.map((branch) {
                                    return DropdownMenuItem<String>(
                                      value: branch.id,
                                      child: Text(branch.name),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedBranchId = value;
                                      _selectedBranchName = branches
                                          .firstWhere((branch) => branch.id == value)
                                          .name;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      SizedBox(height: 16.h),
                      
                      // Assignee
                      if (teamService.isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else if (teamMembers.isEmpty)
                        Text(
                          'No team members available. Please add team members first.',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Assign To',
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  isExpanded: true,
                                  hint: const Text('Select Assignee'),
                                  value: _selectedAssigneeId,
                                  items: teamMembers.map((member) {
                                    return DropdownMenuItem<String>(
                                      value: member.id,
                                      child: Text(member.name),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedAssigneeId = value;
                                      _selectedAssigneeName = teamMembers
                                          .firstWhere((member) => member.id == value)
                                          .name;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      SizedBox(height: 32.h),
                      
                      // Update button
                      CustomButton(
                        text: 'Update Task',
                        isLoading: taskService.isLoading,
                        onPressed: _updateTask,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Cancel button
                      CustomButton(
                        text: 'Cancel',
                        color: Colors.grey.shade200,
                        textColor: AppColors.textPrimary,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
