import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Update profile use case
class UpdateProfile {
  final AuthRepository _repository;

  UpdateProfile(this._repository);

  /// Call the use case
  Future<Either<AuthFailure, UserEntity>> call(Params params) {
    return _repository.updateProfile(
      displayName: params.displayName,
      photoURL: params.photoURL,
      phoneNumber: params.phoneNumber,
    );
  }
}

/// Update profile parameters
class Params extends Equatable {
  final String? displayName;
  final String? photoURL;
  final String? phoneNumber;

  const Params({
    this.displayName,
    this.photoURL,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [displayName, photoURL, phoneNumber];
}
