import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/branch_entity.dart';

/// Branch model
class BranchModel extends BranchEntity {
  const BranchModel({
    required super.id,
    required super.name,
    super.address,
    super.phoneNumber,
    super.email,
    super.managerId,
    super.managerName,
    super.logoUrl,
    super.isActive,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  /// Create a branch model from a Firestore document
  factory BranchModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    
    if (data == null) {
      throw Exception('Document data is null');
    }
    
    return BranchModel(
      id: doc.id,
      name: data['name'] ?? '',
      address: data['address'],
      phoneNumber: data['phoneNumber'],
      email: data['email'],
      managerId: data['managerId'],
      managerName: data['managerName'],
      logoUrl: data['logoUrl'],
      isActive: data['isActive'] ?? true,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      metadata: data['metadata'],
    );
  }

  /// Create a branch model from a map
  factory BranchModel.fromMap(Map<String, dynamic> map, String id) {
    return BranchModel(
      id: id,
      name: map['name'] ?? '',
      address: map['address'],
      phoneNumber: map['phoneNumber'],
      email: map['email'],
      managerId: map['managerId'],
      managerName: map['managerName'],
      logoUrl: map['logoUrl'],
      isActive: map['isActive'] ?? true,
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
      metadata: map['metadata'],
    );
  }

  /// Convert the branch model to a map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'address': address,
      'phoneNumber': phoneNumber,
      'email': email,
      'managerId': managerId,
      'managerName': managerName,
      'logoUrl': logoUrl,
      'isActive': isActive,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'metadata': metadata,
    };
  }

  /// Create a copy of this branch model with the given fields replaced
  @override
  BranchModel copyWith({
    String? id,
    String? name,
    String? address,
    String? phoneNumber,
    String? email,
    String? managerId,
    String? managerName,
    String? logoUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return BranchModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      logoUrl: logoUrl ?? this.logoUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }
}
