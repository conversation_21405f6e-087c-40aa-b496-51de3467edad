{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,233,362,460,549,668,846,1037,1175,1264,1370,1461,1562,1743,1857,1955,2204,2312,2406,2464,2528,2609,2704,2814,3027,3096,3173,3262,3328,3409,3480,3548,3656,3743,3833,3904,3971,4028,4128,4234,4337,4463,4558,4629,4700,4790,4861,4966,5081", "endColumns": "86,90,128,97,88,118,177,190,137,88,105,90,100,180,113,97,248,107,93,57,63,80,94,109,212,68,76,88,65,80,70,67,107,86,89,70,66,56,99,105,102,125,94,70,70,89,70,104,114,90", "endOffsets": "137,228,357,455,544,663,841,1032,1170,1259,1365,1456,1557,1738,1852,1950,2199,2307,2401,2459,2523,2604,2699,2809,3022,3091,3168,3257,3323,3404,3475,3543,3651,3738,3828,3899,3966,4023,4123,4229,4332,4458,4553,4624,4695,4785,4856,4961,5076,5167"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14784,21782,23763,24732,24830,29100,29569,29747,29938,30076,30265,30371,30462,30563,30744,30858,30956,31205,31405,31499,31557,31621,31702,31797,31907,32120,32189,32266,32355,32421,32502,32573,32641,33182,33269,33359,33430,33497,33554,33654,33760,33863,33989,34084,36958,37515,37605,37676,37781,37896", "endColumns": "86,90,128,97,88,118,177,190,137,88,105,90,100,180,113,97,248,107,93,57,63,80,94,109,212,68,76,88,65,80,70,67,107,86,89,70,66,56,99,105,102,125,94,70,70,89,70,104,114,90", "endOffsets": "14866,21868,23887,24825,24914,29214,29742,29933,30071,30160,30366,30457,30558,30739,30853,30951,31200,31308,31494,31552,31616,31697,31792,31902,32115,32184,32261,32350,32416,32497,32568,32636,32744,33264,33354,33425,33492,33549,33649,33755,33858,33984,34079,34150,37024,37600,37671,37776,37891,37982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6956,7733,7832,7939", "endColumns": "111,98,106,96", "endOffsets": "7063,7827,7934,8031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2836,2922,3000,3056,3107,3173,3241,3315,3404,3479,3551,3629,3699,3772,3876,3960,4037,4125,4214,4288,4361,4446,4495,4573,4639,4719,4802,4864,4928,4991,5060,5168,5271,5372,5471,5531,5586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2831,2917,2995,3051,3102,3168,3236,3310,3399,3474,3546,3624,3694,3767,3871,3955,4032,4120,4209,4283,4356,4441,4490,4568,4634,4714,4797,4859,4923,4986,5055,5163,5266,5367,5466,5526,5581,5661"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,7420,7647,8097,8336,8396,8483,8547,8609,8671,8739,8804,8860,8978,9036,9097,9153,9228,9354,9440,9520,9661,9739,9819,9941,10027,10105,10161,10212,10278,10346,10420,10509,10584,10656,10734,10804,10877,10981,11065,11142,11230,11319,11393,11466,11551,11600,11678,11744,11824,11907,11969,12033,12096,12165,12273,12376,12477,12576,12636,13071", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,7479,7728,8165,8391,8478,8542,8604,8666,8734,8799,8855,8973,9031,9092,9148,9223,9349,9435,9515,9656,9734,9814,9936,10022,10100,10156,10207,10273,10341,10415,10504,10579,10651,10729,10799,10872,10976,11060,11137,11225,11314,11388,11461,11546,11595,11673,11739,11819,11902,11964,12028,12091,12160,12268,12371,12472,12571,12631,12686,13146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4790,4950,5077,5186,5329,5454,5574,5806,5962,6068,6230,6357,6502,6680,6746,6808", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4785,4945,5072,5181,5324,5449,5569,5674,5957,6063,6225,6352,6497,6675,6741,6803,6881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5679", "endColumns": "126", "endOffsets": "5801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6886,7484,12862,13151,40058,40492,40572", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "6951,7567,12936,13294,40222,40567,40644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8036,40649", "endColumns": "60,73", "endOffsets": "8092,40718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15234,15475,15737,15883,15971,16038,16523,16592,16729,16793,16861,16929,16989,17047,17120,17243,17313,17542,18408,18472,19495,19644,19721,23514,23623,24639,25323,25378,25506,26220,26289,27121,27218,27935,28023", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "15294,15548,15799,15966,16033,16094,16587,16654,16788,16856,16924,16984,17042,17115,17179,17308,17371,17610,18467,18556,19564,19716,19807,23618,23703,24682,25373,25448,25563,26284,26353,27213,27300,28018,28105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "26559", "endColumns": "81", "endOffsets": "26636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,204,321,379,466,520,576,650,752,921,1186,1476,1550,1619,1703,1791,1907,2041,2108,2178,2348,2417,2476,2550,2631,2767,2891,3004,3087,3162,3247,3314", "endColumns": "68,79,116,57,86,53,55,73,101,168,264,289,73,68,83,87,115,133,66,69,169,68,58,73,80,135,123,112,82,74,84,66,156", "endOffsets": "119,199,316,374,461,515,571,645,747,916,1181,1471,1545,1614,1698,1786,1902,2036,2103,2173,2343,2412,2471,2545,2626,2762,2886,2999,3082,3157,3242,3309,3466"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14715,14871,27305,28975,34353,35948,36002,36058,36132,36234,36403,36668,37987,38061,38130,38214,38302,38418,38552,38619,38765,38935,39004,39063,39137,39218,39354,39478,39591,39674,39749,39834,39901", "endColumns": "68,79,116,57,86,53,55,73,101,168,264,289,73,68,83,87,115,133,66,69,169,68,58,73,80,135,123,112,82,74,84,66,156", "endOffsets": "14779,14946,27417,29028,34435,35997,36053,36127,36229,36398,36663,36953,38056,38125,38209,38297,38413,38547,38614,38684,38930,38999,39058,39132,39213,39349,39473,39586,39669,39744,39829,39896,40053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13715,13804,13908,13975,14042,14108,14181", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "13799,13903,13970,14037,14103,14176,14243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,7068,7163,7338,8170,8247,12691,12780,12941,13006,13299,13380,13544,40227,40305,40372", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4524,4604,7158,7257,7415,8242,8331,12775,12857,13001,13066,13375,13459,13609,40300,40367,40487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4609,7262,7572", "endColumns": "74,75,74", "endOffsets": "4679,7333,7642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,13614", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,13710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13464", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,421,469,536,635,704,959,1019,1111,1182,1237,1301,1619,1693,1758,1811,1864,1960,2084,2200,2257,2344,2423,2506,2572,2671,2969,3046,3123,3190,3250,3312,3372,3472,3564,3668,3760,3833,3912,3997,4195,4402,4514,4569,5286,5350", "endColumns": "156,208,47,66,98,68,254,59,91,70,54,63,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,99,91,103,91,72,78,84,197,206,111,54,716,63,54", "endOffsets": "207,416,464,531,630,699,954,1014,1106,1177,1232,1296,1614,1688,1753,1806,1859,1955,2079,2195,2252,2339,2418,2501,2567,2666,2964,3041,3118,3185,3245,3307,3367,3467,3559,3663,3755,3828,3907,3992,4190,4397,4509,4564,5281,5345,5400"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19916,20073,21092,21140,21207,21306,21375,21630,21690,23443,23708,23892,24321,24919,24993,25453,26358,26641,26737,26861,26977,27034,27600,27852,28278,28424,28523,28821,28898,29033,29219,29279,29509,30165,31313,32749,32853,32945,33018,33097,34155,34726,34933,35045,35167,35884,37460", "endColumns": "156,208,47,66,98,68,254,59,91,70,54,63,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,99,91,103,91,72,78,84,197,206,111,54,716,63,54", "endOffsets": "20068,20277,21135,21202,21301,21370,21625,21685,21777,23509,23758,23951,24634,24988,25053,25501,26406,26732,26856,26972,27029,27116,27674,27930,28339,28518,28816,28893,28970,29095,29274,29336,29564,30260,31400,32848,32940,33013,33092,33177,34348,34928,35040,35095,35879,35943,37510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15299,16099,16233,16295,16380,16659,17184,17376,17615,17928,18268,18561", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "15372,16151,16290,16375,16435,16724,17238,17444,17682,17989,18323,18623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3130,3195,3870,4521,4595,4712,4812,4867,4965,5055,5123,5214,5300,5357,5440,5491,5568,5664,5734,5805,5850,5928,6020,6067,6115,6183,6241,6306,6488,6651,6767,6831,6915,6992,7093,7182,7266,7342,7434,7514,7596,7682,7735,7866,7914,7968,8035,8102,8176,8240,8312,8400,8466", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,44,77,91,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,81,85,52,130,47,53,66,66,73,63,71,87,65,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3125,3190,3865,4516,4590,4707,4807,4862,4960,5050,5118,5209,5295,5352,5435,5486,5563,5659,5729,5800,5845,5923,6015,6062,6110,6178,6236,6301,6483,6646,6762,6826,6910,6987,7088,7177,7261,7337,7429,7509,7591,7677,7730,7861,7909,7963,8030,8097,8171,8235,8307,8395,8461,8537"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14248,14316,14393,14453,14521,14596,14656,14951,15004,15076,15156,15377,15553,15651,15804,16156,16440,17449,17687,17776,17842,17994,18080,18164,18328,18628,18715,18797,18899,18973,19061,19157,19243,19324,19423,19569,19812,20282,20352,20417,21873,22524,22598,22715,22815,22870,22968,23058,23126,23217,23303,23360,23956,24007,24084,24180,24250,24687,25058,25136,25228,25275,25568,25636,25694,25759,25941,26104,26411,26475,27422,27499,27679,27768,28110,28186,28344,29341,29423,34440,34493,34624,34672,35100,37029,37096,37170,37234,37306,37394,38689", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,44,77,91,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,81,85,52,130,47,53,66,66,73,63,71,87,65,75", "endOffsets": "14311,14388,14448,14516,14591,14651,14710,14999,15071,15151,15229,15470,15646,15732,15878,16228,16518,17537,17771,17837,17923,18075,18159,18263,18403,18710,18792,18894,18968,19056,19152,19238,19319,19418,19490,19639,19911,20347,20412,21087,22519,22593,22710,22810,22865,22963,23053,23121,23212,23298,23355,23438,24002,24079,24175,24245,24316,24727,25131,25223,25270,25318,25631,25689,25754,25936,26099,26215,26470,26554,27494,27595,27763,27847,28181,28273,28419,29418,29504,34488,34619,34667,34721,35162,37091,37165,37229,37301,37389,37455,38760"}}]}]}