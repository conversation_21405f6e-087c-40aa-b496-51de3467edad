import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/subscription_plan.dart';
import '../repositories/subscription_repository.dart';

/// Use case to get subscription plans for a specific country
class GetSubscriptionPlansByCountryUseCase implements UseCase<List<SubscriptionPlan>, CountryParams> {
  final SubscriptionRepository repository;

  /// Constructor
  GetSubscriptionPlansByCountryUseCase(this.repository);

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> call(CountryParams params) {
    return repository.getSubscriptionPlansByCountry(params.countryCode);
  }
}

/// Parameters for GetSubscriptionPlansByCountryUseCase
class CountryParams extends Equatable {
  /// Country code
  final String countryCode;

  /// Constructor
  const CountryParams({required this.countryCode});

  @override
  List<Object?> get props => [countryCode];
}
