import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';

/// Branch repository interface
abstract class BranchRepository {
  /// Get all branches
  Future<Either<Failure, List<BranchEntity>>> getAllBranches();

  /// Get branch by id
  Future<Either<Failure, BranchEntity>> getBranchById(String branchId);

  /// Create branch
  Future<Either<Failure, BranchEntity>> createBranch(BranchEntity branch);

  /// Update branch
  Future<Either<Failure, BranchEntity>> updateBranch(BranchEntity branch);

  /// Delete branch
  Future<Either<Failure, void>> deleteBranch(String branchId);

  /// Get branches by manager id
  Future<Either<Failure, List<BranchEntity>>> getBranchesByManagerId(String managerId);

  /// Get active branches
  Future<Either<Failure, List<BranchEntity>>> getActiveBranches();
}
