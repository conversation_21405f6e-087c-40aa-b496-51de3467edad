import 'package:flutter/foundation.dart';
import '../../features/real_estate_projects/domain/entities/project_entity.dart';
import '../../features/real_estate_projects/domain/entities/phase_entity.dart';
import '../../features/real_estate_projects/domain/entities/project_file_entity.dart';
import '../../features/real_estate_projects/domain/entities/schedule_entity.dart';
import '../data/mock_real_estate_data.dart';

/// خدمة إدارة المشاريع العقارية
class RealEstateService extends ChangeNotifier {
  List<ProjectEntity> _projects = [];
  ProjectEntity? _selectedProject;
  List<PhaseEntity> _phases = [];
  PhaseEntity? _selectedPhase;
  List<ProjectFileEntity> _files = [];
  List<ScheduleEntity> _schedules = [];
  bool _isLoading = false;
  String? _error;

  /// الحصول على جميع المشاريع
  List<ProjectEntity> get projects => _projects;

  /// الحصول على المشروع المحدد
  ProjectEntity? get selectedProject => _selectedProject;

  /// الحصول على جميع المراحل
  List<PhaseEntity> get phases => _phases;

  /// الحصول على المرحلة المحددة
  PhaseEntity? get selectedPhase => _selectedPhase;

  /// الحصول على جميع الملفات
  List<ProjectFileEntity> get files => _files;

  /// الحصول على جميع المواعيد
  List<ScheduleEntity> get schedules => _schedules;

  /// التحقق من حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get error => _error;

  /// تحميل جميع المشاريع
  Future<void> loadProjects() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المشاريع من البيانات الوهمية
      _projects = List.from(MockRealEstateData.projects);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// الحصول على مشروع بالمعرف
  Future<ProjectEntity?> getProjectById(String projectId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المشروع من البيانات الوهمية
      _selectedProject = MockRealEstateData.getProjectById(projectId);
      _isLoading = false;
      notifyListeners();
      return _selectedProject;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// إنشاء مشروع جديد
  Future<ProjectEntity?> createProject(ProjectEntity project) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // إضافة المشروع إلى البيانات الوهمية
      final newProject = MockRealEstateData.addProject(project);
      _projects.add(newProject);
      _selectedProject = newProject;
      _isLoading = false;
      notifyListeners();
      return newProject;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// تحديث مشروع
  Future<ProjectEntity?> updateProject(ProjectEntity project) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث المشروع في البيانات الوهمية
      final updatedProject = MockRealEstateData.updateProject(project);
      
      // تحديث المشروع في القائمة
      final index = _projects.indexWhere((p) => p.id == project.id);
      if (index != -1) {
        _projects[index] = updatedProject;
      }
      
      _selectedProject = updatedProject;
      _isLoading = false;
      notifyListeners();
      return updatedProject;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// حذف مشروع
  Future<bool> deleteProject(String projectId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // حذف المشروع من البيانات الوهمية
      MockRealEstateData.deleteProject(projectId);
      
      // حذف المشروع من القائمة
      _projects.removeWhere((project) => project.id == projectId);
      
      if (_selectedProject?.id == projectId) {
        _selectedProject = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تحميل مراحل مشروع
  Future<void> loadProjectPhases(String projectId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المراحل من البيانات الوهمية
      _phases = MockRealEstateData.getPhasesByProjectId(projectId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// تحميل ملفات مشروع
  Future<void> loadProjectFiles(String projectId, {String? phaseId}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على الملفات من البيانات الوهمية
      _files = MockRealEstateData.getFilesByProjectId(projectId, phaseId: phaseId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// تحميل مواعيد مشروع
  Future<void> loadProjectSchedules(String projectId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المواعيد من البيانات الوهمية
      _schedules = MockRealEstateData.getSchedulesByProjectId(projectId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// الحصول على المشاريع حسب النوع
  List<ProjectEntity> getProjectsByType(PropertyType type) {
    return _projects.where((project) => project.propertyType == type).toList();
  }

  /// الحصول على المشاريع حسب الحالة
  List<ProjectEntity> getProjectsByStatus(ProjectStatus status) {
    return _projects.where((project) => project.status == status).toList();
  }

  /// تعيين المشروع المحدد
  void setSelectedProject(ProjectEntity project) {
    _selectedProject = project;
    notifyListeners();
  }

  /// تعيين المرحلة المحددة
  void setSelectedPhase(PhaseEntity phase) {
    _selectedPhase = phase;
    notifyListeners();
  }

  /// مسح المشروع المحدد
  void clearSelectedProject() {
    _selectedProject = null;
    notifyListeners();
  }

  /// مسح المرحلة المحددة
  void clearSelectedPhase() {
    _selectedPhase = null;
    notifyListeners();
  }

  /// إضافة ملف جديد
  Future<ProjectFileEntity?> addFile(ProjectFileEntity file) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // إضافة الملف إلى البيانات الوهمية
      final newFile = MockRealEstateData.addFile(file);
      _files.add(newFile);
      _isLoading = false;
      notifyListeners();
      return newFile;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// إضافة موعد جديد
  Future<ScheduleEntity?> addSchedule(ScheduleEntity schedule) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // إضافة الموعد إلى البيانات الوهمية
      final newSchedule = MockRealEstateData.addSchedule(schedule);
      _schedules.add(newSchedule);
      _isLoading = false;
      notifyListeners();
      return newSchedule;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
}
