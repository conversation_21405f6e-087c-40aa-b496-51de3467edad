import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../domain/entities/project_entity.dart';

/// شاشة تحديد نوع المنشأة
class PropertyTypeSelectionScreen extends StatefulWidget {
  const PropertyTypeSelectionScreen({super.key});

  @override
  State<PropertyTypeSelectionScreen> createState() => _PropertyTypeSelectionScreenState();
}

class _PropertyTypeSelectionScreenState extends State<PropertyTypeSelectionScreen> {
  PropertyType? _selectedType;

  void _selectPropertyType(PropertyType type) {
    setState(() {
      _selectedType = type;
    });
  }

  void _continue() {
    if (_selectedType != null) {
      Navigator.pushNamed(
        context,
        '/construction-phases',
        arguments: _selectedType,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('نوع المنشأة'),
        backgroundColor: AppColors.background,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Text(
                'اختر نوع المنشأة',
                style: AppTextStyles.h2.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'حدد نوع المشروع العقاري الذي تريد إدارته',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 48.h),

              // Property type cards
              Expanded(
                child: Column(
                  children: [
                    // Residential card
                    _buildPropertyTypeCard(
                      type: PropertyType.residential,
                      title: 'سكنية',
                      subtitle: 'فلل، شقق، مجمعات سكنية',
                      icon: Icons.home,
                      description: 'مشاريع سكنية تشمل الفلل والشقق والمجمعات السكنية والوحدات التجارية السكنية',
                      examples: ['فلل سكنية', 'شقق سكنية', 'مجمعات سكنية', 'استراحات'],
                    ),

                    SizedBox(height: 24.h),

                    // Commercial card
                    _buildPropertyTypeCard(
                      type: PropertyType.commercial,
                      title: 'تجارية',
                      subtitle: 'مكاتب، محلات، مراكز تجارية',
                      icon: Icons.business,
                      description: 'مشاريع تجارية تشمل المكاتب والمحلات التجارية والمراكز التجارية والمباني الإدارية',
                      examples: ['مراكز تجارية', 'مباني إدارية', 'محلات تجارية', 'مكاتب'],
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),

              // Continue button
              ElevatedButton(
                onPressed: _selectedType != null ? _continue : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'متابعة',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPropertyTypeCard({
    required PropertyType type,
    required String title,
    required String subtitle,
    required IconData icon,
    required String description,
    required List<String> examples,
  }) {
    final isSelected = _selectedType == type;

    return GestureDetector(
      onTap: () => _selectPropertyType(type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    icon,
                    size: 30.w,
                    color: isSelected ? Colors.white : AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.h4.copyWith(
                          color: isSelected ? AppColors.primary : AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        subtitle,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      size: 16.w,
                      color: Colors.white,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              description,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 12.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: examples.map((example) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primary.withOpacity(0.2) 
                        : AppColors.background,
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : AppColors.border,
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    example,
                    style: AppTextStyles.caption.copyWith(
                      color: isSelected ? AppColors.primary : AppColors.textSecondary,
                      fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
