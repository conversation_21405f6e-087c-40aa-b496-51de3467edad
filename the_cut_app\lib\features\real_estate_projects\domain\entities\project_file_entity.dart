import 'package:equatable/equatable.dart';
import 'phase_entity.dart';

/// نوع الملف
enum FileType {
  image,      // صورة
  video,      // فيديو
  document,   // مستند
  pdf,        // ملف PDF
  audio,      // ملف صوتي
  other,      // أخرى
}

/// كيان ملف المشروع
class ProjectFileEntity extends Equatable {
  /// معرف الملف
  final String id;

  /// معرف المشروع
  final String projectId;

  /// معرف المرحلة
  final String? phaseId;

  /// القسم الذي ينتمي إليه الملف
  final PhaseSection section;

  /// اسم الملف
  final String fileName;

  /// الاسم الأصلي للملف
  final String originalFileName;

  /// نوع الملف
  final FileType fileType;

  /// حجم الملف بالبايت
  final int fileSize;

  /// رابط الملف
  final String fileUrl;

  /// رابط الصورة المصغرة (للصور والفيديوهات)
  final String? thumbnailUrl;

  /// وصف الملف
  final String? description;

  /// تاريخ رفع الملف
  final DateTime uploadDate;

  /// معرف المستخدم الذي رفع الملف
  final String uploadedById;

  /// اسم المستخدم الذي رفع الملف
  final String uploadedByName;

  /// العلامات (Tags)
  final List<String>? tags;

  /// هل الملف مفضل
  final bool isFavorite;

  /// هل الملف محذوف (soft delete)
  final bool isDeleted;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// Constructor
  const ProjectFileEntity({
    required this.id,
    required this.projectId,
    this.phaseId,
    required this.section,
    required this.fileName,
    required this.originalFileName,
    required this.fileType,
    required this.fileSize,
    required this.fileUrl,
    this.thumbnailUrl,
    this.description,
    required this.uploadDate,
    required this.uploadedById,
    required this.uploadedByName,
    this.tags,
    this.isFavorite = false,
    this.isDeleted = false,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة محدثة من الملف
  ProjectFileEntity copyWith({
    String? id,
    String? projectId,
    String? phaseId,
    PhaseSection? section,
    String? fileName,
    String? originalFileName,
    FileType? fileType,
    int? fileSize,
    String? fileUrl,
    String? thumbnailUrl,
    String? description,
    DateTime? uploadDate,
    String? uploadedById,
    String? uploadedByName,
    List<String>? tags,
    bool? isFavorite,
    bool? isDeleted,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ProjectFileEntity(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      phaseId: phaseId ?? this.phaseId,
      section: section ?? this.section,
      fileName: fileName ?? this.fileName,
      originalFileName: originalFileName ?? this.originalFileName,
      fileType: fileType ?? this.fileType,
      fileSize: fileSize ?? this.fileSize,
      fileUrl: fileUrl ?? this.fileUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      description: description ?? this.description,
      uploadDate: uploadDate ?? this.uploadDate,
      uploadedById: uploadedById ?? this.uploadedById,
      uploadedByName: uploadedByName ?? this.uploadedByName,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      isDeleted: isDeleted ?? this.isDeleted,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// الحصول على امتداد الملف
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  @override
  List<Object?> get props => [
        id,
        projectId,
        phaseId,
        section,
        fileName,
        originalFileName,
        fileType,
        fileSize,
        fileUrl,
        thumbnailUrl,
        description,
        uploadDate,
        uploadedById,
        uploadedByName,
        tags,
        isFavorite,
        isDeleted,
        updatedAt,
        metadata,
      ];
}

/// امتدادات مساعدة لنوع الملف
extension FileTypeExtension on FileType {
  String get nameAr {
    switch (this) {
      case FileType.image:
        return 'صورة';
      case FileType.video:
        return 'فيديو';
      case FileType.document:
        return 'مستند';
      case FileType.pdf:
        return 'ملف PDF';
      case FileType.audio:
        return 'ملف صوتي';
      case FileType.other:
        return 'أخرى';
    }
  }

  String get nameEn {
    switch (this) {
      case FileType.image:
        return 'Image';
      case FileType.video:
        return 'Video';
      case FileType.document:
        return 'Document';
      case FileType.pdf:
        return 'PDF';
      case FileType.audio:
        return 'Audio';
      case FileType.other:
        return 'Other';
    }
  }

  String get iconName {
    switch (this) {
      case FileType.image:
        return 'image';
      case FileType.video:
        return 'videocam';
      case FileType.document:
        return 'description';
      case FileType.pdf:
        return 'picture_as_pdf';
      case FileType.audio:
        return 'audiotrack';
      case FileType.other:
        return 'insert_drive_file';
    }
  }

  /// تحديد نوع الملف من الامتداد
  static FileType fromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return FileType.image;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return FileType.video;
      case 'pdf':
        return FileType.pdf;
      case 'mp3':
      case 'wav':
      case 'aac':
      case 'flac':
        return FileType.audio;
      case 'doc':
      case 'docx':
      case 'txt':
      case 'rtf':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
        return FileType.document;
      default:
        return FileType.other;
    }
  }
}
