{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "82,429", "startColumns": "4,4", "startOffsets": "8095,40257", "endColumns": "60,73", "endOffsets": "8151,40326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5725", "endColumns": "147", "endOffsets": "5868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,424,425,426", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4563,7108,7204,7379,8220,8296,12722,12811,12970,13034,13313,13399,13569,39837,39915,39982", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4558,4646,7199,7298,7462,8291,8379,12806,12887,13029,13093,13394,13484,13633,39910,39977,40097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,211", "endColumns": "79,75,73", "endOffsets": "130,206,280"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4651,7303,7618", "endColumns": "79,75,73", "endOffsets": "4726,7374,7687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4731,4838,4995,5122,5232,5373,5498,5621,5873,6021,6129,6291,6419,6573,6729,6795,6858", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4833,4990,5117,5227,5368,5493,5616,5720,6016,6124,6286,6414,6568,6724,6790,6853,6932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,343,429,486,549,601,667,748,817,912,1007,1091,1169,1247,1330,1426,1518,1587,1676,1762,1849,1956,2039,2128,2204,2300,2372,2461,2553,2636,2710,2805,2869,2940,3052,3123,3188,3846,4485,4559,4671,4772,4827,4924,5013,5079,5167,5260,5317,5401,5450,5526,5620,5687,5765,5812,5895,5992,6037,6086,6153,6211,6279,6459,6614,6750,6815,6902,6977,7064,7143,7223,7300,7387,7468,7555,7642,7695,7827,7877,7939,8006,8073,8146,8210,8282,8365,8433", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,46,82,96,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,86,86,52,131,49,61,66,66,72,63,71,82,67,78", "endOffsets": "118,195,261,338,424,481,544,596,662,743,812,907,1002,1086,1164,1242,1325,1421,1513,1582,1671,1757,1844,1951,2034,2123,2199,2295,2367,2456,2548,2631,2705,2800,2864,2935,3047,3118,3183,3841,4480,4554,4666,4767,4822,4919,5008,5074,5162,5255,5312,5396,5445,5521,5615,5682,5760,5807,5890,5987,6032,6081,6148,6206,6274,6454,6609,6745,6810,6897,6972,7059,7138,7218,7295,7382,7463,7550,7637,7690,7822,7872,7934,8001,8068,8141,8205,8277,8360,8428,8507"}, "to": {"startLines": "154,155,156,157,158,159,160,164,165,166,167,170,172,173,175,180,184,199,202,203,204,206,207,208,210,214,215,216,217,218,219,220,221,222,223,225,228,231,232,233,242,243,244,245,246,247,248,249,250,251,252,253,260,261,262,263,264,267,272,273,274,275,280,281,282,283,284,285,289,290,300,301,303,304,308,309,311,321,322,371,372,373,374,378,389,390,391,392,393,394,409", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13739,13807,13884,13950,14027,14113,14170,14478,14530,14596,14677,14885,15055,15150,15301,15652,15942,16978,17221,17313,17382,17537,17623,17710,17880,18177,18266,18342,18438,18510,18599,18691,18774,18848,18943,19081,19316,19802,19873,19938,21410,22049,22123,22235,22336,22391,22488,22577,22643,22731,22824,22881,23470,23519,23595,23689,23756,24189,24580,24663,24760,24805,25098,25165,25223,25291,25471,25626,25957,26022,26948,27023,27188,27267,27604,27681,27834,28819,28906,33959,34012,34144,34194,34628,36604,36671,36744,36808,36880,36963,38293", "endColumns": "67,76,65,76,85,56,62,51,65,80,68,94,94,83,77,77,82,95,91,68,88,85,86,106,82,88,75,95,71,88,91,82,73,94,63,70,111,70,64,657,638,73,111,100,54,96,88,65,87,92,56,83,48,75,93,66,77,46,82,96,44,48,66,57,67,179,154,135,64,86,74,86,78,79,76,86,80,86,86,52,131,49,61,66,66,72,63,71,82,67,78", "endOffsets": "13802,13879,13945,14022,14108,14165,14228,14525,14591,14672,14741,14975,15145,15229,15374,15725,16020,17069,17308,17377,17466,17618,17705,17812,17958,18261,18337,18433,18505,18594,18686,18769,18843,18938,19002,19147,19423,19868,19933,20591,22044,22118,22230,22331,22386,22483,22572,22638,22726,22819,22876,22960,23514,23590,23684,23751,23829,24231,24658,24755,24800,24849,25160,25218,25286,25466,25621,25757,26017,26104,27018,27105,27262,27342,27676,27763,27910,28901,28988,34007,34139,34189,34251,34690,36666,36739,36803,36875,36958,37026,38367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,238,363,472,567,685,874,1077,1211,1303,1408,1500,1596,1784,1883,1980,2234,2338,2427,2487,2552,2638,2730,2837,3050,3120,3212,3303,3371,3455,3527,3596,3708,3796,3888,3961,4030,4089,4191,4295,4399,4530,4625,4697,4772,4862,4934,5044,5160", "endColumns": "89,92,124,108,94,117,188,202,133,91,104,91,95,187,98,96,253,103,88,59,64,85,91,106,212,69,91,90,67,83,71,68,111,87,91,72,68,58,101,103,103,130,94,71,74,89,71,109,115,92", "endOffsets": "140,233,358,467,562,680,869,1072,1206,1298,1403,1495,1591,1779,1878,1975,2229,2333,2422,2482,2547,2633,2725,2832,3045,3115,3207,3298,3366,3450,3522,3591,3703,3791,3883,3956,4025,4084,4186,4290,4394,4525,4620,4692,4767,4857,4929,5039,5155,5248"}, "to": {"startLines": "162,241,258,268,269,318,324,325,326,327,329,330,331,332,333,334,335,336,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,358,359,360,361,362,363,364,365,366,367,368,388,396,397,398,399,400", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14305,21317,23281,24236,24345,28578,29053,29242,29445,29579,29771,29876,29968,30064,30252,30351,30448,30702,30890,30979,31039,31104,31190,31282,31389,31602,31672,31764,31855,31923,32007,32079,32148,32687,32775,32867,32940,33009,33068,33170,33274,33378,33509,33604,36529,37086,37176,37248,37358,37474", "endColumns": "89,92,124,108,94,117,188,202,133,91,104,91,95,187,98,96,253,103,88,59,64,85,91,106,212,69,91,90,67,83,71,68,111,87,91,72,68,58,101,103,103,130,94,71,74,89,71,109,115,92", "endOffsets": "14390,21405,23401,24340,24435,28691,29237,29440,29574,29666,29871,29963,30059,30247,30346,30443,30697,30801,30974,31034,31099,31185,31277,31384,31597,31667,31759,31850,31918,32002,32074,32143,32255,32770,32862,32935,33004,33063,33165,33269,33373,33504,33599,33671,36599,37171,37243,37353,37469,37562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,188,250,335,400,477,536,612,684,750,813", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "125,183,245,330,395,472,531,607,679,745,808,877"}, "to": {"startLines": "169,179,181,182,183,187,195,198,201,205,209,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14810,15594,15730,15792,15877,16164,16709,16902,17149,17471,17817,18108", "endColumns": "74,57,61,84,64,76,58,75,71,65,62,68", "endOffsets": "14880,15647,15787,15872,15937,16236,16763,16973,17216,17532,17875,18172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "26109", "endColumns": "84", "endOffsets": "26189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,819,932,1009,1084,1177,1272,1367,1461,1563,1658,1755,1853,1949,2042,2122,2228,2327,2423,2528,2631,2733,2887,13489", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,814,927,1004,1079,1172,1267,1362,1456,1558,1653,1750,1848,1944,2037,2117,2223,2322,2418,2523,2626,2728,2882,2984,13564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,429,478,545,641,710,967,1044,1150,1220,1274,1338,1645,1719,1785,1838,1891,1973,2090,2197,2254,2340,2418,2500,2566,2665,2943,3021,3104,3167,3227,3290,3350,3450,3534,3632,3724,3797,3876,3961,4154,4356,4469,4526,5227,5293", "endColumns": "168,204,48,66,95,68,256,76,105,69,53,63,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,99,83,97,91,72,78,84,192,201,112,56,700,65,54", "endOffsets": "219,424,473,540,636,705,962,1039,1145,1215,1269,1333,1640,1714,1780,1833,1886,1968,2085,2192,2249,2335,2413,2495,2561,2660,2938,3016,3099,3162,3222,3285,3345,3445,3529,3627,3719,3792,3871,3956,4149,4351,4464,4521,5222,5288,5343"}, "to": {"startLines": "229,230,234,235,236,237,238,239,240,254,257,259,265,270,271,278,288,292,293,294,295,296,302,305,310,312,313,314,315,317,319,320,323,328,337,353,354,355,356,357,369,375,376,377,379,380,395", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19428,19597,20596,20645,20712,20808,20877,21134,21211,22965,23227,23406,23834,24440,24514,24983,25904,26194,26276,26393,26500,26557,27110,27347,27768,27915,28014,28292,28370,28515,28696,28756,28993,29671,30806,32260,32358,32450,32523,32602,33676,34256,34458,34571,34695,35396,37031", "endColumns": "168,204,48,66,95,68,256,76,105,69,53,63,306,73,65,52,52,81,116,106,56,85,77,81,65,98,277,77,82,62,59,62,59,99,83,97,91,72,78,84,192,201,112,56,700,65,54", "endOffsets": "19592,19797,20640,20707,20803,20872,21129,21206,21312,23030,23276,23465,24136,24509,24575,25031,25952,26271,26388,26495,26552,26638,27183,27424,27829,28009,28287,28365,28448,28573,28751,28814,29048,29766,30885,32353,32445,32518,32597,32682,33864,34453,34566,34623,35391,35457,37081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3443,3538,3640,3738,3837,3945,4050,13638", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3533,3635,3733,3832,3940,4045,4166,13734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1082,1174,1238,1298,1390,1455,1518,1580,1647,1711,1765,1870,1929,1990,2044,2113,2232,2315,2399,2535,2614,2698,2820,2906,2984,3038,3089,3155,3224,3298,3387,3463,3535,3612,3683,3757,3868,3959,4038,4125,4213,4285,4359,4444,4495,4574,4641,4722,4806,4868,4932,4995,5063,5170,5269,5368,5463,5521,5576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "261,360,452,533,635,715,813,935,1014,1077,1169,1233,1293,1385,1450,1513,1575,1642,1706,1760,1865,1924,1985,2039,2108,2227,2310,2394,2530,2609,2693,2815,2901,2979,3033,3084,3150,3219,3293,3382,3458,3530,3607,3678,3752,3863,3954,4033,4120,4208,4280,4354,4439,4490,4569,4636,4717,4801,4863,4927,4990,5058,5165,5264,5363,5458,5516,5571,5649"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2989,3088,3180,3261,3363,4171,4269,4391,7467,7692,8156,8384,8444,8536,8601,8664,8726,8793,8857,8911,9016,9075,9136,9190,9259,9378,9461,9545,9681,9760,9844,9966,10052,10130,10184,10235,10301,10370,10444,10533,10609,10681,10758,10829,10903,11014,11105,11184,11271,11359,11431,11505,11590,11641,11720,11787,11868,11952,12014,12078,12141,12209,12316,12415,12514,12609,12667,13098", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "311,3083,3175,3256,3358,3438,4264,4386,4465,7525,7779,8215,8439,8531,8596,8659,8721,8788,8852,8906,9011,9070,9131,9185,9254,9373,9456,9540,9676,9755,9839,9961,10047,10125,10179,10230,10296,10365,10439,10528,10604,10676,10753,10824,10898,11009,11100,11179,11266,11354,11426,11500,11585,11636,11715,11782,11863,11947,12009,12073,12136,12204,12311,12410,12509,12604,12662,12717,13171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7008,7784,7884,7997", "endColumns": "99,99,112,97", "endOffsets": "7103,7879,7992,8090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,194,261,346,415,476,548,615,679,747,817,877,935,1008,1083,1154,1217,1292,1355,1437,1511,1590,1675,1782,1867,1915,1963,2044,2106,2179,2248,2345,2432,2521", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "114,189,256,341,410,471,543,610,674,742,812,872,930,1003,1078,1149,1212,1287,1350,1432,1506,1585,1670,1777,1862,1910,1958,2039,2101,2174,2243,2340,2427,2516,2602"}, "to": {"startLines": "168,171,174,176,177,178,185,186,188,189,190,191,192,193,194,196,197,200,211,212,224,226,227,255,256,266,276,277,279,286,287,297,298,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14746,14980,15234,15379,15464,15533,16025,16097,16241,16305,16373,16443,16503,16561,16634,16768,16839,17074,17963,18026,19007,19152,19231,23035,23142,24141,24854,24902,25036,25762,25835,26643,26740,27429,27518", "endColumns": "63,74,66,84,68,60,71,66,63,67,69,59,57,72,74,70,62,74,62,81,73,78,84,106,84,47,47,80,61,72,68,96,86,88,85", "endOffsets": "14805,15050,15296,15459,15528,15589,16092,16159,16300,16368,16438,16498,16556,16629,16704,16834,16897,17144,18021,18103,19076,19226,19311,23137,23222,24184,24897,24978,25093,25830,25899,26735,26822,27513,27599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,210,331,393,483,538,600,678,780,962,1246,1550,1630,1707,1787,1877,1997,2136,2203,2276,2457,2527,2586,2661,2744,2863,2988,3098,3183,3262,3344,3413", "endColumns": "71,82,120,61,89,54,61,77,101,181,283,303,79,76,79,89,119,138,66,72,180,69,58,74,82,118,124,109,84,78,81,68,158", "endOffsets": "122,205,326,388,478,533,595,673,775,957,1241,1545,1625,1702,1782,1872,1992,2131,2198,2271,2452,2522,2581,2656,2739,2858,2983,3093,3178,3257,3339,3408,3567"}, "to": {"startLines": "161,163,299,316,370,381,382,383,384,385,386,387,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14233,14395,26827,28453,33869,35462,35517,35579,35657,35759,35941,36225,37567,37647,37724,37804,37894,38014,38153,38220,38372,38553,38623,38682,38757,38840,38959,39084,39194,39279,39358,39440,39509", "endColumns": "71,82,120,61,89,54,61,77,101,181,283,303,79,76,79,89,119,138,66,72,180,69,58,74,82,118,124,109,84,78,81,68,158", "endOffsets": "14300,14473,26943,28510,33954,35512,35574,35652,35754,35936,36220,36524,37642,37719,37799,37889,38009,38148,38215,38288,38548,38618,38677,38752,38835,38954,39079,39189,39274,39353,39435,39504,39663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,342,479,648,727", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "171,259,337,474,643,722,798"}, "to": {"startLines": "69,76,144,148,423,427,428", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6937,7530,12892,13176,39668,40102,40181", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "7003,7613,12965,13308,39832,40176,40252"}}]}]}