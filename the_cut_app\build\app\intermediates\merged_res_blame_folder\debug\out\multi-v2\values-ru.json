{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "84,438", "startColumns": "4,4", "startOffsets": "8368,42403", "endColumns": "60,78", "endOffsets": "8424,42477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3361,3425,4168,4845,4919,5025,5132,5187,5276,5362,5431,5518,5620,5676,5766,5815,5892,5999,6064,6135,6184,6264,6356,6403,6457,6531,6589,6668,6847,7002,7135,7202,7296,7375,7472,7555,7636,7712,7801,7883,7970,8064,8120,8257,8307,8362,8451,8518,8587,8657,8726,8814,8885", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,86,93,55,136,49,54,88,66,68,69,68,87,70,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3356,3420,4163,4840,4914,5020,5127,5182,5271,5357,5426,5513,5615,5671,5761,5810,5887,5994,6059,6130,6179,6259,6351,6398,6452,6526,6584,6663,6842,6997,7130,7197,7291,7370,7467,7550,7631,7707,7796,7878,7965,8059,8115,8252,8302,8357,8446,8513,8582,8652,8721,8809,8880,8954"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,240,241,242,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,276,281,282,283,284,289,290,291,292,293,294,298,299,309,310,312,313,317,318,320,330,331,380,381,382,383,387,398,399,400,401,402,403,418", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14726,14795,14873,14943,15025,15121,15186,15501,15556,15627,15716,15933,16118,16223,16378,16734,17033,18097,18364,18466,18540,18707,18800,18890,19065,19375,19474,19555,19661,19735,19828,19933,20027,20121,20222,20364,20621,21124,21195,21259,22813,23490,23564,23670,23777,23832,23921,24007,24076,24163,24265,24321,24945,24994,25071,25178,25243,25702,26071,26151,26243,26290,26598,26672,26730,26809,26988,27143,27475,27542,28532,28611,28790,28873,29237,29313,29487,30531,30618,35883,35939,36076,36126,36586,38644,38711,38780,38850,38919,39007,40400", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,86,93,55,136,49,54,88,66,68,69,68,87,70,73", "endOffsets": "14790,14868,14938,15020,15116,15181,15252,15551,15622,15711,15785,16033,16218,16306,16455,16814,17118,18194,18461,18535,18634,18795,18885,19000,19145,19469,19550,19656,19730,19823,19928,20022,20116,20217,20283,20430,20732,21190,21254,21997,23485,23559,23665,23772,23827,23916,24002,24071,24158,24260,24316,24406,24989,25066,25173,25238,25309,25746,26146,26238,26285,26339,26667,26725,26804,26983,27138,27271,27537,27631,28606,28703,28868,28949,29308,29397,29564,30613,30707,35934,36071,36121,36176,36670,38706,38775,38845,38914,39002,39073,40469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5882", "endColumns": "156", "endOffsets": "6034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,13925", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,14002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "71,78,146,150,432,436,437", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7156,7777,13311,13609,41797,42243,42325", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "7225,7864,13383,13749,41961,42320,42398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,209,352,409,501,560,623,725,833,1019,1315,1626,1720,1792,1871,1961,2092,2227,2292,2368,2554,2621,2684,2756,2839,2956,3080,3189,3278,3367,3455,3534", "endColumns": "76,76,142,56,91,58,62,101,107,185,295,310,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,108,88,88,87,78,156", "endOffsets": "127,204,347,404,496,555,618,720,828,1014,1310,1621,1715,1787,1866,1956,2087,2222,2287,2363,2549,2616,2679,2751,2834,2951,3075,3184,3273,3362,3450,3529,3686"}, "to": {"startLines": "170,172,308,325,379,390,391,392,393,394,395,396,410,411,412,413,414,415,416,417,419,420,421,422,423,424,425,426,427,428,429,430,431", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15257,15424,28389,30148,35791,37446,37505,37568,37670,37778,37964,38260,39658,39752,39824,39903,39993,40124,40259,40324,40474,40660,40727,40790,40862,40945,41062,41186,41295,41384,41473,41561,41640", "endColumns": "76,76,142,56,91,58,62,101,107,185,295,310,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,108,88,88,87,78,156", "endOffsets": "15329,15496,28527,30200,35878,37500,37563,37665,37773,37959,38255,38566,39747,39819,39898,39988,40119,40254,40319,40395,40655,40722,40785,40857,40940,41057,41181,41290,41379,41468,41556,41635,41792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14180,14266,14369,14436,14503,14567,14654", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "14261,14364,14431,14498,14562,14649,14721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4883,4990,5156,5282,5392,5534,5663,5778,6039,6220,6327,6490,6616,6783,6941,7010,7070", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4985,5151,5277,5387,5529,5658,5773,5877,6215,6322,6485,6611,6778,6936,7005,7065,7151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7230,8046,8151,8263", "endColumns": "107,104,111,104", "endOffsets": "7333,8146,8258,8363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,264,265,275,285,286,288,295,296,306,307,315,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15790,16038,16311,16460,16544,16613,17123,17195,17331,17396,17482,17552,17624,17687,17761,17883,17956,18199,19150,19215,20288,20435,20525,24490,24604,25651,26344,26395,26534,27276,27348,28179,28295,29038,29139", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "15848,16113,16373,16539,16608,16669,17190,17254,17391,17477,17547,17619,17682,17756,17820,17951,18015,18282,19210,19296,20359,20520,20616,24599,24680,25697,26390,26476,26593,27343,27417,28290,28384,29134,29232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "300", "startColumns": "4", "startOffsets": "27636", "endColumns": "91", "endOffsets": "27723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,7711,7947,8429,8678,8741,8859,8920,8985,9042,9112,9173,9227,9343,9400,9462,9516,9590,9718,9806,9892,10029,10113,10198,10332,10423,10499,10553,10604,10670,10742,10820,10916,10998,11078,11154,11231,11308,11415,11504,11577,11667,11762,11836,11917,12010,12065,12146,12212,12298,12383,12445,12509,12572,12644,12742,12841,12936,13028,13086,13529", "endLines": "7,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,7772,8041,8501,8736,8854,8915,8980,9037,9107,9168,9222,9338,9395,9457,9511,9585,9713,9801,9887,10024,10108,10193,10327,10418,10494,10548,10599,10665,10737,10815,10911,10993,11073,11149,11226,11303,11410,11499,11572,11662,11757,11831,11912,12005,12060,12141,12207,12293,12378,12440,12504,12567,12639,12737,12836,12931,13023,13081,13136,13604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,249,391,495,570,689,867,1080,1230,1321,1419,1514,1610,1813,1926,2023,2273,2371,2463,2525,2593,2684,2777,2881,3096,3170,3254,3339,3410,3494,3568,3645,3793,3885,3983,4057,4126,4189,4293,4428,4539,4673,4766,4837,4910,5007,5080,5210,5328", "endColumns": "89,103,141,103,74,118,177,212,149,90,97,94,95,202,112,96,249,97,91,61,67,90,92,103,214,73,83,84,70,83,73,76,147,91,97,73,68,62,103,134,110,133,92,70,72,96,72,129,117,95", "endOffsets": "140,244,386,490,565,684,862,1075,1225,1316,1414,1509,1605,1808,1921,2018,2268,2366,2458,2520,2588,2679,2772,2876,3091,3165,3249,3334,3405,3489,3563,3640,3788,3880,3978,4052,4121,4184,4288,4423,4534,4668,4761,4832,4905,5002,5075,5205,5323,5419"}, "to": {"startLines": "171,250,267,277,278,327,333,334,335,336,338,339,340,341,342,343,344,345,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,367,368,369,370,371,372,373,374,375,376,377,397,405,406,407,408,409", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15334,22709,24739,25751,25855,30287,30772,30950,31163,31313,31511,31609,31704,31800,32003,32116,32213,32463,32662,32754,32816,32884,32975,33068,33172,33387,33461,33545,33630,33701,33785,33859,33936,34560,34652,34750,34824,34893,34956,35060,35195,35306,35440,35533,38571,39144,39241,39314,39444,39562", "endColumns": "89,103,141,103,74,118,177,212,149,90,97,94,95,202,112,96,249,97,91,61,67,90,92,103,214,73,83,84,70,83,73,76,147,91,97,73,68,62,103,134,110,133,92,70,72,96,72,129,117,95", "endOffsets": "15419,22808,24876,25850,25925,30401,30945,31158,31308,31399,31604,31699,31795,31998,32111,32208,32458,32556,32749,32811,32879,32970,33063,33167,33382,33456,33540,33625,33696,33780,33854,33931,34079,34647,34745,34819,34888,34951,35055,35190,35301,35435,35528,35599,38639,39236,39309,39439,39557,39653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15853,16674,16819,16883,16968,17259,17825,18020,18287,18639,19005,19301", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "15928,16729,16878,16963,17028,17326,17878,18092,18359,18702,19060,19370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,442,488,555,654,729,997,1057,1149,1228,1282,1346,1683,1756,1824,1877,1930,2009,2124,2235,2302,2381,2463,2547,2632,2751,3045,3134,3211,3293,3353,3418,3478,3585,3686,3815,3913,3987,4066,4162,4349,4560,4691,4754,5461,5525", "endColumns": "174,211,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,106,100,128,97,73,78,95,186,210,130,62,706,63,65", "endOffsets": "225,437,483,550,649,724,992,1052,1144,1223,1277,1341,1678,1751,1819,1872,1925,2004,2119,2230,2297,2376,2458,2542,2627,2746,3040,3129,3206,3288,3348,3413,3473,3580,3681,3810,3908,3982,4061,4157,4344,4555,4686,4749,5456,5520,5586"}, "to": {"startLines": "238,239,243,244,245,246,247,248,249,263,266,268,274,279,280,287,297,301,302,303,304,305,311,314,319,321,322,323,324,326,328,329,332,337,346,362,363,364,365,366,378,384,385,386,388,389,404", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20737,20912,22002,22048,22115,22214,22289,22557,22617,24411,24685,24881,25314,25930,26003,26481,27422,27728,27807,27922,28033,28100,28708,28954,29402,29569,29688,29982,30071,30205,30406,30466,30712,31404,32561,34084,34213,34311,34385,34464,35604,36181,36392,36523,36675,37382,39078", "endColumns": "174,211,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,106,100,128,97,73,78,95,186,210,130,62,706,63,65", "endOffsets": "20907,21119,22043,22110,22209,22284,22552,22612,22704,24485,24734,24940,25646,25998,26066,26529,27470,27802,27917,28028,28095,28174,28785,29033,29482,29683,29977,30066,30143,30282,30461,30526,30767,31506,32657,34208,34306,34380,34459,34555,35786,36387,36518,36581,37377,37441,39139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,14079", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,14175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,433,434,435", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,7338,7436,7619,8506,8588,13141,13229,13388,13459,13754,13838,14007,41966,42050,42120", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4718,4801,7431,7533,7706,8583,8673,13224,13306,13454,13524,13833,13920,14074,42045,42115,42238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4806,7538,7869", "endColumns": "76,80,77", "endOffsets": "4878,7614,7942"}}]}]}