{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-el-rGR/values-el-rGR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,223,358,424,519,579,639,725,829,1006,1307,1638,1712,1787,1876,1971,2121,2282,2357,2440,2609,2677,2738,2812,2893,3030,3173,3296,3394,3473,3567,3638", "endColumns": "83,83,134,65,94,59,59,85,103,176,300,330,73,74,88,94,149,160,74,82,168,67,60,73,80,136,142,122,97,78,93,70,170", "endOffsets": "134,218,353,419,514,574,634,720,824,1001,1302,1633,1707,1782,1871,1966,2116,2277,2352,2435,2604,2672,2733,2807,2888,3025,3168,3291,3389,3468,3562,3633,3804"}, "to": {"startLines": "16,18,153,170,224,235,236,237,238,239,240,241,255,256,257,258,259,260,261,262,264,265,266,267,268,269,270,271,272,273,274,275,276", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1096,1268,14738,16576,22307,24103,24163,24223,24309,24413,24590,24891,26318,26392,26467,26556,26651,26801,26962,27037,27203,27372,27440,27501,27575,27656,27793,27936,28059,28157,28236,28330,28401", "endColumns": "83,83,134,65,94,59,59,85,103,176,300,330,73,74,88,94,149,160,74,82,168,67,60,73,80,136,142,122,97,78,93,70,170", "endOffsets": "1175,1347,14868,16637,22397,24158,24218,24304,24408,24585,24886,25217,26387,26462,26551,26646,26796,26957,27032,27115,27367,27435,27496,27570,27651,27788,27931,28054,28152,28231,28325,28396,28567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,197,259,341,406,478,538,618,700,766,830", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "133,192,254,336,401,473,533,613,695,761,825,904"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1729,2560,2701,2763,2845,3133,3676,3874,4135,4503,4867,5167", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "1807,2614,2758,2840,2905,3200,3731,3949,4212,4564,4926,5241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,430,475,550,657,739,1012,1075,1170,1239,1297,1361,1697,1774,1840,1899,1952,2048,2207,2328,2385,2475,2566,2653,2731,2832,3159,3239,3317,3382,3445,3509,3572,3672,3771,3869,3961,4035,4114,4200,4407,4618,4736,4795,5657,5722", "endColumns": "153,220,44,74,106,81,272,62,94,68,57,63,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,99,98,97,91,73,78,85,206,210,117,58,861,64,65", "endOffsets": "204,425,470,545,652,734,1007,1070,1165,1234,1292,1356,1692,1769,1835,1894,1947,2043,2202,2323,2380,2470,2561,2648,2726,2827,3154,3234,3312,3377,3440,3504,3567,3667,3766,3864,3956,4030,4109,4195,4402,4613,4731,4790,5652,5717,5783"}, "to": {"startLines": "84,85,89,90,91,92,93,94,95,109,112,114,120,125,126,133,143,146,147,148,149,150,156,159,164,166,167,168,169,171,173,174,177,182,191,207,208,209,210,211,223,229,230,231,233,234,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6698,6852,7999,8044,8119,8226,8308,8581,8644,10664,10948,11160,11605,12234,12311,12833,13811,14023,14119,14278,14399,14456,15060,15351,15823,15990,16091,16418,16498,16642,16824,16887,17136,17848,19085,20615,20713,20805,20879,20958,22100,22720,22931,23049,23176,24038,25764", "endColumns": "153,220,44,74,106,81,272,62,94,68,57,63,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,99,98,97,91,73,78,85,206,210,117,58,861,64,65", "endOffsets": "6847,7068,8039,8114,8221,8303,8576,8639,8734,10728,11001,11219,11936,12306,12372,12887,13859,14114,14273,14394,14451,14541,15146,15433,15896,16086,16413,16493,16571,16702,16882,16946,17194,17943,19179,20708,20800,20874,20953,21039,22302,22926,23044,23103,24033,24098,25825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,208,270,344,427,497,557,612,693,780,867,973,1079,1170,1252,1334,1422,1525,1630,1709,1811,1900,1993,2109,2196,2298,2384,2493,2574,2675,2784,2889,2977,3088,3168,3254,3360,3438,3504,4286,5044,5126,5251,5360,5423,5526,5631,5720,5841,5957,6015,6101,6151,6231,6340,6413,6482,6532,6615,6721,6767,6819,6890,6948,7007,7205,7383,7520,7592,7679,7756,7866,7965,8066,8151,8245,8334,8419,8519,8572,8729,8780,8837,8905,8973,9051,9128,9201,9291,9361", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,49,82,105,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,84,99,52,156,50,56,67,67,77,76,72,89,69,82", "endOffsets": "122,203,265,339,422,492,552,607,688,775,862,968,1074,1165,1247,1329,1417,1520,1625,1704,1806,1895,1988,2104,2191,2293,2379,2488,2569,2670,2779,2884,2972,3083,3163,3249,3355,3433,3499,4281,5039,5121,5246,5355,5418,5521,5626,5715,5836,5952,6010,6096,6146,6226,6335,6408,6477,6527,6610,6716,6762,6814,6885,6943,7002,7200,7378,7515,7587,7674,7751,7861,7960,8061,8146,8240,8329,8414,8514,8567,8724,8775,8832,8900,8968,9046,9123,9196,9286,9356,9439"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,86,87,88,97,98,99,100,101,102,103,104,105,106,107,108,115,116,117,118,119,122,127,128,129,130,135,136,137,138,139,140,144,145,154,155,157,158,162,163,165,175,176,225,226,227,228,232,243,244,245,246,247,248,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "594,666,747,809,883,966,1036,1352,1407,1488,1575,1812,2001,2107,2266,2619,2910,3954,4217,4322,4401,4569,4658,4751,4931,5246,5348,5434,5543,5624,5725,5834,5939,6027,6138,6298,6592,7073,7151,7217,8849,9607,9689,9814,9923,9986,10089,10194,10283,10404,10520,10578,11224,11274,11354,11463,11536,11990,12377,12460,12566,12612,12956,13027,13085,13144,13342,13520,13864,13936,14873,14950,15151,15250,15644,15729,15901,16951,17036,22402,22455,22612,22663,23108,25308,25376,25454,25531,25604,25694,27120", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,49,82,105,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,84,99,52,156,50,56,67,67,77,76,72,89,69,82", "endOffsets": "661,742,804,878,961,1031,1091,1402,1483,1570,1657,1913,2102,2193,2343,2696,2993,4052,4317,4396,4498,4653,4746,4862,5013,5343,5429,5538,5619,5720,5829,5934,6022,6133,6213,6379,6693,7146,7212,7994,9602,9684,9809,9918,9981,10084,10189,10278,10399,10515,10573,10659,11269,11349,11458,11531,11600,12035,12455,12561,12607,12659,13022,13080,13139,13337,13515,13652,13931,14018,14945,15055,15245,15346,15724,15818,15985,17031,17131,22450,22607,22658,22715,23171,25371,25449,25526,25599,25689,25759,27198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,138,242,309,377,442,525", "endColumns": "82,103,66,67,64,82,68", "endOffsets": "133,237,304,372,437,520,589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,253,407,507,601,718,895,1110,1276,1367,1475,1576,1677,1870,1986,2092,2390,2504,2599,2662,2730,2823,2922,3039,3256,3329,3416,3500,3572,3661,3734,3805,3935,4022,4128,4204,4274,4338,4445,4571,4680,4817,4919,4991,5077,5166,5237,5356,5479", "endColumns": "87,109,153,99,93,116,176,214,165,90,107,100,100,192,115,105,297,113,94,62,67,92,98,116,216,72,86,83,71,88,72,70,129,86,105,75,69,63,106,125,108,136,101,71,85,88,70,118,122,85", "endOffsets": "138,248,402,502,596,713,890,1105,1271,1362,1470,1571,1672,1865,1981,2087,2385,2499,2594,2657,2725,2818,2917,3034,3251,3324,3411,3495,3567,3656,3729,3800,3930,4017,4123,4199,4269,4333,4440,4566,4675,4812,4914,4986,5072,5161,5232,5351,5474,5560"}, "to": {"startLines": "17,96,113,123,124,172,178,179,180,181,183,184,185,186,187,188,189,190,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,212,213,214,215,216,217,218,219,220,221,222,242,250,251,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1180,8739,11006,12040,12140,16707,17199,17376,17591,17757,17948,18056,18157,18258,18451,18567,18673,18971,19184,19279,19342,19410,19503,19602,19719,19936,20009,20096,20180,20252,20341,20414,20485,21044,21131,21237,21313,21383,21447,21554,21680,21789,21926,22028,25222,25830,25919,25990,26109,26232", "endColumns": "87,109,153,99,93,116,176,214,165,90,107,100,100,192,115,105,297,113,94,62,67,92,98,116,216,72,86,83,71,88,72,70,129,86,105,75,69,63,106,125,108,136,101,71,85,88,70,118,122,85", "endOffsets": "1263,8844,11155,12135,12229,16819,17371,17586,17752,17843,18051,18152,18253,18446,18562,18668,18966,19080,19274,19337,19405,19498,19597,19714,19931,20004,20091,20175,20247,20336,20409,20480,20610,21126,21232,21308,21378,21442,21549,21675,21784,21921,22023,22095,25303,25914,25985,26104,26227,26313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,205,273,356,424,485,551,620,685,766,837,897,958,1027,1091,1162,1229,1307,1372,1456,1536,1637,1744,1865,1959,2008,2085,2177,2241,2323,2395,2502,2587,2691", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "117,200,268,351,419,480,546,615,680,761,832,892,953,1022,1086,1157,1224,1302,1367,1451,1531,1632,1739,1860,1954,2003,2080,2172,2236,2318,2390,2497,2582,2686,2788"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,110,111,121,131,132,134,141,142,151,152,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1662,1918,2198,2348,2431,2499,2998,3064,3205,3270,3351,3422,3482,3543,3612,3736,3807,4057,5018,5083,6218,6384,6485,10733,10854,11941,12664,12741,12892,13657,13739,14546,14653,15438,15542", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "1724,1996,2261,2426,2494,2555,3059,3128,3265,3346,3417,3477,3538,3607,3671,3802,3869,4130,5078,5162,6293,6480,6587,10849,10943,11985,12736,12828,12951,13734,13806,14648,14733,15537,15639"}}]}]}