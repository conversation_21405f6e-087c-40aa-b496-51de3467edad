import 'package:equatable/equatable.dart';

/// نوع المنشأة
enum PropertyType {
  residential, // سكنية
  commercial,  // تجارية
}

/// مراحل البناء
enum ConstructionPhase {
  blackWork,    // مرحلة الأسود / الأعمال المدنية
  civilWork,    // الأعمال المدنية
  finishing,    // أعمال التشطيب
}

/// حالة المشروع
enum ProjectStatus {
  planning,     // تخطيط
  inProgress,   // قيد التنفيذ
  completed,    // مكتمل
  onHold,       // متوقف
}

/// كيان المشروع العقاري
class ProjectEntity extends Equatable {
  /// معرف المشروع
  final String id;

  /// اسم المشروع
  final String name;

  /// وصف المشروع
  final String? description;

  /// نوع المنشأة
  final PropertyType propertyType;

  /// المرحلة الحالية
  final ConstructionPhase currentPhase;

  /// حالة المشروع
  final ProjectStatus status;

  /// موقع المشروع
  final String? location;

  /// معرف المالك
  final String ownerId;

  /// اسم المالك
  final String ownerName;

  /// معرف المشرف
  final String? supervisorId;

  /// اسم المشرف
  final String? supervisorName;

  /// تاريخ البدء
  final DateTime? startDate;

  /// تاريخ الانتهاء المتوقع
  final DateTime? expectedEndDate;

  /// تاريخ الانتهاء الفعلي
  final DateTime? actualEndDate;

  /// الميزانية الإجمالية
  final double? totalBudget;

  /// التكلفة المنفقة
  final double? spentAmount;

  /// صورة المشروع الرئيسية
  final String? mainImageUrl;

  /// صور إضافية
  final List<String>? additionalImages;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// Constructor
  const ProjectEntity({
    required this.id,
    required this.name,
    this.description,
    required this.propertyType,
    required this.currentPhase,
    this.status = ProjectStatus.planning,
    this.location,
    required this.ownerId,
    required this.ownerName,
    this.supervisorId,
    this.supervisorName,
    this.startDate,
    this.expectedEndDate,
    this.actualEndDate,
    this.totalBudget,
    this.spentAmount,
    this.mainImageUrl,
    this.additionalImages,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة محدثة من المشروع
  ProjectEntity copyWith({
    String? id,
    String? name,
    String? description,
    PropertyType? propertyType,
    ConstructionPhase? currentPhase,
    ProjectStatus? status,
    String? location,
    String? ownerId,
    String? ownerName,
    String? supervisorId,
    String? supervisorName,
    DateTime? startDate,
    DateTime? expectedEndDate,
    DateTime? actualEndDate,
    double? totalBudget,
    double? spentAmount,
    String? mainImageUrl,
    List<String>? additionalImages,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ProjectEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      propertyType: propertyType ?? this.propertyType,
      currentPhase: currentPhase ?? this.currentPhase,
      status: status ?? this.status,
      location: location ?? this.location,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      supervisorId: supervisorId ?? this.supervisorId,
      supervisorName: supervisorName ?? this.supervisorName,
      startDate: startDate ?? this.startDate,
      expectedEndDate: expectedEndDate ?? this.expectedEndDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      totalBudget: totalBudget ?? this.totalBudget,
      spentAmount: spentAmount ?? this.spentAmount,
      mainImageUrl: mainImageUrl ?? this.mainImageUrl,
      additionalImages: additionalImages ?? this.additionalImages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        propertyType,
        currentPhase,
        status,
        location,
        ownerId,
        ownerName,
        supervisorId,
        supervisorName,
        startDate,
        expectedEndDate,
        actualEndDate,
        totalBudget,
        spentAmount,
        mainImageUrl,
        additionalImages,
        createdAt,
        updatedAt,
        metadata,
      ];
}

/// امتدادات مساعدة لنوع المنشأة
extension PropertyTypeExtension on PropertyType {
  String get nameAr {
    switch (this) {
      case PropertyType.residential:
        return 'سكنية';
      case PropertyType.commercial:
        return 'تجارية';
    }
  }

  String get nameEn {
    switch (this) {
      case PropertyType.residential:
        return 'Residential';
      case PropertyType.commercial:
        return 'Commercial';
    }
  }
}

/// امتدادات مساعدة لمراحل البناء
extension ConstructionPhaseExtension on ConstructionPhase {
  String get nameAr {
    switch (this) {
      case ConstructionPhase.blackWork:
        return 'مرحلة الأسود';
      case ConstructionPhase.civilWork:
        return 'الأعمال المدنية';
      case ConstructionPhase.finishing:
        return 'أعمال التشطيب';
    }
  }

  String get nameEn {
    switch (this) {
      case ConstructionPhase.blackWork:
        return 'Black Work';
      case ConstructionPhase.civilWork:
        return 'Civil Work';
      case ConstructionPhase.finishing:
        return 'Finishing Work';
    }
  }
}

/// امتدادات مساعدة لحالة المشروع
extension ProjectStatusExtension on ProjectStatus {
  String get nameAr {
    switch (this) {
      case ProjectStatus.planning:
        return 'تخطيط';
      case ProjectStatus.inProgress:
        return 'قيد التنفيذ';
      case ProjectStatus.completed:
        return 'مكتمل';
      case ProjectStatus.onHold:
        return 'متوقف';
    }
  }

  String get nameEn {
    switch (this) {
      case ProjectStatus.planning:
        return 'Planning';
      case ProjectStatus.inProgress:
        return 'In Progress';
      case ProjectStatus.completed:
        return 'Completed';
      case ProjectStatus.onHold:
        return 'On Hold';
    }
  }
}
