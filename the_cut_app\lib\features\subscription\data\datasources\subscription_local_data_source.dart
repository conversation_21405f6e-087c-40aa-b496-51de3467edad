import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/error/exceptions.dart';
import '../models/country_model.dart';
import '../models/subscriber_model.dart';
import '../models/subscription_plan_model.dart';
import '../models/transaction_record_model.dart';

/// Interface for the subscription local data source
abstract class SubscriptionLocalDataSource {
  /// Get the last cached subscription plans
  Future<List<SubscriptionPlanModel>> getLastSubscriptionPlans();

  /// Cache subscription plans
  Future<void> cacheSubscriptionPlans(List<SubscriptionPlanModel> plans);

  /// Get the last cached subscription plans for a specific country
  Future<List<SubscriptionPlanModel>> getLastSubscriptionPlansByCountry(String countryCode);

  /// Cache subscription plans for a specific country
  Future<void> cacheSubscriptionPlansByCountry(
      String countryCode, List<SubscriptionPlanModel> plans);

  /// Get a specific subscription plan by ID
  Future<SubscriptionPlanModel> getSubscriptionPlanById(String id);

  /// Cache a specific subscription plan
  Future<void> cacheSubscriptionPlan(SubscriptionPlanModel plan);

  /// Get the last cached current subscriber for a user
  Future<SubscriberModel?> getLastCurrentSubscriber(String userId);

  /// Cache the current subscriber for a user
  Future<void> cacheCurrentSubscriber(SubscriberModel subscriber);

  /// Get the last cached transaction history for a user
  Future<List<TransactionRecordModel>> getLastTransactionHistory(String userId);

  /// Cache the transaction history for a user
  Future<void> cacheTransactionHistory(
      String userId, List<TransactionRecordModel> transactions);

  /// Get the last cached supported countries
  Future<List<CountryModel>> getLastSupportedCountries();

  /// Cache supported countries
  Future<void> cacheSupportedCountries(List<CountryModel> countries);

  /// Get a specific country by code
  Future<CountryModel> getCountryByCode(String countryCode);

  /// Cache a specific country
  Future<void> cacheCountry(CountryModel country);

  /// Get the last cached exchange rates
  Future<Map<String, double>> getLastExchangeRates();

  /// Cache exchange rates
  Future<void> cacheExchangeRates(Map<String, double> exchangeRates);
}

/// Implementation of the subscription local data source
class SubscriptionLocalDataSourceImpl implements SubscriptionLocalDataSource {
  final SharedPreferences sharedPreferences;

  /// Constructor
  SubscriptionLocalDataSourceImpl({required this.sharedPreferences});

  /// Keys for shared preferences
  static const String cachedSubscriptionPlansKey = 'CACHED_SUBSCRIPTION_PLANS';
  static const String cachedSubscriberKey = 'CACHED_SUBSCRIBER_';
  static const String cachedTransactionHistoryKey = 'CACHED_TRANSACTION_HISTORY_';
  static const String cachedSupportedCountriesKey = 'CACHED_SUPPORTED_COUNTRIES';
  static const String cachedCountryKey = 'CACHED_COUNTRY_';
  static const String cachedExchangeRatesKey = 'CACHED_EXCHANGE_RATES';

  @override
  Future<List<SubscriptionPlanModel>> getLastSubscriptionPlans() async {
    final jsonString = sharedPreferences.getString(cachedSubscriptionPlansKey);
    if (jsonString != null) {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList
          .map((jsonMap) => SubscriptionPlanModel.fromJson(jsonMap))
          .toList();
    } else {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheSubscriptionPlans(List<SubscriptionPlanModel> plans) async {
    final List<Map<String, dynamic>> jsonList = plans.map((plan) => plan.toJson()).toList();
    await sharedPreferences.setString(
        cachedSubscriptionPlansKey, json.encode(jsonList));
  }

  @override
  Future<List<SubscriptionPlanModel>> getLastSubscriptionPlansByCountry(
      String countryCode) async {
    final jsonString = sharedPreferences.getString('$cachedSubscriptionPlansKey$countryCode');
    if (jsonString != null) {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList
          .map((jsonMap) => SubscriptionPlanModel.fromJson(jsonMap))
          .toList();
    } else {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheSubscriptionPlansByCountry(
      String countryCode, List<SubscriptionPlanModel> plans) async {
    final List<Map<String, dynamic>> jsonList = plans.map((plan) => plan.toJson()).toList();
    await sharedPreferences.setString(
        '$cachedSubscriptionPlansKey$countryCode', json.encode(jsonList));
  }

  @override
  Future<SubscriptionPlanModel> getSubscriptionPlanById(String id) async {
    final jsonString = sharedPreferences.getString('$cachedSubscriptionPlansKey$id');
    if (jsonString != null) {
      return SubscriptionPlanModel.fromJson(json.decode(jsonString));
    } else {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheSubscriptionPlan(SubscriptionPlanModel plan) async {
    await sharedPreferences.setString(
        '$cachedSubscriptionPlansKey${plan.id}', json.encode(plan.toJson()));
  }

  // Implementation of other methods would go here
  // For brevity, they are not included in this example
  
  @override
  Future<SubscriberModel?> getLastCurrentSubscriber(String userId) {
    // TODO: implement getLastCurrentSubscriber
    throw UnimplementedError();
  }
  
  @override
  Future<void> cacheCurrentSubscriber(SubscriberModel subscriber) {
    // TODO: implement cacheCurrentSubscriber
    throw UnimplementedError();
  }
  
  @override
  Future<List<TransactionRecordModel>> getLastTransactionHistory(String userId) {
    // TODO: implement getLastTransactionHistory
    throw UnimplementedError();
  }
  
  @override
  Future<void> cacheTransactionHistory(String userId, List<TransactionRecordModel> transactions) {
    // TODO: implement cacheTransactionHistory
    throw UnimplementedError();
  }
  
  @override
  Future<List<CountryModel>> getLastSupportedCountries() {
    // TODO: implement getLastSupportedCountries
    throw UnimplementedError();
  }
  
  @override
  Future<void> cacheSupportedCountries(List<CountryModel> countries) {
    // TODO: implement cacheSupportedCountries
    throw UnimplementedError();
  }
  
  @override
  Future<CountryModel> getCountryByCode(String countryCode) {
    // TODO: implement getCountryByCode
    throw UnimplementedError();
  }
  
  @override
  Future<void> cacheCountry(CountryModel country) {
    // TODO: implement cacheCountry
    throw UnimplementedError();
  }
  
  @override
  Future<Map<String, double>> getLastExchangeRates() {
    // TODO: implement getLastExchangeRates
    throw UnimplementedError();
  }
  
  @override
  Future<void> cacheExchangeRates(Map<String, double> exchangeRates) {
    // TODO: implement cacheExchangeRates
    throw UnimplementedError();
  }
}
