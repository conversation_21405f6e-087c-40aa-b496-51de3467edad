import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/font_service.dart';
import '../../domain/entities/country.dart';

/// Widget to select a country
class CountrySelector extends StatelessWidget {
  /// List of countries
  final List<Country> countries;

  /// Selected country
  final Country selectedCountry;

  /// Callback when a country is selected
  final ValueChanged<Country> onCountrySelected;

  /// Constructor
  const CountrySelector({
    super.key,
    required this.countries,
    required this.selectedCountry,
    required this.onCountrySelected,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final fontService = FontService.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.translate('select_country'),
          style: GoogleFonts.getFont(
            fontService.fontFamily,
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Country>(
              value: selectedCountry,
              isExpanded: true,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              borderRadius: BorderRadius.circular(8.r),
              icon: Icon(
                isRtl ? Icons.keyboard_arrow_left : Icons.keyboard_arrow_right,
                color: AppColors.textSecondary,
              ),
              items: countries.map((country) {
                return DropdownMenuItem<Country>(
                  value: country,
                  child: Row(
                    children: [
                      Text(
                        country.flagEmoji,
                        style: TextStyle(fontSize: 20.sp),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          isRtl ? country.nameAr : country.name,
                          style: GoogleFonts.getFont(
                            fontService.fontFamily,
                            fontSize: 14.sp,
                            color: AppColors.textPrimary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '(${country.currencyCode})',
                        style: GoogleFonts.getFont(
                          fontService.fontFamily,
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (country) {
                if (country != null) {
                  onCountrySelected(country);
                }
              },
            ),
          ),
        ),
        SizedBox(height: 16.h),
        // Currency info
        Row(
          children: [
            Icon(
              Icons.info_outline,
              size: 16.w,
              color: AppColors.textSecondary,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                localizations.translate('prices_in_local_currency'),
                style: GoogleFonts.getFont(
                  fontService.fontFamily,
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
