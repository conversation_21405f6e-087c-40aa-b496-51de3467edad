import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/font_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/country.dart';
import '../../domain/entities/subscription_plan.dart';
import '../bloc/subscription_bloc.dart';
import '../widgets/payment_method_selector.dart';

/// Screen to handle payment for subscription
class PaymentScreen extends StatefulWidget {
  /// Subscription plan
  final SubscriptionPlan plan;

  /// Country
  final Country country;

  /// Constructor
  const PaymentScreen({
    super.key,
    required this.plan,
    required this.country,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  String _selectedPaymentMethod = 'credit_card';
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _cardHolderController = TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _cvvController = TextEditingController();

  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final fontService = FontService.of(context);
    final authService = Provider.of<AuthService>(context);
    final localizedPrice =
        (widget.plan.price * widget.country.exchangeRate).toStringAsFixed(2);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('payment')),
      ),
      body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state is SubscribedToPlan) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text(localizations.translate('subscription_successful')),
                backgroundColor: AppColors.success,
              ),
            );
            Navigator.popUntil(context, ModalRoute.withName('/home'));
          } else if (state is SubscriptionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is SubscribingToPlan) {
            return const LoadingIndicator();
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order summary
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.translate('order_summary'),
                            style: GoogleFonts.getFont(
                              fontService.fontFamily,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          _buildSummaryRow(
                            localizations.translate('plan'),
                            widget.plan.name,
                            fontService,
                          ),
                          _buildSummaryRow(
                            localizations.translate('duration'),
                            '${widget.plan.durationMonths} ${localizations.translate('months')}',
                            fontService,
                          ),
                          _buildSummaryRow(
                            localizations.translate('price'),
                            '${widget.country.currencySymbol}$localizedPrice',
                            fontService,
                          ),
                          if (widget.plan.discountPercentage > 0)
                            _buildSummaryRow(
                              localizations.translate('discount'),
                              '${widget.plan.discountPercentage.toStringAsFixed(0)}%',
                              fontService,
                              valueColor: AppColors.success,
                            ),
                          Divider(height: 24.h),
                          _buildSummaryRow(
                            localizations.translate('total'),
                            '${widget.country.currencySymbol}$localizedPrice',
                            fontService,
                            isBold: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 24.h),

                  // Payment method
                  Text(
                    localizations.translate('payment_method'),
                    style: GoogleFonts.getFont(
                      fontService.fontFamily,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  PaymentMethodSelector(
                    supportedMethods: widget.country.supportedPaymentMethods,
                    selectedMethod: _selectedPaymentMethod,
                    onMethodSelected: (method) {
                      setState(() {
                        _selectedPaymentMethod = method;
                      });
                    },
                  ),
                  SizedBox(height: 24.h),

                  // Credit card form
                  if (_selectedPaymentMethod == 'credit_card') ...[
                    Text(
                      localizations.translate('card_details'),
                      style: GoogleFonts.getFont(
                        fontService.fontFamily,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: _cardNumberController,
                      labelText: localizations.translate('card_number'),
                      hintText: 'XXXX XXXX XXXX XXXX',
                      prefixIcon: Icons.credit_card,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations
                              .translate('please_enter_card_number');
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    CustomTextField(
                      controller: _cardHolderController,
                      labelText: localizations.translate('card_holder_name'),
                      hintText:
                          localizations.translate('card_holder_name_hint'),
                      prefixIcon: Icons.person,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations
                              .translate('please_enter_card_holder_name');
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.h),
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            controller: _expiryDateController,
                            labelText: localizations.translate('expiry_date'),
                            hintText: 'MM/YY',
                            prefixIcon: Icons.calendar_today,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return localizations
                                    .translate('please_enter_expiry_date');
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: CustomTextField(
                            controller: _cvvController,
                            labelText: localizations.translate('cvv'),
                            hintText: 'XXX',
                            prefixIcon: Icons.lock,
                            keyboardType: TextInputType.number,
                            obscureText: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return localizations
                                    .translate('please_enter_cvv');
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                  SizedBox(height: 32.h),

                  // Pay button
                  CustomButton(
                    text: localizations.translate('pay_now'),
                    onPressed: () => _processPayment(context, authService),
                    isLoading: state is SubscribingToPlan,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value,
    FontService fontService, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.getFont(
              fontService.fontFamily,
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.getFont(
              fontService.fontFamily,
              fontSize: 14.sp,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: valueColor ?? AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _processPayment(BuildContext context, AuthService authService) {
    if (_formKey.currentState!.validate()) {
      final userId = authService.currentUser?.id;
      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(AppLocalizations.of(context).translate('login_required')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Extract card details if using credit card
      String? cardToken;
      String? cardLast4;
      String? cardBrand;
      int? cardExpiryMonth;
      int? cardExpiryYear;

      if (_selectedPaymentMethod == 'credit_card') {
        // In a real app, you would tokenize the card details using a payment processor
        cardToken = 'tok_visa';
        cardLast4 = _cardNumberController.text
            .substring(_cardNumberController.text.length - 4);
        cardBrand = 'visa';

        final expiryParts = _expiryDateController.text.split('/');
        if (expiryParts.length == 2) {
          cardExpiryMonth = int.tryParse(expiryParts[0]);
          cardExpiryYear = int.tryParse(expiryParts[1]);
          if (cardExpiryYear != null && cardExpiryYear < 100) {
            cardExpiryYear += 2000;
          }
        }
      }

      context.read<SubscriptionBloc>().add(
            SubscribeToPlanEvent(
              userId: userId,
              subscriptionPlanId: widget.plan.id,
              paymentMethod: _selectedPaymentMethod,
              countryCode: widget.country.code,
              currencyCode: widget.country.currencyCode,
              cardToken: cardToken,
              cardLast4: cardLast4,
              cardBrand: cardBrand,
              cardExpiryMonth: cardExpiryMonth,
              cardExpiryYear: cardExpiryYear,
            ),
          );
    }
  }
}
