{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,227,343,432,525,634,799,992,1139,1227,1318,1413,1508,1700,1800,1897,2129,2222,2310,2370,2435,2513,2611,2710,2915,2984,3066,3150,3215,3295,3366,3441,3565,3654,3746,3820,3888,3950,4048,4161,4266,4397,4485,4559,4634,4722,4790,4893,5006", "endColumns": "78,92,115,88,92,108,164,192,146,87,90,94,94,191,99,96,231,92,87,59,64,77,97,98,204,68,81,83,64,79,70,74,123,88,91,73,67,61,97,112,104,130,87,73,74,87,67,102,112,85", "endOffsets": "129,222,338,427,520,629,794,987,1134,1222,1313,1408,1503,1695,1795,1892,2124,2217,2305,2365,2430,2508,2606,2705,2910,2979,3061,3145,3210,3290,3361,3436,3560,3649,3741,3815,3883,3945,4043,4156,4261,4392,4480,4554,4629,4717,4785,4888,5001,5087"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14843,21827,23727,24666,24755,28955,29422,29587,29780,29927,30113,30204,30299,30394,30586,30686,30783,31015,31203,31291,31351,31416,31494,31592,31691,31896,31965,32047,32131,32196,32276,32347,32422,32978,33067,33159,33233,33301,33363,33461,33574,33679,33810,33898,36687,37245,37333,37401,37504,37617", "endColumns": "78,92,115,88,92,108,164,192,146,87,90,94,94,191,99,96,231,92,87,59,64,77,97,98,204,68,81,83,64,79,70,74,123,88,91,73,67,61,97,112,104,130,87,73,74,87,67,102,112,85", "endOffsets": "14917,21915,23838,24750,24843,29059,29582,29775,29922,30010,30199,30294,30389,30581,30681,30778,31010,31103,31286,31346,31411,31489,31587,31686,31891,31960,32042,32126,32191,32271,32342,32417,32541,33062,33154,33228,33296,33358,33456,33569,33674,33805,33893,33967,36757,37328,37396,37499,37612,37698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,401,450,516,613,683,924,994,1093,1159,1215,1279,1557,1625,1688,1743,1798,1872,1990,2098,2157,2244,2319,2410,2484,2584,2838,2917,2994,3062,3122,3184,3246,3344,3439,3539,3632,3707,3786,3871,4055,4250,4366,4426,5069,5131", "endColumns": "159,185,48,65,96,69,240,69,98,65,55,63,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,97,94,99,92,74,78,84,183,194,115,59,642,61,58", "endOffsets": "210,396,445,511,608,678,919,989,1088,1154,1210,1274,1552,1620,1683,1738,1793,1867,1985,2093,2152,2239,2314,2405,2479,2579,2833,2912,2989,3057,3117,3179,3241,3339,3434,3534,3627,3702,3781,3866,4050,4245,4361,4421,5064,5126,5185"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20060,20220,21135,21184,21250,21347,21417,21658,21728,23418,23671,23843,24292,24848,24916,25368,26246,26538,26612,26730,26838,26897,27477,27714,28153,28304,28404,28658,28737,28887,29064,29124,29360,30015,31108,32546,32646,32739,32814,32893,33972,34539,34734,34850,34979,35622,37186", "endColumns": "159,185,48,65,96,69,240,69,98,65,55,63,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,97,94,99,92,74,78,84,183,194,115,59,642,61,58", "endOffsets": "20215,20401,21179,21245,21342,21412,21653,21723,21822,23479,23722,23902,24565,24911,24974,25418,26296,26607,26725,26833,26892,26979,27547,27800,28222,28399,28653,28732,28809,28950,29119,29181,29417,30108,31198,32641,32734,32809,32888,32973,34151,34729,34845,34905,35617,35679,37240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,13648", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,13744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8023,40331", "endColumns": "60,79", "endOffsets": "8079,40406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4593,7217,7542", "endColumns": "79,78,78", "endOffsets": "4668,7291,7616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,7017,7116,7296,8159,8236,12733,12824,12984,13050,13332,13413,13576,39888,39965,40037", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4503,4588,7111,7212,7380,8231,8324,12819,12901,13045,13113,13408,13490,13643,39960,40032,40154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13749,13833,13933,13999,14069,14134,14209", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "13828,13928,13994,14064,14129,14204,14273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3215,3280,3877,4446,4519,4630,4725,4780,4880,4976,5046,5145,5239,5296,5375,5425,5503,5608,5681,5760,5807,5881,5969,6016,6064,6136,6194,6261,6417,6568,6684,6757,6832,6912,7004,7084,7166,7242,7331,7408,7492,7582,7637,7771,7820,7876,7945,8010,8079,8144,8210,8299,8369", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,46,73,87,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,83,89,54,133,48,55,68,64,68,64,65,88,69,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3210,3275,3872,4441,4514,4625,4720,4775,4875,4971,5041,5140,5234,5291,5370,5420,5498,5603,5676,5755,5802,5876,5964,6011,6059,6131,6189,6256,6412,6563,6679,6752,6827,6907,6999,7079,7161,7237,7326,7403,7487,7577,7632,7766,7815,7871,7940,8005,8074,8139,8205,8294,8364,8434"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14278,14349,14429,14491,14564,14646,14707,14992,15044,15122,15195,15409,15584,15683,15841,16211,16503,17533,17785,17881,17951,18112,18202,18290,18466,18781,18874,18951,19051,19124,19214,19305,19389,19468,19568,19711,19950,20406,20473,20538,21920,22489,22562,22673,22768,22823,22923,23019,23089,23188,23282,23339,23907,23957,24035,24140,24213,24619,24979,25053,25141,25188,25485,25557,25615,25682,25838,25989,26301,26374,27305,27385,27552,27632,27988,28064,28227,29186,29270,34245,34300,34434,34483,34910,36762,36827,36896,36961,37027,37116,38409", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,46,73,87,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,83,89,54,133,48,55,68,64,68,64,65,88,69,69", "endOffsets": "14344,14424,14486,14559,14641,14702,14769,15039,15117,15190,15264,15503,15678,15766,15919,16295,16583,17627,17876,17946,18039,18197,18285,18396,18549,18869,18946,19046,19119,19209,19300,19384,19463,19563,19631,19783,20055,20468,20533,21130,22484,22557,22668,22763,22818,22918,23014,23084,23183,23277,23334,23413,23952,24030,24135,24208,24287,24661,25048,25136,25183,25231,25552,25610,25677,25833,25984,26100,26369,26444,27380,27472,27627,27709,28059,28148,28299,29265,29355,34295,34429,34478,34534,34974,36822,36891,36956,37022,37111,37181,38474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,194,329,402,491,550,614,708,823,977,1225,1494,1573,1647,1723,1814,1930,2057,2122,2200,2342,2408,2465,2533,2614,2745,2865,2972,3059,3139,3224,3293", "endColumns": "68,69,134,72,88,58,63,93,114,153,247,268,78,73,75,90,115,126,64,77,141,65,56,67,80,130,119,106,86,79,84,68,146", "endOffsets": "119,189,324,397,486,545,609,703,818,972,1220,1489,1568,1642,1718,1809,1925,2052,2117,2195,2337,2403,2460,2528,2609,2740,2860,2967,3054,3134,3219,3288,3435"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14774,14922,27170,28814,34156,35684,35743,35807,35901,36016,36170,36418,37703,37782,37856,37932,38023,38139,38266,38331,38479,38621,38687,38744,38812,38893,39024,39144,39251,39338,39418,39503,39572", "endColumns": "68,69,134,72,88,58,63,93,114,153,247,268,78,73,75,90,115,126,64,77,141,65,56,67,80,130,119,106,86,79,84,68,146", "endOffsets": "14838,14987,27300,28882,34240,35738,35802,35896,36011,36165,36413,36682,37777,37851,37927,38018,38134,38261,38326,38404,38616,38682,38739,38807,38888,39019,39139,39246,39333,39413,39498,39567,39714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15333,16145,16300,16361,16441,16724,17261,17457,17712,18044,18401,18711", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "15404,16206,16356,16436,16498,16788,17315,17528,17780,18107,18461,18776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6844,7450,12906,13197,39719,40159,40249", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "6909,7537,12979,13327,39883,40244,40326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "26449", "endColumns": "88", "endOffsets": "26533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4673,4784,4937,5068,5174,5317,5443,5559,5816,5957,6063,6212,6338,6486,6625,6691,6761", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4779,4932,5063,5169,5312,5438,5554,5661,5952,6058,6207,6333,6481,6620,6686,6756,6839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,13495", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,13571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,7385,7621,8084,8329,8394,8482,8547,8613,8671,8742,8808,8862,8972,9032,9096,9150,9223,9339,9423,9504,9637,9722,9807,9940,10030,10104,10156,10207,10273,10350,10432,10516,10590,10664,10743,10820,10892,10999,11088,11164,11255,11350,11424,11497,11591,11645,11719,11791,11877,11963,12025,12089,12152,12223,12324,12427,12522,12622,12678,13118", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,7445,7709,8154,8389,8477,8542,8608,8666,8737,8803,8857,8967,9027,9091,9145,9218,9334,9418,9499,9632,9717,9802,9935,10025,10099,10151,10202,10268,10345,10427,10511,10585,10659,10738,10815,10887,10994,11083,11159,11250,11345,11419,11492,11586,11640,11714,11786,11872,11958,12020,12084,12147,12218,12319,12422,12517,12617,12673,12728,13192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5666", "endColumns": "149", "endOffsets": "5811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6914,7714,7815,7924", "endColumns": "102,100,108,98", "endOffsets": "7012,7810,7919,8018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15269,15508,15771,15924,16016,16084,16588,16655,16793,16855,16925,17000,17060,17122,17197,17320,17393,17632,18554,18622,19636,19788,19866,23484,23587,24570,25236,25288,25423,26105,26174,26984,27083,27805,27898", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "15328,15579,15836,16011,16079,16140,16650,16719,16850,16920,16995,17055,17117,17192,17256,17388,17452,17707,18617,18706,19706,19861,19945,23582,23666,24614,25283,25363,25480,26169,26241,27078,27165,27893,27983"}}]}]}