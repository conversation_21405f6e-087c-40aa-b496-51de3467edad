{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3175,3240,3917,4569,4643,4758,4853,4908,5001,5086,5153,5241,5329,5386,5464,5513,5590,5696,5768,5843,5889,5968,6060,6107,6155,6226,6284,6344,6524,6686,6810,6877,6961,7039,7136,7216,7298,7371,7460,7537,7621,7706,7759,7891,7939,7993,8062,8128,8197,8261,8331,8419,8486", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,83,84,52,131,47,53,68,65,68,63,69,87,66,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3170,3235,3912,4564,4638,4753,4848,4903,4996,5081,5148,5236,5324,5381,5459,5508,5585,5691,5763,5838,5884,5963,6055,6102,6150,6221,6279,6339,6519,6681,6805,6872,6956,7034,7131,7211,7293,7366,7455,7532,7616,7701,7754,7886,7934,7988,8057,8123,8192,8256,8326,8414,8481,8561"}, "to": {"startLines": "154,155,156,157,158,159,160,164,165,166,167,170,172,173,175,180,184,199,202,203,204,206,207,208,210,214,215,216,217,218,219,220,221,222,223,225,228,231,232,233,242,243,244,245,246,247,248,249,250,251,252,253,260,261,262,263,264,267,272,273,274,275,280,281,282,283,284,285,289,290,299,300,302,303,307,308,310,320,321,370,371,372,373,377,388,389,390,391,392,393,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13620,13688,13765,13827,13895,13972,14030,14328,14381,14457,14534,14758,14935,15034,15187,15536,15823,16840,17084,17177,17246,17402,17489,17577,17746,18049,18139,18215,18312,18391,18487,18575,18664,18749,18850,18999,19248,19703,19775,19840,21284,21936,22010,22125,22220,22275,22368,22453,22520,22608,22696,22753,23322,23371,23448,23554,23626,24044,24418,24497,24589,24636,24926,24997,25055,25115,25295,25457,25771,25838,26688,26766,26939,27019,27356,27429,27583,28552,28636,33624,33677,33809,33857,34277,36156,36222,36291,36355,36425,36513,37816", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,83,84,52,131,47,53,68,65,68,63,69,87,66,79", "endOffsets": "13683,13760,13822,13890,13967,14025,14084,14376,14452,14529,14610,14852,15029,15115,15262,15609,15901,16932,17172,17241,17331,17484,17572,17681,17822,18134,18210,18307,18386,18482,18570,18659,18744,18845,18920,19067,19347,19770,19835,20512,21931,22005,22120,22215,22270,22363,22448,22515,22603,22691,22748,22826,23366,23443,23549,23621,23696,24085,24492,24584,24631,24679,24992,25050,25110,25290,25452,25576,25833,25917,26761,26858,27014,27096,27424,27513,27655,28631,28716,33672,33804,33852,33906,34341,36217,36286,36350,36420,36508,36575,37891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,13519", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,13615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,13369", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,13444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,406,454,521,618,687,916,981,1079,1145,1200,1264,1559,1633,1699,1752,1805,1897,2023,2130,2187,2271,2347,2430,2495,2594,2870,2947,3024,3085,3145,3207,3267,3367,3448,3551,3648,3721,3800,3885,4062,4254,4372,4428,5088,5153", "endColumns": "153,196,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,99,80,102,96,72,78,84,176,191,117,55,659,64,54", "endOffsets": "204,401,449,516,613,682,911,976,1074,1140,1195,1259,1554,1628,1694,1747,1800,1892,2018,2125,2182,2266,2342,2425,2490,2589,2865,2942,3019,3080,3140,3202,3262,3362,3443,3546,3643,3716,3795,3880,4057,4249,4367,4423,5083,5148,5203"}, "to": {"startLines": "229,230,234,235,236,237,238,239,240,254,257,259,265,270,271,278,288,291,292,293,294,295,301,304,309,311,312,313,314,316,318,319,322,327,336,352,353,354,355,356,368,374,375,376,378,379,394", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19352,19506,20517,20565,20632,20729,20798,21027,21092,22831,23082,23258,23701,24278,24352,24809,25718,25922,26014,26140,26247,26304,26863,27101,27518,27660,27759,28035,28112,28248,28430,28490,28721,29377,30484,31943,32046,32143,32216,32295,33360,33911,34103,34221,34346,35006,36580", "endColumns": "153,196,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,99,80,102,96,72,78,84,176,191,117,55,659,64,54", "endOffsets": "19501,19698,20560,20627,20724,20793,21022,21087,21185,22892,23132,23317,23991,24347,24413,24857,25766,26009,26135,26242,26299,26383,26934,27179,27578,27754,28030,28107,28184,28304,28485,28547,28776,29472,30560,32041,32138,32211,32290,32375,33532,34098,34216,34272,35001,35066,36630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "82,428", "startColumns": "4,4", "startOffsets": "8004,39784", "endColumns": "60,74", "endOffsets": "8060,39854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "69,76,144,148,422,426,427", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6837,7442,12781,13065,39196,39629,39708", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "6902,7524,12854,13202,39360,39703,39779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2769,2857,2935,2989,3040,3106,3174,3248,3338,3414,3485,3563,3633,3703,3803,3892,3970,4058,4148,4220,4292,4376,4427,4505,4571,4652,4735,4797,4861,4924,4993,5093,5197,5290,5390,5448,5503", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2764,2852,2930,2984,3035,3101,3169,3243,3333,3409,3480,3558,3628,3698,3798,3887,3965,4053,4143,4215,4287,4371,4422,4500,4566,4647,4730,4792,4856,4919,4988,5088,5192,5285,5385,5443,5498,5576"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,7378,7602,8065,8297,8357,8444,8508,8570,8634,8702,8767,8821,8930,8988,9050,9104,9179,9299,9381,9461,9595,9673,9753,9876,9964,10042,10096,10147,10213,10281,10355,10445,10521,10592,10670,10740,10810,10910,10999,11077,11165,11255,11327,11399,11483,11534,11612,11678,11759,11842,11904,11968,12031,12100,12200,12304,12397,12497,12555,12987", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,7437,7689,8128,8352,8439,8503,8565,8629,8697,8762,8816,8925,8983,9045,9099,9174,9294,9376,9456,9590,9668,9748,9871,9959,10037,10091,10142,10208,10276,10350,10440,10516,10587,10665,10735,10805,10905,10994,11072,11160,11250,11322,11394,11478,11529,11607,11673,11754,11837,11899,11963,12026,12095,12195,12299,12392,12492,12550,12605,13060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5632", "endColumns": "129", "endOffsets": "5757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "168,171,174,176,177,178,185,186,188,189,190,191,192,193,194,196,197,200,211,212,224,226,227,255,256,266,276,277,279,286,287,296,297,305,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14615,14857,15120,15267,15351,15418,15906,15975,16112,16176,16244,16316,16376,16435,16508,16631,16701,16937,17827,17891,18925,19072,19157,22897,22995,23996,24684,24732,24862,25581,25648,26388,26482,27184,27272", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "14675,14930,15182,15346,15413,15474,15970,16037,16171,16239,16311,16371,16430,16503,16567,16696,16759,17007,17886,17975,18994,19152,19243,22990,23077,24039,24727,24804,24921,25643,25713,26477,26563,27267,27351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4729,4888,5014,5123,5279,5409,5529,5762,5916,6023,6184,6312,6454,6630,6697,6759", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4724,4883,5009,5118,5274,5404,5524,5627,5911,6018,6179,6307,6449,6625,6692,6754,6832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6907,7694,7795,7907", "endColumns": "109,100,111,96", "endOffsets": "7012,7790,7902,7999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,205,325,384,471,525,583,659,765,928,1197,1486,1560,1629,1713,1804,1925,2054,2127,2201,2364,2433,2492,2566,2647,2788,2917,3031,3117,3192,3277,3345", "endColumns": "70,78,119,58,86,53,57,75,105,162,268,288,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,113,85,74,84,67,155", "endOffsets": "121,200,320,379,466,520,578,654,760,923,1192,1481,1555,1624,1708,1799,1920,2049,2122,2196,2359,2428,2487,2561,2642,2783,2912,3026,3112,3187,3272,3340,3496"}, "to": {"startLines": "161,163,298,315,369,380,381,382,383,384,385,386,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14089,14249,26568,28189,33537,35071,35125,35183,35259,35365,35528,35797,37101,37175,37244,37328,37419,37540,37669,37742,37896,38059,38128,38187,38261,38342,38483,38612,38726,38812,38887,38972,39040", "endColumns": "70,78,119,58,86,53,57,75,105,162,268,288,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,113,85,74,84,67,155", "endOffsets": "14155,14323,26683,28243,33619,35120,35178,35254,35360,35523,35792,36081,37170,37239,37323,37414,37535,37664,37737,37811,38054,38123,38182,38256,38337,38478,38607,38721,38807,38882,38967,39035,39191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "169,179,181,182,183,187,195,198,201,205,209,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14680,15479,15614,15676,15761,16042,16572,16764,17012,17336,17686,17980", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "14753,15531,15671,15756,15818,16107,16626,16835,17079,17397,17741,18044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,238,359,458,547,668,845,1033,1173,1264,1367,1460,1561,1728,1836,1935,2165,2271,2365,2424,2488,2575,2672,2783,3007,3076,3153,3242,3308,3389,3460,3536,3649,3735,3825,3896,3963,4022,4124,4234,4339,4467,4558,4629,4699,4786,4858,4964,5078", "endColumns": "88,93,120,98,88,120,176,187,139,90,102,92,100,166,107,98,229,105,93,58,63,86,96,110,223,68,76,88,65,80,70,75,112,85,89,70,66,58,101,109,104,127,90,70,69,86,71,105,113,86", "endOffsets": "139,233,354,453,542,663,840,1028,1168,1259,1362,1455,1556,1723,1831,1930,2160,2266,2360,2419,2483,2570,2667,2778,3002,3071,3148,3237,3303,3384,3455,3531,3644,3730,3820,3891,3958,4017,4119,4229,4334,4462,4553,4624,4694,4781,4853,4959,5073,5160"}, "to": {"startLines": "162,241,258,268,269,317,323,324,325,326,328,329,330,331,332,333,334,335,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,357,358,359,360,361,362,363,364,365,366,367,387,395,396,397,398,399", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14160,21190,23137,24090,24189,28309,28781,28958,29146,29286,29477,29580,29673,29774,29941,30049,30148,30378,30565,30659,30718,30782,30869,30966,31077,31301,31370,31447,31536,31602,31683,31754,31830,32380,32466,32556,32627,32694,32753,32855,32965,33070,33198,33289,36086,36635,36722,36794,36900,37014", "endColumns": "88,93,120,98,88,120,176,187,139,90,102,92,100,166,107,98,229,105,93,58,63,86,96,110,223,68,76,88,65,80,70,75,112,85,89,70,66,58,101,109,104,127,90,70,69,86,71,105,113,86", "endOffsets": "14244,21279,23253,24184,24273,28425,28953,29141,29281,29372,29575,29668,29769,29936,30044,30143,30373,30479,30654,30713,30777,30864,30961,31072,31296,31365,31442,31531,31597,31678,31749,31825,31938,32461,32551,32622,32689,32748,32850,32960,33065,33193,33284,33355,36151,36717,36789,36895,37009,37096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4548,7214,7529", "endColumns": "74,75,72", "endOffsets": "4618,7285,7597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,423,424,425", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,7017,7114,7290,8133,8209,12610,12699,12859,12923,13207,13287,13449,39365,39442,39509", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,7109,7209,7373,8204,8292,12694,12776,12918,12982,13282,13364,13514,39437,39504,39624"}}]}]}