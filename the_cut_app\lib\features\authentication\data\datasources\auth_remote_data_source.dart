import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

/// Authentication remote data source interface
abstract class AuthRemoteDataSource {
  /// Get the current user
  Future<UserModel?> getCurrentUser();

  /// Get the auth state changes stream
  Stream<UserModel?> get authStateChanges;

  /// Sign in with email and password
  Future<UserModel> signInWithEmailAndPassword(
    String email,
    String password,
  );

  /// Register with email and password
  Future<UserModel> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  );

  /// Sign out
  Future<void> signOut();

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email);

  /// Update user profile
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  });

  /// Update email
  Future<void> updateEmail(String email);

  /// Update password
  Future<void> updatePassword(String password);

  /// Delete account
  Future<void> deleteAccount();
  
  /// Update user role
  Future<UserModel> updateUserRole(
    String userId,
    List<String> roles,
  );
  
  /// Update user branch
  Future<UserModel> updateUserBranch(
    String userId,
    String branchId,
  );
  
  /// Get all users
  Future<List<UserModel>> getAllUsers();
  
  /// Get user by id
  Future<UserModel> getUserById(String userId);
}

/// Authentication remote data source implementation
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;
  
  AuthRemoteDataSourceImpl(this._firebaseAuth, this._firestore);
  
  /// Get the users collection reference
  CollectionReference get _usersCollection => 
      _firestore.collection('users');
  
  @override
  Future<UserModel?> getCurrentUser() async {
    final firebaseUser = _firebaseAuth.currentUser;
    
    if (firebaseUser == null) {
      return null;
    }
    
    try {
      final userDoc = await _usersCollection.doc(firebaseUser.uid).get();
      
      if (userDoc.exists) {
        return UserModel.fromFirestore(userDoc);
      } else {
        // Create user document if it doesn't exist
        final userModel = UserModel.fromFirebaseUser(firebaseUser);
        await _usersCollection.doc(firebaseUser.uid).set(userModel.toMap());
        return userModel;
      }
    } catch (e) {
      throw ServerException(
        message: 'Failed to get current user: ${e.toString()}',
      );
    }
  }
  
  @override
  Stream<UserModel?> get authStateChanges {
    return _firebaseAuth.authStateChanges().asyncMap((firebaseUser) async {
      if (firebaseUser == null) {
        return null;
      }
      
      try {
        final userDoc = await _usersCollection.doc(firebaseUser.uid).get();
        
        if (userDoc.exists) {
          return UserModel.fromFirestore(userDoc);
        } else {
          // Create user document if it doesn't exist
          final userModel = UserModel.fromFirebaseUser(firebaseUser);
          await _usersCollection.doc(firebaseUser.uid).set(userModel.toMap());
          return userModel;
        }
      } catch (e) {
        throw ServerException(
          message: 'Failed to get user from auth state changes: ${e.toString()}',
        );
      }
    });
  }
  
  @override
  Future<UserModel> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user == null) {
        throw const AuthException(
          message: 'User is null after sign in',
          code: 500,
        );
      }
      
      // Update last login time
      await _usersCollection.doc(userCredential.user!.uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
      });
      
      final userDoc = await _usersCollection.doc(userCredential.user!.uid).get();
      
      if (userDoc.exists) {
        return UserModel.fromFirestore(userDoc);
      } else {
        // Create user document if it doesn't exist
        final userModel = UserModel.fromFirebaseUser(userCredential.user!);
        await _usersCollection.doc(userCredential.user!.uid).set(userModel.toMap());
        return userModel;
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Authentication failed',
        code: 401,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to sign in: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<UserModel> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user == null) {
        throw const AuthException(
          message: 'User is null after registration',
          code: 500,
        );
      }
      
      // Update user profile with name
      await userCredential.user!.updateDisplayName(name);
      
      // Reload user to get updated data
      await userCredential.user!.reload();
      
      // Create user document
      final userModel = UserModel.fromFirebaseUser(userCredential.user!).copyWith(
        name: name,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      await _usersCollection.doc(userCredential.user!.uid).set(userModel.toMap());
      
      return userModel;
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Registration failed',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to register: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      throw ServerException(
        message: 'Failed to sign out: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Failed to send password reset email',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to send password reset email: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        throw const AuthException(
          message: 'No user is signed in',
          code: 401,
        );
      }
      
      // Update Firebase Auth profile
      if (displayName != null) {
        await user.updateDisplayName(displayName);
      }
      
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }
      
      // Update Firestore document
      final updates = <String, dynamic>{};
      
      if (displayName != null) {
        updates['name'] = displayName;
      }
      
      if (photoURL != null) {
        updates['photoUrl'] = photoURL;
      }
      
      if (phoneNumber != null) {
        updates['phoneNumber'] = phoneNumber;
      }
      
      await _usersCollection.doc(user.uid).update(updates);
      
      // Reload user to get updated data
      await user.reload();
      
      // Get updated user document
      final userDoc = await _usersCollection.doc(user.uid).get();
      
      return UserModel.fromFirestore(userDoc);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Failed to update profile',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to update profile: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> updateEmail(String email) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        throw const AuthException(
          message: 'No user is signed in',
          code: 401,
        );
      }
      
      // Update Firebase Auth email
      await user.updateEmail(email);
      
      // Update Firestore document
      await _usersCollection.doc(user.uid).update({
        'email': email,
      });
      
      // Reload user to get updated data
      await user.reload();
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Failed to update email',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to update email: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> updatePassword(String password) async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        throw const AuthException(
          message: 'No user is signed in',
          code: 401,
        );
      }
      
      await user.updatePassword(password);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Failed to update password',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to update password: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      
      if (user == null) {
        throw const AuthException(
          message: 'No user is signed in',
          code: 401,
        );
      }
      
      // Delete Firestore document
      await _usersCollection.doc(user.uid).delete();
      
      // Delete Firebase Auth user
      await user.delete();
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw AuthException(
        message: e.message ?? 'Failed to delete account',
        code: 400,
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete account: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<UserModel> updateUserRole(
    String userId,
    List<String> roles,
  ) async {
    try {
      // Update Firestore document
      await _usersCollection.doc(userId).update({
        'roles': roles,
      });
      
      // Get updated user document
      final userDoc = await _usersCollection.doc(userId).get();
      
      return UserModel.fromFirestore(userDoc);
    } catch (e) {
      throw ServerException(
        message: 'Failed to update user role: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<UserModel> updateUserBranch(
    String userId,
    String branchId,
  ) async {
    try {
      // Update Firestore document
      await _usersCollection.doc(userId).update({
        'branchId': branchId,
      });
      
      // Get updated user document
      final userDoc = await _usersCollection.doc(userId).get();
      
      return UserModel.fromFirestore(userDoc);
    } catch (e) {
      throw ServerException(
        message: 'Failed to update user branch: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _usersCollection.get();
      
      return querySnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to get all users: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<UserModel> getUserById(String userId) async {
    try {
      final userDoc = await _usersCollection.doc(userId).get();
      
      if (!userDoc.exists) {
        throw NotFoundException(
          message: 'User not found',
        );
      }
      
      return UserModel.fromFirestore(userDoc);
    } catch (e) {
      if (e is NotFoundException) {
        rethrow;
      }
      
      throw ServerException(
        message: 'Failed to get user by id: ${e.toString()}',
      );
    }
  }
}
