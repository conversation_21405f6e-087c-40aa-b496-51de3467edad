import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';

/// Loading indicator types
enum LoadingIndicatorType {
  circular,
  linear,
}

/// Loading indicator sizes
enum LoadingIndicatorSize {
  small,
  medium,
  large,
}

/// Custom loading indicator
class LoadingIndicator extends StatelessWidget {
  final LoadingIndicatorType type;
  final LoadingIndicatorSize size;
  final Color? color;
  final double? value;
  final String? message;
  final bool withBackground;

  const LoadingIndicator({
    super.key,
    this.type = LoadingIndicatorType.circular,
    this.size = LoadingIndicatorSize.medium,
    this.color,
    this.value,
    this.message,
    this.withBackground = false,
  });

  @override
  Widget build(BuildContext context) {
    final Widget indicator = _buildIndicator();

    if (withBackground) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
        child: Center(
          child: Container(
            padding: EdgeInsets.all(24.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                indicator,
                if (message != null) ...[
                  SizedBox(height: 16.h),
                  Text(
                    message!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }

    if (message != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          SizedBox(height: 16.h),
          Text(
            message!,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return indicator;
  }

  Widget _buildIndicator() {
    switch (type) {
      case LoadingIndicatorType.circular:
        return _buildCircularIndicator();
      case LoadingIndicatorType.linear:
        return _buildLinearIndicator();
    }
  }

  Widget _buildCircularIndicator() {
    final double indicatorSize = _getIndicatorSize();

    return SizedBox(
      width: indicatorSize,
      height: indicatorSize,
      child: CircularProgressIndicator(
        value: value,
        strokeWidth: _getStrokeWidth(),
        color: color ?? AppColors.primary,
      ),
    );
  }

  Widget _buildLinearIndicator() {
    return SizedBox(
      width: _getLinearWidth(),
      child: LinearProgressIndicator(
        value: value,
        minHeight: _getLinearHeight(),
        color: color ?? AppColors.primary,
        backgroundColor: AppColors.divider,
      ),
    );
  }

  double _getIndicatorSize() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 24.r;
      case LoadingIndicatorSize.medium:
        return 40.r;
      case LoadingIndicatorSize.large:
        return 56.r;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 2.w;
      case LoadingIndicatorSize.medium:
        return 3.w;
      case LoadingIndicatorSize.large:
        return 4.w;
    }
  }

  double _getLinearWidth() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 100.w;
      case LoadingIndicatorSize.medium:
        return 200.w;
      case LoadingIndicatorSize.large:
        return 300.w;
    }
  }

  double _getLinearHeight() {
    switch (size) {
      case LoadingIndicatorSize.small:
        return 2.h;
      case LoadingIndicatorSize.medium:
        return 4.h;
      case LoadingIndicatorSize.large:
        return 6.h;
    }
  }
}
