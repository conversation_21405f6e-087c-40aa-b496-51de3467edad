import '../../domain/entities/subscriber.dart';

/// Subscriber model
class SubscriberModel extends Subscriber {
  /// Constructor
  const SubscriberModel({
    required super.id,
    required super.userId,
    required super.subscriptionPlanId,
    required super.startDate,
    required super.endDate,
    required super.isActive,
    super.isInTrial = false,
    super.autoRenew = true,
    required super.paymentMethod,
    super.cardLast4,
    super.cardBrand,
    super.cardExpiryMonth,
    super.cardExpiryYear,
    required super.countryCode,
    required super.currencyCode,
    required super.nextBillingDate,
    required super.billingCycle,
    required super.billingAmount,
    required super.billingStatus,
    super.cancellationDate,
    super.cancellationReason,
    required super.createdAt,
    required super.updatedAt,
    super.storeReceiptId,
    super.originalTransactionId,
  });

  /// Create a model from a JSON map
  factory SubscriberModel.fromJson(Map<String, dynamic> json) {
    return SubscriberModel(
      id: json['id'],
      userId: json['user_id'],
      subscriptionPlanId: json['subscription_plan_id'],
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'],
      isInTrial: json['is_in_trial'] ?? false,
      autoRenew: json['auto_renew'] ?? true,
      paymentMethod: json['payment_method'],
      cardLast4: json['card_last4'],
      cardBrand: json['card_brand'],
      cardExpiryMonth: json['card_expiry_month'],
      cardExpiryYear: json['card_expiry_year'],
      countryCode: json['country_code'],
      currencyCode: json['currency_code'],
      nextBillingDate: DateTime.parse(json['next_billing_date']),
      billingCycle: json['billing_cycle'],
      billingAmount: json['billing_amount'].toDouble(),
      billingStatus: json['billing_status'],
      cancellationDate: json['cancellation_date'] != null
          ? DateTime.parse(json['cancellation_date'])
          : null,
      cancellationReason: json['cancellation_reason'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      storeReceiptId: json['store_receipt_id'],
      originalTransactionId: json['original_transaction_id'],
    );
  }

  /// Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'subscription_plan_id': subscriptionPlanId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive,
      'is_in_trial': isInTrial,
      'auto_renew': autoRenew,
      'payment_method': paymentMethod,
      'card_last4': cardLast4,
      'card_brand': cardBrand,
      'card_expiry_month': cardExpiryMonth,
      'card_expiry_year': cardExpiryYear,
      'country_code': countryCode,
      'currency_code': currencyCode,
      'next_billing_date': nextBillingDate.toIso8601String(),
      'billing_cycle': billingCycle,
      'billing_amount': billingAmount,
      'billing_status': billingStatus,
      'cancellation_date': cancellationDate?.toIso8601String(),
      'cancellation_reason': cancellationReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'store_receipt_id': storeReceiptId,
      'original_transaction_id': originalTransactionId,
    };
  }
}
