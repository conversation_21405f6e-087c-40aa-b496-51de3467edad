import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/finance_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../domain/entities/transaction_entity.dart';

/// Transaction details screen
class TransactionDetailsScreen extends StatefulWidget {
  /// Transaction ID
  final String transactionId;

  /// Constructor
  const TransactionDetailsScreen({
    super.key,
    required this.transactionId,
  });

  @override
  State<TransactionDetailsScreen> createState() => _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FinanceService>(context, listen: false)
          .getTransactionById(widget.transactionId);
    });
  }

  Future<void> _deleteTransaction(TransactionEntity transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text(
          'Are you sure you want to delete "${transaction.title}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final financeService = Provider.of<FinanceService>(context, listen: false);
      final success = await financeService.deleteTransaction(transaction.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction deleted successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              financeService.error ?? 'Failed to delete transaction',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  String _getCategoryText(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.sales:
        return 'Sales';
      case TransactionCategory.services:
        return 'Services';
      case TransactionCategory.salary:
        return 'Salary';
      case TransactionCategory.rent:
        return 'Rent';
      case TransactionCategory.utilities:
        return 'Utilities';
      case TransactionCategory.supplies:
        return 'Supplies';
      case TransactionCategory.equipment:
        return 'Equipment';
      case TransactionCategory.marketing:
        return 'Marketing';
      case TransactionCategory.taxes:
        return 'Taxes';
      case TransactionCategory.insurance:
        return 'Insurance';
      case TransactionCategory.other:
        return 'Other';
    }
  }

  @override
  Widget build(BuildContext context) {
    final financeService = Provider.of<FinanceService>(context);
    final transaction = financeService.selectedTransaction;

    return Scaffold(
      appBar: AppBar(
        title: Text(transaction?.title ?? 'Transaction Details'),
        actions: [
          if (transaction != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/edit-transaction',
                  arguments: transaction.id,
                );
              },
            ),
        ],
      ),
      body: financeService.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : transaction == null
              ? _buildTransactionNotFound()
              : _buildTransactionDetails(transaction),
    );
  }

  Widget _buildTransactionNotFound() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: AppColors.error.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'Transaction Not Found',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'The transaction you are looking for does not exist or has been deleted.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: 'Go Back',
              icon: Icons.arrow_back,
              onPressed: () {
                Navigator.pop(context);
              },
              width: 200.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionDetails(TransactionEntity transaction) {
    final Color typeColor = transaction.type == TransactionType.income
        ? AppColors.success
        : AppColors.error;
    
    final IconData typeIcon = transaction.type == TransactionType.income
        ? Icons.arrow_upward
        : Icons.arrow_downward;
    
    final String typeText = transaction.type == TransactionType.income
        ? 'Income'
        : 'Expense';
    
    final String categoryText = _getCategoryText(transaction.category);
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction header
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Type and amount
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color: typeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          typeIcon,
                          size: 20.w,
                          color: typeColor,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          typeText,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: typeColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Amount
                  Text(
                    '\$${transaction.amount.toStringAsFixed(2)}',
                    style: AppTextStyles.h2.copyWith(
                      color: typeColor,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Title
                  Text(
                    transaction.title,
                    style: AppTextStyles.h3,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Category
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      categoryText,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Transaction info
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Transaction Details',
                    style: AppTextStyles.h4,
                  ),
                  SizedBox(height: 16.h),
                  
                  // Description
                  if (transaction.description != null) ...[
                    _buildInfoItem(
                      icon: Icons.description_outlined,
                      title: 'Description',
                      value: transaction.description!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Date
                  _buildInfoItem(
                    icon: Icons.calendar_today_outlined,
                    title: 'Date',
                    value: _formatDate(transaction.date),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Created by
                  _buildInfoItem(
                    icon: Icons.person_outline,
                    title: 'Created By',
                    value: transaction.createdByName,
                  ),
                  SizedBox(height: 16.h),
                  
                  // Branch
                  if (transaction.branchName != null) ...[
                    _buildInfoItem(
                      icon: Icons.store_outlined,
                      title: 'Branch',
                      value: transaction.branchName!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Created at
                  _buildInfoItem(
                    icon: Icons.access_time_outlined,
                    title: 'Created At',
                    value: _formatDateTime(transaction.createdAt),
                  ),
                  
                  // Updated at
                  if (transaction.updatedAt != null) ...[
                    SizedBox(height: 16.h),
                    _buildInfoItem(
                      icon: Icons.update_outlined,
                      title: 'Updated At',
                      value: _formatDateTime(transaction.updatedAt!),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Actions
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Edit Transaction',
                  icon: Icons.edit_outlined,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/edit-transaction',
                      arguments: transaction.id,
                    );
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CustomButton(
                  text: 'Delete Transaction',
                  icon: Icons.delete_outline,
                  color: AppColors.error,
                  onPressed: () => _deleteTransaction(transaction),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20.w,
          color: AppColors.textSecondary,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
