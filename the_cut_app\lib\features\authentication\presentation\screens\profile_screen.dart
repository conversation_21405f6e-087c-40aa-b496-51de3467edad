import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/font_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Profile screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    final user = Provider.of<AuthService>(context, listen: false).currentUser;
    _nameController = TextEditingController(text: user?.name ?? '');
    _emailController = TextEditingController(text: user?.email ?? '');
    _phoneController = TextEditingController(text: user?.phoneNumber ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _toggleEditing() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _updateProfile() async {
    if (_formKey.currentState!.validate()) {
      final authService = Provider.of<AuthService>(context, listen: false);
      final localizations = AppLocalizations.of(context);

      final success = await authService.updateProfile(
        displayName: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
      );

      if (success && mounted) {
        setState(() {
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(localizations.translate('profile_updated_successfully')),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authService.error ??
                localizations.translate('failed_to_update_profile')),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _signOut() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    await authService.signOut();

    if (mounted) {
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    final user = authService.currentUser;
    final localizations = AppLocalizations.of(context);
    final fontService = Provider.of<FontService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('profile')),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _toggleEditing,
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile picture
                CircleAvatar(
                  radius: 60.r,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    user?.name?.isNotEmpty == true
                        ? user!.name!.substring(0, 1).toUpperCase()
                        : 'U',
                    style: GoogleFonts.getFont(
                      fontService.fontFamily,
                      fontSize: 48.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                SizedBox(height: 24.h),

                // User role badge
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    user?.roles?.isNotEmpty == true
                        ? localizations
                            .translate(user!.roles!.first.toLowerCase())
                        : localizations.translate('user'),
                    style: GoogleFonts.getFont(
                      fontService.fontFamily,
                      fontSize: 12.sp,
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 32.h),

                // Name field
                CustomTextField(
                  controller: _nameController,
                  hintText: localizations.translate('full_name'),
                  labelText: localizations.translate('full_name'),
                  prefixIcon: Icons.person_outline,
                  enabled: _isEditing,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.translate('please_enter_your_name');
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),

                // Email field
                CustomTextField(
                  controller: _emailController,
                  hintText: localizations.translate('email'),
                  labelText: localizations.translate('email'),
                  prefixIcon: Icons.email_outlined,
                  enabled: false, // Email cannot be changed
                ),
                SizedBox(height: 16.h),

                // Phone field
                CustomTextField(
                  controller: _phoneController,
                  hintText: localizations.translate('phone_number'),
                  labelText: localizations.translate('phone_number'),
                  prefixIcon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  enabled: _isEditing,
                ),
                SizedBox(height: 32.h),

                // Update profile button
                if (_isEditing) ...[
                  CustomButton(
                    text: localizations.translate('update_profile'),
                    isLoading: authService.isLoading,
                    onPressed: _updateProfile,
                  ),
                  SizedBox(height: 16.h),

                  // Cancel button
                  CustomButton(
                    text: localizations.translate('cancel'),
                    color: Colors.grey.shade200,
                    textColor: AppColors.textPrimary,
                    onPressed: _toggleEditing,
                  ),
                ] else ...[
                  // Sign out button
                  CustomButton(
                    text: localizations.translate('sign_out'),
                    color: AppColors.error,
                    isLoading: authService.isLoading,
                    onPressed: _signOut,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
