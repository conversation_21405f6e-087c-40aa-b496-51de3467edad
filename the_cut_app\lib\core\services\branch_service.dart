import 'package:flutter/material.dart';
import '../../features/branches/domain/entities/branch_entity.dart';
import '../data/mock_data.dart';

/// Branch service
class BranchService extends ChangeNotifier {
  List<BranchEntity> _branches = [];
  BranchEntity? _selectedBranch;
  bool _isLoading = false;
  String? _error;

  /// Get all branches
  List<BranchEntity> get branches => _branches;

  /// Get selected branch
  BranchEntity? get selectedBranch => _selectedBranch;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Load all branches
  Future<void> loadBranches() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get branches from mock data
      _branches = List.from(MockData.branches);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get branch by id
  Future<BranchEntity?> getBranchById(String branchId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get branch from mock data
      _selectedBranch = MockData.getBranchById(branchId);
      _isLoading = false;
      notifyListeners();
      return _selectedBranch;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Create branch
  Future<BranchEntity?> createBranch(BranchEntity branch) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Add branch to mock data
      final newBranch = MockData.addBranch(branch);
      _branches.add(newBranch);
      _selectedBranch = newBranch;
      _isLoading = false;
      notifyListeners();
      return newBranch;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update branch
  Future<BranchEntity?> updateBranch(BranchEntity branch) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Update branch in mock data
      final updatedBranch = MockData.updateBranch(branch);
      
      // Update branch in list
      final index = _branches.indexWhere((b) => b.id == branch.id);
      if (index != -1) {
        _branches[index] = updatedBranch;
      }
      
      _selectedBranch = updatedBranch;
      _isLoading = false;
      notifyListeners();
      return updatedBranch;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Delete branch
  Future<bool> deleteBranch(String branchId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Delete branch from mock data
      MockData.deleteBranch(branchId);
      
      // Remove branch from list
      _branches.removeWhere((branch) => branch.id == branchId);
      
      if (_selectedBranch?.id == branchId) {
        _selectedBranch = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Get active branches
  Future<List<BranchEntity>> getActiveBranches() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get active branches from mock data
      final activeBranches = MockData.getActiveBranches();
      _isLoading = false;
      notifyListeners();
      return activeBranches;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Set selected branch
  void setSelectedBranch(BranchEntity branch) {
    _selectedBranch = branch;
    notifyListeners();
  }

  /// Clear selected branch
  void clearSelectedBranch() {
    _selectedBranch = null;
    notifyListeners();
  }
}
