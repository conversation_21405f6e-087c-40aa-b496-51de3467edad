import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/services/team_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';

/// Edit team member screen
class EditTeamMemberScreen extends StatefulWidget {
  /// Team member ID
  final String teamMemberId;

  /// Constructor
  const EditTeamMemberScreen({
    super.key,
    required this.teamMemberId,
  });

  @override
  State<EditTeamMemberScreen> createState() => _EditTeamMemberScreenState();
}

class _EditTeamMemberScreenState extends State<EditTeamMemberScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _roleController;
  String? _selectedBranchId;
  String? _selectedBranchName;
  bool _isActive = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _roleController = TextEditingController();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTeamMember();
      Provider.of<BranchService>(context, listen: false).loadBranches();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _roleController.dispose();
    super.dispose();
  }

  Future<void> _loadTeamMember() async {
    final teamService = Provider.of<TeamService>(context, listen: false);
    final teamMember = await teamService.getTeamMemberById(widget.teamMemberId);
    
    if (teamMember != null && mounted) {
      setState(() {
        _nameController.text = teamMember.name;
        _emailController.text = teamMember.email;
        _phoneController.text = teamMember.phoneNumber ?? '';
        _roleController.text = teamMember.role;
        _selectedBranchId = teamMember.branchId;
        _selectedBranchName = teamMember.branchName;
        _isActive = teamMember.isActive;
        _isLoading = false;
      });
    } else if (mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Team member not found'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _updateTeamMember() async {
    if (_formKey.currentState!.validate()) {
      final teamService = Provider.of<TeamService>(context, listen: false);
      final currentTeamMember = teamService.selectedTeamMember;
      
      if (currentTeamMember == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Team member not found'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final updatedTeamMember = currentTeamMember.copyWith(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        role: _roleController.text.trim(),
        branchId: _selectedBranchId,
        branchName: _selectedBranchName,
        isActive: _isActive,
        updatedAt: DateTime.now(),
      );
      
      final result = await teamService.updateTeamMember(updatedTeamMember);
      
      if (result != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Team member updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(teamService.error ?? 'Failed to update team member'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final teamService = Provider.of<TeamService>(context);
    final branchService = Provider.of<BranchService>(context);
    final branches = branchService.branches;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Team Member'),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SafeArea(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Form title
                      Text(
                        'Team Member Information',
                        style: AppTextStyles.h4,
                      ),
                      SizedBox(height: 24.h),
                      
                      // Team member name
                      CustomTextField(
                        controller: _nameController,
                        hintText: 'Full Name',
                        labelText: 'Full Name',
                        prefixIcon: Icons.person_outline,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter team member name';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Team member email
                      CustomTextField(
                        controller: _emailController,
                        hintText: 'Email',
                        labelText: 'Email',
                        prefixIcon: Icons.email_outlined,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter team member email';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Team member phone
                      CustomTextField(
                        controller: _phoneController,
                        hintText: 'Phone Number',
                        labelText: 'Phone Number',
                        prefixIcon: Icons.phone_outlined,
                        keyboardType: TextInputType.phone,
                        textInputAction: TextInputAction.next,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Team member role
                      CustomTextField(
                        controller: _roleController,
                        hintText: 'Role',
                        labelText: 'Role',
                        prefixIcon: Icons.work_outline,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter team member role';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Team member branch
                      if (branchService.isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else if (branches.isEmpty)
                        Text(
                          'No branches available. Please create a branch first.',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Branch',
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  isExpanded: true,
                                  hint: const Text('Select Branch'),
                                  value: _selectedBranchId,
                                  items: branches.map((branch) {
                                    return DropdownMenuItem<String>(
                                      value: branch.id,
                                      child: Text(branch.name),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedBranchId = value;
                                      _selectedBranchName = branches
                                          .firstWhere((branch) => branch.id == value)
                                          .name;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      SizedBox(height: 24.h),
                      
                      // Team member status
                      Row(
                        children: [
                          Text(
                            'Team Member Status:',
                            style: AppTextStyles.bodyMedium,
                          ),
                          SizedBox(width: 16.w),
                          Switch(
                            value: _isActive,
                            onChanged: (value) {
                              setState(() {
                                _isActive = value;
                              });
                            },
                            activeColor: AppColors.primary,
                          ),
                          Text(
                            _isActive ? 'Active' : 'Inactive',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: _isActive ? AppColors.success : AppColors.error,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 32.h),
                      
                      // Update button
                      CustomButton(
                        text: 'Update Team Member',
                        isLoading: teamService.isLoading,
                        onPressed: _updateTeamMember,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Cancel button
                      CustomButton(
                        text: 'Cancel',
                        color: Colors.grey.shade200,
                        textColor: AppColors.textPrimary,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
