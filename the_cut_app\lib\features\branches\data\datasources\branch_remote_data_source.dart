import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/branch_model.dart';

/// Branch remote data source interface
abstract class BranchRemoteDataSource {
  /// Get all branches
  Future<List<BranchModel>> getAllBranches();

  /// Get branch by id
  Future<BranchModel> getBranchById(String branchId);

  /// Create branch
  Future<BranchModel> createBranch(BranchModel branch);

  /// Update branch
  Future<BranchModel> updateBranch(BranchModel branch);

  /// Delete branch
  Future<void> deleteBranch(String branchId);

  /// Get branches by manager id
  Future<List<BranchModel>> getBranchesByManagerId(String managerId);

  /// Get active branches
  Future<List<BranchModel>> getActiveBranches();
}

/// Branch remote data source implementation
class BranchRemoteDataSourceImpl implements BranchRemoteDataSource {
  final FirebaseFirestore _firestore;
  
  BranchRemoteDataSourceImpl(this._firestore);
  
  /// Get the branches collection reference
  CollectionReference get _branchesCollection => 
      _firestore.collection(AppConstants.branchesCollection);
  
  @override
  Future<List<BranchModel>> getAllBranches() async {
    try {
      final querySnapshot = await _branchesCollection.get();
      
      return querySnapshot.docs
          .map((doc) => BranchModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to get all branches: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<BranchModel> getBranchById(String branchId) async {
    try {
      final docSnapshot = await _branchesCollection.doc(branchId).get();
      
      if (!docSnapshot.exists) {
        throw NotFoundException(
          message: 'Branch not found',
        );
      }
      
      return BranchModel.fromFirestore(docSnapshot);
    } catch (e) {
      if (e is NotFoundException) {
        rethrow;
      }
      
      throw ServerException(
        message: 'Failed to get branch by id: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<BranchModel> createBranch(BranchModel branch) async {
    try {
      final docRef = _branchesCollection.doc();
      
      final branchWithId = branch.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await docRef.set(branchWithId.toMap());
      
      return branchWithId;
    } catch (e) {
      throw ServerException(
        message: 'Failed to create branch: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<BranchModel> updateBranch(BranchModel branch) async {
    try {
      final branchWithUpdatedAt = branch.copyWith(
        updatedAt: DateTime.now(),
      );
      
      await _branchesCollection.doc(branch.id).update(branchWithUpdatedAt.toMap());
      
      return branchWithUpdatedAt;
    } catch (e) {
      throw ServerException(
        message: 'Failed to update branch: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<void> deleteBranch(String branchId) async {
    try {
      await _branchesCollection.doc(branchId).delete();
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete branch: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<List<BranchModel>> getBranchesByManagerId(String managerId) async {
    try {
      final querySnapshot = await _branchesCollection
          .where('managerId', isEqualTo: managerId)
          .get();
      
      return querySnapshot.docs
          .map((doc) => BranchModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to get branches by manager id: ${e.toString()}',
      );
    }
  }
  
  @override
  Future<List<BranchModel>> getActiveBranches() async {
    try {
      final querySnapshot = await _branchesCollection
          .where('isActive', isEqualTo: true)
          .get();
      
      return querySnapshot.docs
          .map((doc) => BranchModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to get active branches: ${e.toString()}',
      );
    }
  }
}
