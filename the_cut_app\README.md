# The Cut App

A B2B Project Management application built with Flutter and Firebase, following Clean Architecture principles.

## Features

- **Authentication**: User registration, login, and profile management
- **Branches Management**: Create and manage multiple business branches
- **Team Management**: Add team members and assign roles
- **Tasks Management**: Create, assign, and track tasks
- **Finance Management**: Track income, expenses, and generate reports

## Architecture

This project follows Clean Architecture principles with a feature-first approach:

```
lib/
  ├── core/                 # Core functionality used across the app
  │   ├── constants/        # App constants
  │   ├── errors/           # Error handling
  │   ├── utils/            # Utility functions
  │   └── widgets/          # Common widgets
  │
  ├── features/             # App features
  │   ├── authentication/   # Authentication feature
  │   │   ├── data/         # Data layer
  │   │   ├── domain/       # Domain layer
  │   │   └── presentation/ # Presentation layer
  │   │
  │   ├── branches/         # Branches feature
  │   ├── team/             # Team feature
  │   ├── tasks/            # Tasks feature
  │   └── finance/          # Finance feature
  │
  ├── firebase/             # Firebase services
  ├── injection_container.dart # Dependency injection
  └── main.dart             # App entry point
```

## Getting Started

### Prerequisites

- Flutter SDK
- Firebase account
- Android Studio / VS Code

### Installation

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Configure Firebase:
   - Create a new Firebase project
   - Add Android and iOS apps to your Firebase project
   - Download and add the configuration files
   - Enable Authentication, Firestore, and Storage services
4. Run the app: `flutter run`

## Dependencies

- **Firebase**: Authentication, Firestore, Storage
- **State Management**: flutter_bloc
- **Dependency Injection**: get_it
- **Functional Programming**: dartz
- **UI**: flutter_screenutil, cached_network_image
- **Utils**: intl, logger, connectivity_plus, shared_preferences

## License

This project is licensed under the MIT License - see the LICENSE file for details.
