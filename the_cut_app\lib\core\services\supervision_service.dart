import 'package:flutter/foundation.dart';
import '../../features/supervision/domain/entities/supervisor_entity.dart';
import '../../features/real_estate_projects/domain/entities/project_entity.dart';
import '../data/mock_supervision_data.dart';

/// خدمة إدارة الإشراف
class SupervisionService extends ChangeNotifier {
  SupervisorEntity? _currentSupervisor;
  List<SupervisorEntity> _supervisors = [];
  List<ProjectEntity> _supervisorProjects = [];
  bool _isLoading = false;
  String? _error;

  /// الحصول على المشرف الحالي
  SupervisorEntity? get currentSupervisor => _currentSupervisor;

  /// الحصول على جميع المشرفين
  List<SupervisorEntity> get supervisors => _supervisors;

  /// الحصول على مشاريع المشرف
  List<ProjectEntity> get supervisorProjects => _supervisorProjects;

  /// التحقق من حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get error => _error;

  /// تسجيل دخول المشرف
  Future<bool> signInSupervisor(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المشرف من البيانات الوهمية
      _currentSupervisor = MockSupervisionData.getSupervisorByEmailAndPassword(email, password);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تسجيل مشرف جديد
  Future<bool> registerSupervisor(
    String fullName,
    String email,
    String password,
    SupervisorType type,
    String? phoneNumber,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // إنشاء مشرف جديد
      final supervisor = SupervisorEntity(
        id: 'supervisor_${DateTime.now().millisecondsSinceEpoch}',
        fullName: fullName,
        email: email,
        phoneNumber: phoneNumber,
        type: type,
        registrationDate: DateTime.now(),
        status: SupervisorStatus.pending,
      );

      // إضافة المشرف إلى البيانات الوهمية
      final newSupervisor = MockSupervisionData.addSupervisor(supervisor);
      _currentSupervisor = newSupervisor;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تسجيل خروج المشرف
  Future<void> signOutSupervisor() async {
    _currentSupervisor = null;
    _supervisorProjects.clear();
    notifyListeners();
  }

  /// تحديث ملف المشرف الشخصي
  Future<bool> updateSupervisorProfile(SupervisorEntity supervisor) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث المشرف في البيانات الوهمية
      final updatedSupervisor = MockSupervisionData.updateSupervisor(supervisor);
      _currentSupervisor = updatedSupervisor;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تحميل مشاريع المشرف
  Future<void> loadSupervisorProjects(String supervisorId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على مشاريع المشرف من البيانات الوهمية
      _supervisorProjects = MockSupervisionData.getProjectsBySupervisorId(supervisorId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// تحميل جميع المشرفين
  Future<void> loadSupervisors() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // الحصول على المشرفين من البيانات الوهمية
      _supervisors = List.from(MockSupervisionData.supervisors);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// البحث عن المشرفين
  List<SupervisorEntity> searchSupervisors(String query) {
    if (query.isEmpty) return _supervisors;
    
    return _supervisors.where((supervisor) {
      return supervisor.fullName.toLowerCase().contains(query.toLowerCase()) ||
             supervisor.email.toLowerCase().contains(query.toLowerCase()) ||
             supervisor.type.nameAr.contains(query) ||
             supervisor.type.nameEn.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// الحصول على المشرفين حسب النوع
  List<SupervisorEntity> getSupervisorsByType(SupervisorType type) {
    return _supervisors.where((supervisor) => supervisor.type == type).toList();
  }

  /// الحصول على المشرفين حسب الحالة
  List<SupervisorEntity> getSupervisorsByStatus(SupervisorStatus status) {
    return _supervisors.where((supervisor) => supervisor.status == status).toList();
  }

  /// الحصول على المشرفين المتاحين
  List<SupervisorEntity> getAvailableSupervisors() {
    return _supervisors.where((supervisor) => supervisor.isAvailable).toList();
  }

  /// إرسال طلب إعادة تعيين كلمة المرور
  Future<bool> sendPasswordResetRequest(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // التحقق من وجود البريد الإلكتروني
      final supervisor = MockSupervisionData.getSupervisorByEmail(email);
      if (supervisor == null) {
        throw Exception('البريد الإلكتروني غير مسجل');
      }

      // محاكاة إرسال البريد الإلكتروني
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تحديث كلمة المرور
  Future<bool> updatePassword(String currentPassword, String newPassword) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور الحالية
      // وتحديث كلمة المرور الجديدة في قاعدة البيانات
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تحديث حالة المشرف
  Future<bool> updateSupervisorStatus(String supervisorId, SupervisorStatus status) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث حالة المشرف في البيانات الوهمية
      MockSupervisionData.updateSupervisorStatus(supervisorId, status);
      
      // تحديث القائمة المحلية
      final index = _supervisors.indexWhere((s) => s.id == supervisorId);
      if (index != -1) {
        _supervisors[index] = _supervisors[index].copyWith(status: status);
      }
      
      // تحديث المشرف الحالي إذا كان هو نفسه
      if (_currentSupervisor?.id == supervisorId) {
        _currentSupervisor = _currentSupervisor!.copyWith(status: status);
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// تحديث تقييم المشرف
  Future<bool> updateSupervisorRating(String supervisorId, double rating) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث تقييم المشرف في البيانات الوهمية
      MockSupervisionData.updateSupervisorRating(supervisorId, rating);
      
      // تحديث القائمة المحلية
      final index = _supervisors.indexWhere((s) => s.id == supervisorId);
      if (index != -1) {
        _supervisors[index] = _supervisors[index].copyWith(rating: rating);
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
