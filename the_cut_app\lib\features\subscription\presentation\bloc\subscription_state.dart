part of 'subscription_bloc.dart';

/// Base class for subscription states
abstract class SubscriptionState extends Equatable {
  /// Constructor
  const SubscriptionState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class SubscriptionInitial extends SubscriptionState {}

/// State when subscription plans are loading
class SubscriptionPlansLoading extends SubscriptionState {}

/// State when subscription plans are loaded
class SubscriptionPlansLoaded extends SubscriptionState {
  /// Subscription plans
  final List<SubscriptionPlan> plans;

  /// Constructor
  const SubscriptionPlansLoaded(this.plans);

  @override
  List<Object?> get props => [plans];
}

/// State when there is an error loading subscription plans
class SubscriptionPlansError extends SubscriptionState {
  /// Error message
  final String message;

  /// Constructor
  const SubscriptionPlansError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State when current subscriber is loading
class CurrentSubscriberLoading extends SubscriptionState {}

/// State when current subscriber is loaded
class CurrentSubscriberLoaded extends SubscriptionState {
  /// Subscriber
  final Subscriber? subscriber;

  /// Constructor
  const CurrentSubscriberLoaded(this.subscriber);

  @override
  List<Object?> get props => [subscriber];
}

/// State when there is an error loading current subscriber
class CurrentSubscriberError extends SubscriptionState {
  /// Error message
  final String message;

  /// Constructor
  const CurrentSubscriberError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State when subscribing to a plan
class SubscribingToPlan extends SubscriptionState {}

/// State when subscribed to a plan
class SubscribedToPlan extends SubscriptionState {
  /// Subscriber
  final Subscriber subscriber;

  /// Constructor
  const SubscribedToPlan(this.subscriber);

  @override
  List<Object?> get props => [subscriber];
}

/// State when there is an error subscribing to a plan
class SubscriptionError extends SubscriptionState {
  /// Error message
  final String message;

  /// Constructor
  const SubscriptionError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State when supported countries are loading
class SupportedCountriesLoading extends SubscriptionState {}

/// State when supported countries are loaded
class SupportedCountriesLoaded extends SubscriptionState {
  /// Supported countries
  final List<Country> countries;

  /// Constructor
  const SupportedCountriesLoaded(this.countries);

  @override
  List<Object?> get props => [countries];
}

/// State when there is an error loading supported countries
class SupportedCountriesError extends SubscriptionState {
  /// Error message
  final String message;

  /// Constructor
  const SupportedCountriesError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State when a country is selected
class CountrySelected extends SubscriptionState {
  /// Selected country
  final Country country;

  /// Constructor
  const CountrySelected(this.country);

  @override
  List<Object?> get props => [country];
}
