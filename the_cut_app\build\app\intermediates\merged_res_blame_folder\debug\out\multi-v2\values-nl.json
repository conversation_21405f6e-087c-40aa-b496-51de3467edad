{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4687,7438,7757", "endColumns": "81,79,78", "endOffsets": "4764,7513,7831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15689,16483,16626,16688,16769,17063,17604,17793,18044,18360,18710,19004", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "15760,16539,16683,16764,16831,17132,17658,17865,18109,18423,18766,19066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4769,4877,5028,5156,5267,5434,5561,5684,5933,6111,6217,6386,6512,6675,6857,6925,6988", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4872,5023,5151,5262,5429,5556,5679,5785,6106,6212,6381,6507,6670,6852,6920,6983,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14061,14150,14252,14319,14386,14453,14530", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "14145,14247,14314,14381,14448,14525,14602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,212,341,402,492,552,614,693,800,973,1282,1612,1694,1767,1850,1942,2063,2194,2261,2339,2532,2601,2661,2733,2820,2966,3098,3221,3315,3396,3485,3561", "endColumns": "74,81,128,60,89,59,61,78,106,172,308,329,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,122,93,80,88,75,157", "endOffsets": "125,207,336,397,487,547,609,688,795,968,1277,1607,1689,1762,1845,1937,2058,2189,2256,2334,2527,2596,2656,2728,2815,2961,3093,3216,3310,3391,3480,3556,3714"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15085,15253,28080,29804,35366,37053,37113,37175,37254,37361,37534,37843,39244,39326,39399,39482,39574,39695,39826,39893,40049,40242,40311,40371,40443,40530,40676,40808,40931,41025,41106,41195,41271", "endColumns": "74,81,128,60,89,59,61,78,106,172,308,329,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,122,93,80,88,75,157", "endOffsets": "15155,15330,28204,29860,35451,37108,37170,37249,37356,37529,37838,38168,39321,39394,39477,39569,39690,39821,39888,39966,40237,40306,40366,40438,40525,40671,40803,40926,41020,41101,41190,41266,41424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3161,3226,3988,4726,4803,4920,5021,5076,5179,5277,5342,5431,5520,5577,5658,5710,5790,5904,5975,6048,6097,6180,6277,6324,6373,6443,6501,6567,6759,6926,7056,7121,7203,7288,7388,7472,7565,7640,7726,7800,7887,7982,8035,8175,8229,8286,8361,8433,8508,8575,8645,8738,8813", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,86,94,52,139,53,56,74,71,74,66,69,92,74,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3156,3221,3983,4721,4798,4915,5016,5071,5174,5272,5337,5426,5515,5572,5653,5705,5785,5899,5970,6043,6092,6175,6272,6319,6368,6438,6496,6562,6754,6921,7051,7116,7198,7283,7383,7467,7560,7635,7721,7795,7882,7977,8030,8170,8224,8281,8356,8428,8503,8570,8640,8733,8808,8886"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14607,14676,14754,14816,14885,14963,15021,15335,15390,15465,15545,15765,15938,16035,16188,16544,16836,17870,18114,18205,18272,18428,18517,18603,18771,19071,19159,19233,19328,19401,19492,19580,19664,19746,19846,19992,20233,20738,20810,20875,22442,23180,23257,23374,23475,23530,23633,23731,23796,23885,23974,24031,24622,24674,24754,24868,24939,25400,25777,25860,25957,26004,26303,26373,26431,26497,26689,26856,27174,27239,28209,28294,28473,28557,28925,29000,29153,30185,30272,35456,35509,35649,35703,36175,38250,38322,38397,38464,38534,38627,39971", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,86,94,52,139,53,56,74,71,74,66,69,92,74,77", "endOffsets": "14671,14749,14811,14880,14958,15016,15080,15385,15460,15540,15621,15857,16030,16115,16262,16621,16919,17963,18200,18267,18355,18512,18598,18705,18848,19154,19228,19323,19396,19487,19575,19659,19741,19841,19910,20062,20338,20805,20870,21632,23175,23252,23369,23470,23525,23628,23726,23791,23880,23969,24026,24107,24669,24749,24863,24934,25007,25444,25855,25952,25999,26048,26368,26426,26492,26684,26851,26981,27234,27316,28289,28389,28552,28645,28995,29081,29222,30267,30362,35504,35644,35698,35755,36245,38317,38392,38459,38529,38622,38697,40044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,13960", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,14056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,7242,7339,7518,8376,8452,13022,13109,13279,13344,13636,13717,13883,41598,41682,41752", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,7334,7433,7598,8447,8543,13104,13193,13339,13404,13712,13795,13955,41677,41747,41867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2873,2963,3040,3097,3148,3214,3292,3375,3463,3539,3614,3693,3766,3837,3946,4040,4118,4207,4297,4371,4452,4539,4592,4671,4738,4819,4903,4965,5029,5092,5163,5271,5383,5485,5596,5657,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2868,2958,3035,3092,3143,3209,3287,3370,3458,3534,3609,3688,3761,3832,3941,4035,4113,4202,4292,4366,4447,4534,4587,4666,4733,4814,4898,4960,5024,5087,5158,5266,5378,5480,5591,5652,5707,5788"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,7603,7836,8297,8548,8611,8704,8766,8832,8890,8963,9027,9083,9205,9262,9324,9380,9456,9590,9675,9761,9899,9980,10059,10183,10273,10350,10407,10458,10524,10602,10685,10773,10849,10924,11003,11076,11147,11256,11350,11428,11517,11607,11681,11762,11849,11902,11981,12048,12129,12213,12275,12339,12402,12473,12581,12693,12795,12906,12967,13409", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,7662,7920,8371,8606,8699,8761,8827,8885,8958,9022,9078,9200,9257,9319,9375,9451,9585,9670,9756,9894,9975,10054,10178,10268,10345,10402,10453,10519,10597,10680,10768,10844,10919,10998,11071,11142,11251,11345,11423,11512,11602,11676,11757,11844,11897,11976,12043,12124,12208,12270,12334,12397,12468,12576,12688,12790,12901,12962,13017,13485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8236,42029", "endColumns": "60,75", "endOffsets": "8292,42100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5790", "endColumns": "142", "endOffsets": "5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "27321", "endColumns": "87", "endOffsets": "27404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7067,7667,13198,13490,41429,41872,41952", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "7134,7752,13274,13631,41593,41947,42024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,249,386,487,577,705,905,1124,1280,1372,1473,1566,1660,1851,1948,2047,2300,2412,2507,2569,2636,2719,2815,2917,3125,3195,3271,3361,3427,3516,3588,3658,3774,3861,3960,4044,4117,4178,4285,4393,4502,4636,4731,4803,4880,4976,5050,5161,5279", "endColumns": "92,100,136,100,89,127,199,218,155,91,100,92,93,190,96,98,252,111,94,61,66,82,95,101,207,69,75,89,65,88,71,69,115,86,98,83,72,60,106,107,108,133,94,71,76,95,73,110,117,87", "endOffsets": "143,244,381,482,572,700,900,1119,1275,1367,1468,1561,1655,1846,1943,2042,2295,2407,2502,2564,2631,2714,2810,2912,3120,3190,3266,3356,3422,3511,3583,3653,3769,3856,3955,4039,4112,4173,4280,4388,4497,4631,4726,4798,4875,4971,5045,5156,5274,5362"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15160,22341,24421,25449,25550,29933,30427,30627,30846,31002,31192,31293,31386,31480,31671,31768,31867,32120,32318,32413,32475,32542,32625,32721,32823,33031,33101,33177,33267,33333,33422,33494,33564,34118,34205,34304,34388,34461,34522,34629,34737,34846,34980,35075,38173,38757,38853,38927,39038,39156", "endColumns": "92,100,136,100,89,127,199,218,155,91,100,92,93,190,96,98,252,111,94,61,66,82,95,101,207,69,75,89,65,88,71,69,115,86,98,83,72,60,106,107,108,133,94,71,76,95,73,110,117,87", "endOffsets": "15248,22437,24553,25545,25635,30056,30622,30841,30997,31089,31288,31381,31475,31666,31763,31862,32115,32227,32408,32470,32537,32620,32716,32818,33026,33096,33172,33262,33328,33417,33489,33559,33675,34200,34299,34383,34456,34517,34624,34732,34841,34975,35070,35142,38245,38848,38922,39033,39151,39239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,450,496,566,665,737,1005,1065,1154,1218,1273,1337,1674,1745,1811,1864,1917,2009,2142,2261,2318,2402,2481,2563,2630,2726,3052,3129,3207,3275,3335,3399,3459,3557,3643,3741,3841,3915,3994,4081,4300,4532,4650,4715,5454,5518", "endColumns": "159,234,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,97,85,97,99,73,78,86,218,231,117,64,738,63,54", "endOffsets": "210,445,491,561,660,732,1000,1060,1149,1213,1268,1332,1669,1740,1806,1859,1912,2004,2137,2256,2313,2397,2476,2558,2625,2721,3047,3124,3202,3270,3330,3394,3454,3552,3638,3736,3836,3910,3989,4076,4295,4527,4645,4710,5449,5513,5568"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20343,20503,21637,21683,21753,21852,21924,22192,22252,24112,24366,24558,25012,25640,25711,26186,27121,27409,27501,27634,27753,27810,28394,28650,29086,29227,29323,29649,29726,29865,30061,30121,30367,31094,32232,33680,33778,33878,33952,34031,35147,35760,35992,36110,36250,36989,38702", "endColumns": "159,234,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,97,85,97,99,73,78,86,218,231,117,64,738,63,54", "endOffsets": "20498,20733,21678,21748,21847,21919,22187,22247,22336,24171,24416,24617,25344,25706,25772,26234,27169,27496,27629,27748,27805,27889,28468,28727,29148,29318,29644,29721,29799,29928,30116,30180,30422,31187,32313,33773,33873,33947,34026,34113,35361,35987,36105,36170,36984,37048,38752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,13800", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,13878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7139,7925,8026,8137", "endColumns": "102,100,110,98", "endOffsets": "7237,8021,8132,8231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15626,15862,16120,16267,16351,16422,16924,16996,17137,17200,17268,17338,17403,17466,17540,17663,17730,17968,18853,18918,19915,20067,20147,24176,24283,25349,26053,26106,26239,26986,27051,27894,27995,28732,28829", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "15684,15933,16183,16346,16417,16478,16991,17058,17195,17263,17333,17398,17461,17535,17599,17725,17788,18039,18913,18999,19987,20142,20228,24278,24361,25395,26101,26181,26298,27046,27116,27990,28075,28824,28920"}}]}]}