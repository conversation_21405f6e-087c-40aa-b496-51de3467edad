{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4649,4754,4906,5031,5138,5289,5412,5528,5769,5928,6033,6185,6310,6456,6604,6667,6729", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "4749,4901,5026,5133,5284,5407,5523,5626,5923,6028,6180,6305,6451,6599,6662,6724,6803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,257,342,405,474,533,608,678,745,806", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "128,187,252,337,400,469,528,603,673,740,801,868"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15159,15955,16093,16158,16243,16527,17057,17255,17497,17814,18168,18459", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "15232,16009,16153,16238,16301,16591,17111,17325,17562,17876,18224,18521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,13477", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,13573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,430,431,432", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,4489,6974,7068,7241,8079,8161,12573,12662,12820,12885,13161,13239,13404,39686,39763,39829", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4484,4566,7063,7162,7323,8156,8245,12657,12741,12880,12944,13234,13316,13472,39758,39824,39945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5631", "endColumns": "137", "endOffsets": "5764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,266,348,417,478,549,616,678,746,816,876,939,1013,1077,1153,1216,1288,1351,1437,1514,1595,1689,1785,1867,1916,1963,2038,2102,2169,2238,2340,2423,2521", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "115,193,261,343,412,473,544,611,673,741,811,871,934,1008,1072,1148,1211,1283,1346,1432,1509,1590,1684,1780,1862,1911,1958,2033,2097,2164,2233,2335,2418,2516,2612"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,303,304,312,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15094,15335,15597,15743,15825,15894,16389,16460,16596,16658,16726,16796,16856,16919,16993,17116,17192,17425,18310,18373,19420,19579,19660,23446,23542,24533,25228,25275,25403,26129,26196,26936,27038,27736,27834", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "15154,15408,15660,15820,15889,15950,16455,16522,16653,16721,16791,16851,16914,16988,17052,17187,17250,17492,18368,18454,19492,19655,19749,23537,23619,24577,25270,25345,25462,26191,26260,27033,27116,27829,27925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,229,361,464,556,669,837,1021,1167,1253,1355,1447,1545,1715,1816,1912,2150,2261,2355,2415,2479,2561,2655,2759,2954,3020,3094,3178,3240,3316,3385,3453,3569,3655,3749,3821,3889,3947,4047,4155,4253,4373,4470,4541,4618,4699,4769,4876,4995", "endColumns": "82,90,131,102,91,112,167,183,145,85,101,91,97,169,100,95,237,110,93,59,63,81,93,103,194,65,73,83,61,75,68,67,115,85,93,71,67,57,99,107,97,119,96,70,76,80,69,106,118,82", "endOffsets": "133,224,356,459,551,664,832,1016,1162,1248,1350,1442,1540,1710,1811,1907,2145,2256,2350,2410,2474,2556,2650,2754,2949,3015,3089,3173,3235,3311,3380,3448,3564,3650,3744,3816,3884,3942,4042,4150,4248,4368,4465,4536,4613,4694,4764,4871,4990,5073"}, "to": {"startLines": "169,248,265,275,276,324,330,331,332,333,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,364,365,366,367,368,369,370,371,372,373,374,394,402,403,404,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14639,21705,23679,24629,24732,28871,29330,29498,29682,29828,30015,30117,30209,30307,30477,30578,30674,30912,31107,31201,31261,31325,31407,31501,31605,31800,31866,31940,32024,32086,32162,32231,32299,32842,32928,33022,33094,33162,33220,33320,33428,33526,33646,33743,36517,37070,37151,37221,37328,37447", "endColumns": "82,90,131,102,91,112,167,183,145,85,101,91,97,169,100,95,237,110,93,59,63,81,93,103,194,65,73,83,61,75,68,67,115,85,93,71,67,57,99,107,97,119,96,70,76,80,69,106,118,82", "endOffsets": "14717,21791,23806,24727,24819,28979,29493,29677,29823,29909,30112,30204,30302,30472,30573,30669,30907,31018,31196,31256,31320,31402,31496,31600,31795,31861,31935,32019,32081,32157,32226,32294,32410,32923,33017,33089,33157,33215,33315,33423,33521,33641,33738,33809,36589,37146,37216,37323,37442,37525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,137,235,301,369,433,506", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "132,230,296,364,428,501,569"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13578,13660,13758,13824,13892,13956,14029", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "13655,13753,13819,13887,13951,14024,14092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,411,456,526,627,699,944,1004,1095,1162,1217,1281,1565,1640,1706,1759,1812,1899,2028,2137,2194,2280,2360,2443,2508,2602,2873,2948,3023,3084,3144,3204,3264,3365,3449,3547,3639,3712,3791,3876,4061,4255,4362,4417,5123,5184", "endColumns": "158,196,44,69,100,71,244,59,90,66,54,63,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,74,74,60,59,59,59,100,83,97,91,72,78,84,184,193,106,54,705,60,54", "endOffsets": "209,406,451,521,622,694,939,999,1090,1157,1212,1276,1560,1635,1701,1754,1807,1894,2023,2132,2189,2275,2355,2438,2503,2597,2868,2943,3018,3079,3139,3199,3259,3360,3444,3542,3634,3707,3786,3871,4056,4250,4357,4412,5118,5179,5234"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,298,299,300,301,302,308,311,316,318,319,320,321,323,325,326,329,334,343,359,360,361,362,363,375,381,382,383,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19858,20017,21021,21066,21136,21237,21309,21554,21614,23379,23624,23811,24249,24824,24899,25350,26265,26468,26555,26684,26793,26850,27403,27653,28095,28236,28330,28601,28676,28810,28984,29044,29270,29914,31023,32415,32513,32605,32678,32757,33814,34364,34558,34665,34787,35493,37015", "endColumns": "158,196,44,69,100,71,244,59,90,66,54,63,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,74,74,60,59,59,59,100,83,97,91,72,78,84,184,193,106,54,705,60,54", "endOffsets": "20012,20209,21061,21131,21232,21304,21549,21609,21700,23441,23674,23870,24528,24894,24960,25398,26313,26550,26679,26788,26845,26931,27478,27731,28155,28325,28596,28671,28746,28866,29039,29099,29325,30010,31102,32508,32600,32673,32752,32837,33994,34553,34660,34715,35488,35549,37065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,207", "endColumns": "77,73,75", "endOffsets": "128,202,278"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4571,7167,7480", "endColumns": "77,73,75", "endOffsets": "4644,7236,7551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,199,309,368,451,506,559,635,732,887,1138,1414,1488,1556,1640,1731,1836,1949,2015,2082,2242,2310,2368,2441,2515,2634,2757,2867,2953,3025,3109,3175", "endColumns": "68,74,109,58,82,54,52,75,96,154,250,275,73,67,83,90,104,112,65,66,159,67,57,72,73,118,122,109,85,71,83,65,150", "endOffsets": "119,194,304,363,446,501,554,630,727,882,1133,1409,1483,1551,1635,1726,1831,1944,2010,2077,2237,2305,2363,2436,2510,2629,2752,2862,2948,3020,3104,3170,3321"}, "to": {"startLines": "168,170,305,322,376,387,388,389,390,391,392,393,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,425,426,427,428", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14570,14722,27121,28751,33999,35554,35609,35662,35738,35835,35990,36241,37530,37604,37672,37756,37847,37952,38065,38131,38273,38433,38501,38559,38632,38706,38825,38948,39058,39144,39216,39300,39366", "endColumns": "68,74,109,58,82,54,52,75,96,154,250,275,73,67,83,90,104,112,65,66,159,67,57,72,73,118,122,109,85,71,83,65,150", "endOffsets": "14634,14792,27226,28805,34077,35604,35657,35733,35830,35985,36236,36512,37599,37667,37751,37842,37947,38060,38126,38193,38428,38496,38554,38627,38701,38820,38943,39053,39139,39211,39295,39361,39512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,70", "endOffsets": "262,333"}, "to": {"startLines": "82,435", "startColumns": "4,4", "startOffsets": "7951,40106", "endColumns": "60,74", "endOffsets": "8007,40176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6876,7646,7743,7852", "endColumns": "97,96,108,98", "endOffsets": "6969,7738,7847,7946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "69,76,144,148,429,433,434", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6808,7393,12746,13027,39517,39950,40030", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "6871,7475,12815,13156,39681,40025,40101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,13321", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,13399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,528,578,658,741,825,923,1021,1107,1185,1264,1347,1442,1535,1602,1689,1776,1866,1976,2057,2144,2225,2328,2408,2504,2593,2678,2766,2873,2951,3033,3137,3209,3274,3944,4590,4667,4786,4890,4945,5042,5133,5200,5292,5386,5443,5527,5576,5655,5754,5831,5901,5948,6028,6121,6166,6211,6281,6339,6400,6577,6749,6873,6938,7023,7101,7195,7279,7365,7441,7530,7606,7683,7772,7823,7951,8000,8054,8121,8184,8252,8319,8390,8477,8542", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,46,79,92,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,76,88,50,127,48,53,66,62,67,66,70,86,64,74", "endOffsets": "119,197,259,328,406,464,523,573,653,736,820,918,1016,1102,1180,1259,1342,1437,1530,1597,1684,1771,1861,1971,2052,2139,2220,2323,2403,2499,2588,2673,2761,2868,2946,3028,3132,3204,3269,3939,4585,4662,4781,4885,4940,5037,5128,5195,5287,5381,5438,5522,5571,5650,5749,5826,5896,5943,6023,6116,6161,6206,6276,6334,6395,6572,6744,6868,6933,7018,7096,7190,7274,7360,7436,7525,7601,7678,7767,7818,7946,7995,8049,8116,8179,8247,8314,8385,8472,8537,8612"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,306,307,309,310,314,315,317,327,328,377,378,379,380,384,395,396,397,398,399,400,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14097,14166,14244,14306,14375,14453,14511,14797,14847,14927,15010,15237,15413,15511,15665,16014,16306,17330,17567,17660,17727,17881,17968,18058,18229,18526,18613,18694,18797,18877,18973,19062,19147,19235,19342,19497,19754,20214,20286,20351,21796,22442,22519,22638,22742,22797,22894,22985,23052,23144,23238,23295,23875,23924,24003,24102,24179,24582,24965,25045,25138,25183,25467,25537,25595,25656,25833,26005,26318,26383,27231,27309,27483,27567,27930,28006,28160,29104,29181,34082,34133,34261,34310,34720,36594,36657,36725,36792,36863,36950,38198", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,46,79,92,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,76,88,50,127,48,53,66,62,67,66,70,86,64,74", "endOffsets": "14161,14239,14301,14370,14448,14506,14565,14842,14922,15005,15089,15330,15506,15592,15738,16088,16384,17420,17655,17722,17809,17963,18053,18163,18305,18608,18689,18792,18872,18968,19057,19142,19230,19337,19415,19574,19853,20281,20346,21016,22437,22514,22633,22737,22792,22889,22980,23047,23139,23233,23290,23374,23919,23998,24097,24174,24244,24624,25040,25133,25178,25223,25532,25590,25651,25828,26000,26124,26378,26463,27304,27398,27562,27648,28001,28090,28231,29176,29265,34128,34256,34305,34359,34782,36652,36720,36787,36858,36945,37010,38268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1036,1126,1193,1252,1342,1406,1470,1533,1602,1666,1720,1832,1890,1952,2006,2078,2200,2287,2368,2508,2585,2666,2793,2884,2961,3015,3066,3132,3202,3279,3366,3441,3512,3589,3658,3727,3834,3925,3997,4086,4175,4249,4321,4407,4457,4536,4602,4682,4766,4828,4892,4955,5024,5124,5219,5311,5403,5461,5516", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "267,349,427,504,590,674,772,887,966,1031,1121,1188,1247,1337,1401,1465,1528,1597,1661,1715,1827,1885,1947,2001,2073,2195,2282,2363,2503,2580,2661,2788,2879,2956,3010,3061,3127,3197,3274,3361,3436,3507,3584,3653,3722,3829,3920,3992,4081,4170,4244,4316,4402,4452,4531,4597,4677,4761,4823,4887,4950,5019,5119,5214,5306,5398,5456,5511,5589"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,7328,7556,8012,8250,8309,8399,8463,8527,8590,8659,8723,8777,8889,8947,9009,9063,9135,9257,9344,9425,9565,9642,9723,9850,9941,10018,10072,10123,10189,10259,10336,10423,10498,10569,10646,10715,10784,10891,10982,11054,11143,11232,11306,11378,11464,11514,11593,11659,11739,11823,11885,11949,12012,12081,12181,12276,12368,12460,12518,12949", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,7388,7641,8074,8304,8394,8458,8522,8585,8654,8718,8772,8884,8942,9004,9058,9130,9252,9339,9420,9560,9637,9718,9845,9936,10013,10067,10118,10184,10254,10331,10418,10493,10564,10641,10710,10779,10886,10977,11049,11138,11227,11301,11373,11459,11509,11588,11654,11734,11818,11880,11944,12007,12076,12176,12271,12363,12455,12513,12568,13022"}}]}]}