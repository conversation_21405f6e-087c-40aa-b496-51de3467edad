import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';
import '../repositories/branch_repository.dart';

/// Update branch use case
class UpdateBranch {
  final BranchRepository _repository;

  UpdateBranch(this._repository);

  /// Call the use case
  Future<Either<Failure, BranchEntity>> call(Params params) {
    return _repository.updateBranch(params.branch);
  }
}

/// Update branch parameters
class Params extends Equatable {
  final BranchEntity branch;

  const Params({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}
