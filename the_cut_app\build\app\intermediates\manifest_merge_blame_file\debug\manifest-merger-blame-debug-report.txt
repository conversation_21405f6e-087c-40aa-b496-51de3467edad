1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.thecut.the_cut_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
30-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
32-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent> <!-- Added to check the default browser that will host the AuthFlow. -->
34        <intent>
34-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:13:9-17:18
35            <action android:name="android.intent.action.VIEW" />
35-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:13-65
35-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:21-62
36
37            <data android:scheme="http" />
37-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
38        </intent>
39        <intent>
39-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
40            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
40-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
41        </intent>
42        <intent>
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
43            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
44        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
45        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
45-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:11:9-54
45-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:11:18-51
46        <package android:name="com.google.android.apps.maps" />
46-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:9-64
46-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:18-61
47    </queries>
48
49    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
49-->[:connectivity_plus] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
49-->[:connectivity_plus] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
50    <uses-permission
50-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
51        android:name="android.permission.READ_EXTERNAL_STORAGE"
51-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
52        android:maxSdkVersion="32" />
52-->[:file_picker] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
53    <uses-permission android:name="com.android.vending.BILLING" />
53-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
53-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
54
55    <uses-feature
55-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:26:5-28:35
56        android:glEsVersion="0x00020000"
56-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:27:9-41
57        android:required="true" />
57-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:28:9-32
58
59    <permission
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
60        android:name="com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
64
65    <application
66        android:name="android.app.Application"
67        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
68        android:debuggable="true"
69        android:extractNativeLibs="true"
70        android:icon="@mipmap/ic_launcher"
71        android:label="the_cut_app" >
72        <activity
73            android:name="com.thecut.the_cut_app.MainActivity"
74            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
75            android:exported="true"
76            android:hardwareAccelerated="true"
77            android:launchMode="singleTop"
78            android:taskAffinity=""
79            android:theme="@style/LaunchTheme"
80            android:windowSoftInputMode="adjustResize" >
81
82            <!--
83                 Specifies an Android theme to apply to this Activity as soon as
84                 the Android process has started. This theme is visible to the user
85                 while the Flutter UI initializes. After that, this theme continues
86                 to determine the Window background behind the Flutter UI.
87            -->
88            <meta-data
89                android:name="io.flutter.embedding.android.NormalTheme"
90                android:resource="@style/NormalTheme" />
91
92            <intent-filter>
93                <action android:name="android.intent.action.MAIN" />
94
95                <category android:name="android.intent.category.LAUNCHER" />
96            </intent-filter>
97        </activity>
98        <!--
99             Don't delete the meta-data below.
100             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
101        -->
102        <meta-data
103            android:name="flutterEmbedding"
104            android:value="2" />
105
106        <provider
106-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
107            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
107-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
108            android:authorities="com.thecut.the_cut_app.flutter.image_provider"
108-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
109            android:exported="false"
109-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
110            android:grantUriPermissions="true" >
110-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
111            <meta-data
111-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
112                android:name="android.support.FILE_PROVIDER_PATHS"
112-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
113                android:resource="@xml/flutter_image_picker_file_paths" />
113-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
114        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
115        <service
115-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
116            android:name="com.google.android.gms.metadata.ModuleDependencies"
116-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
117            android:enabled="false"
117-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
118            android:exported="false" >
118-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
119            <intent-filter>
119-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
120                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
120-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
120-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
121            </intent-filter>
122
123            <meta-data
123-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
124                android:name="photopicker_activity:0:required"
124-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
125                android:value="" />
125-->[:image_picker_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
126        </service>
127
128        <meta-data
128-->[:pay_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-14:36
129            android:name="com.google.android.gms.wallet.api.enabled"
129-->[:pay_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-69
130            android:value="true" />
130-->[:pay_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
131
132        <activity
132-->[:url_launcher_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
133            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
133-->[:url_launcher_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
134            android:exported="false"
134-->[:url_launcher_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
135            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
135-->[:url_launcher_android] D:\All Projects Apps Khamsat\App ********\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
136        <activity
136-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:21:9-65:20
137            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
137-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:22:13-109
138            android:exported="true"
138-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:23:13-36
139            android:launchMode="singleTask" >
139-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:24:13-44
140            <intent-filter>
140-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:25:13-64:29
141                <action android:name="android.intent.action.VIEW" />
141-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:13-65
141-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:21-62
142
143                <category android:name="android.intent.category.DEFAULT" />
143-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:17-76
143-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:27-73
144                <category android:name="android.intent.category.BROWSABLE" />
144-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:17-78
144-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:27-75
145
146                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
147                <data
147-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
148                    android:host="link-accounts"
149                    android:pathPrefix="/com.thecut.the_cut_app/authentication_return"
150                    android:scheme="stripe-auth" />
151
152                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
153                <data
153-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
154                    android:host="link-native-accounts"
155                    android:pathPrefix="/com.thecut.the_cut_app/authentication_return"
156                    android:scheme="stripe-auth" />
157
158                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
159                <data
159-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
160                    android:host="link-accounts"
161                    android:path="/com.thecut.the_cut_app/success"
162                    android:scheme="stripe-auth" />
163                <data
163-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
164                    android:host="link-accounts"
165                    android:path="/com.thecut.the_cut_app/cancel"
166                    android:scheme="stripe-auth" />
167
168                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
169                <data
169-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
170                    android:host="native-redirect"
171                    android:pathPrefix="/com.thecut.the_cut_app"
172                    android:scheme="stripe-auth" />
173
174                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
175                <data
175-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
176                    android:host="auth-redirect"
177                    android:pathPrefix="/com.thecut.the_cut_app"
178                    android:scheme="stripe" />
179            </intent-filter>
180        </activity>
181        <activity
181-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:66:9-69:77
182            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
182-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:67:13-101
183            android:exported="false"
183-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:68:13-37
184            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
184-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:69:13-74
185        <activity
185-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:70:9-74:58
186            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
186-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:71:13-110
187            android:exported="false"
187-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:72:13-37
188            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
188-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:73:13-74
189            android:windowSoftInputMode="adjustResize" />
189-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:74:13-55
190
191        <meta-data
191-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
192            android:name="com.google.android.play.billingclient.version"
192-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
193            android:value="7.1.1" />
193-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
194
195        <activity
195-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
196            android:name="com.android.billingclient.api.ProxyBillingActivity"
196-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
197            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
197-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
198            android:exported="false"
198-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
199            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
199-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
200        <activity
200-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
201            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
201-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
202            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
202-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
203            android:exported="false"
203-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
204            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
204-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
205        <activity
205-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:8:9-12:58
206            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
206-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:9:13-80
207            android:exported="false"
207-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:10:13-37
208            android:theme="@style/StripePaymentSheetDefaultTheme"
208-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:11:13-66
209            android:windowSoftInputMode="adjustResize" />
209-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:12:13-55
210        <activity
210-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:13:9-17:58
211            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
211-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:14:13-82
212            android:exported="false"
212-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:15:13-37
213            android:theme="@style/StripePaymentSheetDefaultTheme"
213-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:16:13-66
214            android:windowSoftInputMode="adjustResize" />
214-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:17:13-55
215        <activity
215-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:18:9-22:58
216            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
216-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:19:13-82
217            android:exported="false"
217-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:20:13-37
218            android:theme="@style/StripePaymentSheetDefaultTheme"
218-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:21:13-66
219            android:windowSoftInputMode="adjustResize" />
219-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:22:13-55
220        <activity
220-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:23:9-27:58
221            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
221-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:24:13-97
222            android:exported="false"
222-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:25:13-37
223            android:theme="@style/StripePaymentSheetDefaultTheme"
223-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:26:13-66
224            android:windowSoftInputMode="adjustResize" />
224-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:27:13-55
225        <activity
225-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:28:9-31:69
226            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
226-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:29:13-118
227            android:exported="false"
227-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:30:13-37
228            android:theme="@style/StripePaymentSheetDefaultTheme" />
228-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:31:13-66
229        <activity
229-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:32:9-35:69
230            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
230-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:33:13-105
231            android:exported="false"
231-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:34:13-37
232            android:theme="@style/StripePaymentSheetDefaultTheme" />
232-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:35:13-66
233        <activity
233-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:36:9-39:69
234            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
234-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:37:13-82
235            android:exported="false"
235-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:38:13-37
236            android:theme="@style/StripePaymentSheetDefaultTheme" />
236-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:39:13-66
237        <activity
237-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:40:9-43:68
238            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
238-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:41:13-94
239            android:exported="false"
239-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:42:13-37
240            android:theme="@style/StripePayLauncherDefaultTheme" />
240-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:43:13-65
241        <activity
241-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:44:9-47:58
242            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
242-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:45:13-121
243            android:theme="@style/StripePaymentSheetDefaultTheme"
243-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:46:13-66
244            android:windowSoftInputMode="adjustResize" />
244-->[com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:47:13-55
245        <activity
245-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:8:9-13:61
246            android:name="com.stripe.android.link.LinkForegroundActivity"
246-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:9:13-74
247            android:autoRemoveFromRecents="true"
247-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:10:13-49
248            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
248-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:11:13-115
249            android:launchMode="singleTop"
249-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:12:13-43
250            android:theme="@style/StripeTransparentTheme" />
250-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:13:13-58
251        <activity
251-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:14:9-31:20
252            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
252-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:15:13-79
253            android:autoRemoveFromRecents="true"
253-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:16:13-49
254            android:exported="true"
254-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:17:13-36
255            android:launchMode="singleInstance"
255-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:18:13-48
256            android:theme="@style/StripeTransparentTheme" >
256-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:19:13-58
257            <intent-filter>
257-->[com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:20:13-30:29
258                <action android:name="android.intent.action.VIEW" />
258-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:13-65
258-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:21-62
259
260                <category android:name="android.intent.category.DEFAULT" />
260-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:17-76
260-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:27-73
261                <category android:name="android.intent.category.BROWSABLE" />
261-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:17-78
261-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:27-75
262
263                <data
263-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
264                    android:host="complete"
265                    android:path="/com.thecut.the_cut_app"
266                    android:scheme="link-popup" />
267            </intent-filter>
268        </activity>
269        <activity
269-->[com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:8:9-11:69
270            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
270-->[com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:9:13-80
271            android:exported="false"
271-->[com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:10:13-37
272            android:theme="@style/StripePaymentSheetDefaultTheme" />
272-->[com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:11:13-66
273        <activity
273-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:15:9-18:57
274            android:name="com.stripe.android.view.AddPaymentMethodActivity"
274-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:16:13-76
275            android:exported="false"
275-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:17:13-37
276            android:theme="@style/StripeDefaultTheme" />
276-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:18:13-54
277        <activity
277-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:19:9-22:57
278            android:name="com.stripe.android.view.PaymentMethodsActivity"
278-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:20:13-74
279            android:exported="false"
279-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:21:13-37
280            android:theme="@style/StripeDefaultTheme" />
280-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:22:13-54
281        <activity
281-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:23:9-26:57
282            android:name="com.stripe.android.view.PaymentFlowActivity"
282-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:24:13-71
283            android:exported="false"
283-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:25:13-37
284            android:theme="@style/StripeDefaultTheme" />
284-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:26:13-54
285        <activity
285-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:27:9-31:58
286            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
286-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:28:13-78
287            android:exported="false"
287-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:29:13-37
288            android:theme="@style/StripeDefaultTheme"
288-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:30:13-54
289            android:windowSoftInputMode="adjustResize" />
289-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:31:13-55
290        <activity
290-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:32:9-35:61
291            android:name="com.stripe.android.view.PaymentRelayActivity"
291-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:33:13-72
292            android:exported="false"
292-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:34:13-37
293            android:theme="@style/StripeTransparentTheme" />
293-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:35:13-58
294        <!--
295        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
296        launched the browser Activity will also handle the return URL deep link.
297        -->
298        <activity
298-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:41:9-45:61
299            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
299-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:42:13-85
300            android:exported="false"
300-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:43:13-37
301            android:launchMode="singleTask"
301-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:44:13-44
302            android:theme="@style/StripeTransparentTheme" />
302-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:45:13-58
303        <activity
303-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:46:9-63:20
304            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
304-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:47:13-88
305            android:exported="true"
305-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:48:13-36
306            android:launchMode="singleTask"
306-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:49:13-44
307            android:theme="@style/StripeTransparentTheme" >
307-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:50:13-58
308            <intent-filter>
308-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:51:13-62:29
309                <action android:name="android.intent.action.VIEW" />
309-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:13-65
309-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:21-62
310
311                <category android:name="android.intent.category.DEFAULT" />
311-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:17-76
311-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:27-73
312                <category android:name="android.intent.category.BROWSABLE" />
312-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:17-78
312-->[com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:27-75
313
314                <!-- Must match `DefaultReturnUrl#value`. -->
315                <data
315-->D:\All Projects Apps Khamsat\App ********\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
316                    android:host="payment_return_url"
317                    android:path="/com.thecut.the_cut_app"
318                    android:scheme="stripesdk" />
319            </intent-filter>
320        </activity>
321        <activity
321-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:64:9-67:57
322            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
322-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:65:13-114
323            android:exported="false"
323-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:66:13-37
324            android:theme="@style/StripeDefaultTheme" />
324-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:67:13-54
325        <activity
325-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:68:9-71:66
326            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
326-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:69:13-90
327            android:exported="false"
327-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:70:13-37
328            android:theme="@style/StripeGooglePayDefaultTheme" />
328-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:71:13-63
329        <activity
329-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:72:9-75:66
330            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
330-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:73:13-103
331            android:exported="false"
331-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:74:13-37
332            android:theme="@style/StripeGooglePayDefaultTheme" />
332-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:75:13-63
333        <activity
333-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:76:9-79:68
334            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
334-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:77:13-107
335            android:exported="false"
335-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:78:13-37
336            android:theme="@style/StripePayLauncherDefaultTheme" />
336-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:79:13-65
337        <activity
337-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:80:9-83:61
338            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
338-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:81:13-97
339            android:exported="false"
339-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:82:13-37
340            android:theme="@style/StripeTransparentTheme" /> <!-- Needs to be explicitly declared on P+ -->
340-->[com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:83:13-58
341        <uses-library
341-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:39:9-41:40
342            android:name="org.apache.http.legacy"
342-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:40:13-50
343            android:required="false" />
343-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:41:13-37
344
345        <activity
345-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
346            android:name="com.google.android.gms.common.api.GoogleApiActivity"
346-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
347            android:exported="false"
347-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
349
350        <uses-library
350-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
351            android:name="androidx.window.extensions"
351-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
352            android:required="false" />
352-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
353        <uses-library
353-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
354            android:name="androidx.window.sidecar"
354-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
355            android:required="false" />
355-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
356
357        <activity
357-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
358            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
358-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
359            android:exported="false"
359-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
360            android:theme="@style/Stripe3DS2Theme" />
360-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
361
362        <provider
362-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
363            android:name="androidx.startup.InitializationProvider"
363-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
364            android:authorities="com.thecut.the_cut_app.androidx-startup"
364-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
365            android:exported="false" >
365-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
366            <meta-data
366-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
367                android:name="androidx.emoji2.text.EmojiCompatInitializer"
367-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
368                android:value="androidx.startup" />
368-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
369            <meta-data
369-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
370                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
370-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
371                android:value="androidx.startup" />
371-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
372            <meta-data
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
373                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
374                android:value="androidx.startup" />
374-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
375        </provider>
376
377        <meta-data
377-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
378            android:name="com.google.android.gms.version"
378-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
379            android:value="@integer/google_play_services_version" />
379-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
380
381        <receiver
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
382            android:name="androidx.profileinstaller.ProfileInstallReceiver"
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
383            android:directBootAware="false"
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
384            android:enabled="true"
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
385            android:exported="true"
385-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
386            android:permission="android.permission.DUMP" >
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
387            <intent-filter>
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
388                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
388-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
388-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
389            </intent-filter>
390            <intent-filter>
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
391                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
391-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
391-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
392            </intent-filter>
393            <intent-filter>
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
394                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
394-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
394-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
395            </intent-filter>
396            <intent-filter>
396-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
397                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
397-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
397-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
398            </intent-filter>
399        </receiver>
400
401        <service
401-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
402            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
402-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
403            android:exported="false" >
403-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
404            <meta-data
404-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
405                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
405-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
406                android:value="cct" />
406-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
407        </service>
408        <service
408-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
409            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
409-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
410            android:exported="false"
410-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
411            android:permission="android.permission.BIND_JOB_SERVICE" >
411-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
412        </service>
413
414        <receiver
414-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
415            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
415-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
416            android:exported="false" />
416-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
417
418        <meta-data
418-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
419            android:name="aia-compat-api-min-version"
419-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
420            android:value="1" />
420-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
421    </application>
422
423</manifest>
