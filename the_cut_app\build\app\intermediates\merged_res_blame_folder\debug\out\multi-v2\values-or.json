{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7130,7916,8021,8134", "endColumns": "109,104,112,108", "endOffsets": "7235,8016,8129,8238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1022,1111,1176,1235,1321,1385,1449,1512,1585,1649,1703,1815,1873,1935,1989,2061,2183,2270,2356,2496,2573,2654,2781,2872,2949,3003,3054,3120,3190,3267,3354,3429,3500,3577,3646,3715,3822,3913,3985,4074,4163,4237,4309,4395,4445,4524,4590,4670,4754,4816,4880,4943,5012,5112,5207,5299,5391,5449,5504", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "267,349,427,504,590,674,768,873,952,1017,1106,1171,1230,1316,1380,1444,1507,1580,1644,1698,1810,1868,1930,1984,2056,2178,2265,2351,2491,2568,2649,2776,2867,2944,2998,3049,3115,3185,3262,3349,3424,3495,3572,3641,3710,3817,3908,3980,4069,4158,4232,4304,4390,4440,4519,4585,4665,4749,4811,4875,4938,5007,5107,5202,5294,5386,5444,5499,5583"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3086,3168,3246,3323,3409,4228,4322,4427,7589,7827,8243,8483,8542,8628,8692,8756,8819,8892,8956,9010,9122,9180,9242,9296,9368,9490,9577,9663,9803,9880,9961,10088,10179,10256,10310,10361,10427,10497,10574,10661,10736,10807,10884,10953,11022,11129,11220,11292,11381,11470,11544,11616,11702,11752,11831,11897,11977,12061,12123,12187,12250,12319,12419,12514,12606,12698,12756,13206", "endLines": "5,33,34,35,36,37,45,46,47,75,78,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "317,3163,3241,3318,3404,3488,4317,4422,4501,7649,7911,8303,8537,8623,8687,8751,8814,8887,8951,9005,9117,9175,9237,9291,9363,9485,9572,9658,9798,9875,9956,10083,10174,10251,10305,10356,10422,10492,10569,10656,10731,10802,10879,10948,11017,11124,11215,11287,11376,11465,11539,11611,11697,11747,11826,11892,11972,12056,12118,12182,12245,12314,12414,12509,12601,12693,12751,12806,13285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "48,49,71,72,74,83,84,141,142,144,145,148,149,151,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4506,4603,7240,7332,7503,8308,8385,12811,12899,13066,13136,13429,13507,13679,14102,14185,14252", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "4598,4685,7327,7427,7584,8380,8478,12894,12981,13131,13201,13502,13584,13744,14180,14247,14366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,212", "endColumns": "85,70,83", "endOffsets": "136,207,291"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4690,7432,7743", "endColumns": "85,70,83", "endOffsets": "4771,7498,7822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4776,4887,5048,5180,5297,5452,5587,5701,5951,6118,6231,6392,6525,6675,6832,6897,6969", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "4882,5043,5175,5292,5447,5582,5696,5806,6113,6226,6387,6520,6670,6827,6892,6964,7051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "153", "startColumns": "4", "startOffsets": "13850", "endColumns": "82", "endOffsets": "13928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5811", "endColumns": "139", "endOffsets": "5946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3596,3698,3801,3906,4007,4109,13749", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3591,3693,3796,3901,4002,4104,4223,13845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,268,348,487,656,737", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "174,263,343,482,651,732,814"}, "to": {"startLines": "69,76,143,147,154,158,159", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7056,7654,12986,13290,13933,14371,14452", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "7125,7738,13061,13424,14097,14447,14529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,441,551,658,744,848,968,1047,1128,1219,1312,1415,1510,1610,1703,1798,1894,1985,2075,2164,2274,2378,2484,2595,2699,2817,2980,13589", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "436,546,653,739,843,963,1042,1123,1214,1307,1410,1505,1605,1698,1793,1889,1980,2070,2159,2269,2373,2479,2590,2694,2812,2975,3081,13674"}}]}]}