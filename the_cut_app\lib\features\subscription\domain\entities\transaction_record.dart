import 'package:equatable/equatable.dart';

/// Transaction record entity
class TransactionRecord extends Equatable {
  /// Unique identifier for the transaction
  final String id;

  /// User ID associated with this transaction
  final String userId;

  /// Subscriber ID associated with this transaction
  final String subscriberId;

  /// Subscription plan ID
  final String subscriptionPlanId;

  /// Transaction amount
  final double amount;

  /// Currency code (e.g., USD, EUR, SAR)
  final String currencyCode;

  /// Transaction status (e.g., success, failed, pending, refunded)
  final String status;

  /// Transaction type (e.g., subscription_purchase, subscription_renewal, refund)
  final String type;

  /// Payment method used (e.g., credit_card, paypal, apple_pay, google_pay)
  final String paymentMethod;

  /// Payment processor (e.g., stripe, paypal, apple, google)
  final String paymentProcessor;

  /// Payment processor transaction ID
  final String processorTransactionId;

  /// Error message (if transaction failed)
  final String? errorMessage;

  /// Error code (if transaction failed)
  final String? errorCode;

  /// Billing address
  final String? billingAddress;

  /// Billing city
  final String? billingCity;

  /// Billing state/province
  final String? billingState;

  /// Billing postal code
  final String? billingPostalCode;

  /// Billing country
  final String? billingCountry;

  /// IP address of the user at the time of transaction
  final String? ipAddress;

  /// User agent of the user at the time of transaction
  final String? userAgent;

  /// Receipt URL
  final String? receiptUrl;

  /// Invoice URL
  final String? invoiceUrl;

  /// Receipt ID from the app store or play store
  final String? storeReceiptId;

  /// Original transaction ID from the app store or play store
  final String? originalTransactionId;

  /// Date when the transaction was created
  final DateTime createdAt;

  /// Date when the transaction was last updated
  final DateTime updatedAt;

  /// Constructor
  const TransactionRecord({
    required this.id,
    required this.userId,
    required this.subscriberId,
    required this.subscriptionPlanId,
    required this.amount,
    required this.currencyCode,
    required this.status,
    required this.type,
    required this.paymentMethod,
    required this.paymentProcessor,
    required this.processorTransactionId,
    this.errorMessage,
    this.errorCode,
    this.billingAddress,
    this.billingCity,
    this.billingState,
    this.billingPostalCode,
    this.billingCountry,
    this.ipAddress,
    this.userAgent,
    this.receiptUrl,
    this.invoiceUrl,
    this.storeReceiptId,
    this.originalTransactionId,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        subscriberId,
        subscriptionPlanId,
        amount,
        currencyCode,
        status,
        type,
        paymentMethod,
        paymentProcessor,
        processorTransactionId,
        errorMessage,
        errorCode,
        billingAddress,
        billingCity,
        billingState,
        billingPostalCode,
        billingCountry,
        ipAddress,
        userAgent,
        receiptUrl,
        invoiceUrl,
        storeReceiptId,
        originalTransactionId,
        createdAt,
        updatedAt,
      ];
}
