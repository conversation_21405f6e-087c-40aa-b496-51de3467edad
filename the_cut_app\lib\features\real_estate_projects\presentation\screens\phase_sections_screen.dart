import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../domain/entities/project_entity.dart';
import '../../domain/entities/phase_entity.dart';

/// شاشة عرض أقسام المرحلة
class PhaseSectionsScreen extends StatefulWidget {
  final PropertyType propertyType;
  final ConstructionPhase phase;

  const PhaseSectionsScreen({
    super.key,
    required this.propertyType,
    required this.phase,
  });

  @override
  State<PhaseSectionsScreen> createState() => _PhaseSectionsScreenState();
}

class _PhaseSectionsScreenState extends State<PhaseSectionsScreen> {
  late List<PhaseSection> _availableSections;

  @override
  void initState() {
    super.initState();
    _availableSections = _getSectionsForPhase(widget.phase);
  }

  List<PhaseSection> _getSectionsForPhase(ConstructionPhase phase) {
    switch (phase) {
      case ConstructionPhase.blackWork:
        return [
          PhaseSection.plans,
          PhaseSection.licenses,
          PhaseSection.schedules,
          PhaseSection.images,
          PhaseSection.documents,
          PhaseSection.reports,
        ];
      case ConstructionPhase.civilWork:
        return [
          PhaseSection.plans,
          PhaseSection.schedules,
          PhaseSection.images,
          PhaseSection.videos,
          PhaseSection.documents,
          PhaseSection.reports,
          PhaseSection.notes,
        ];
      case ConstructionPhase.finishing:
        return [
          PhaseSection.plans,
          PhaseSection.schedules,
          PhaseSection.images,
          PhaseSection.videos,
          PhaseSection.documents,
          PhaseSection.notes,
        ];
    }
  }

  void _navigateToSectionContent(PhaseSection section) {
    Navigator.pushNamed(
      context,
      '/section-content',
      arguments: {
        'propertyType': widget.propertyType,
        'phase': widget.phase,
        'section': section,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(widget.phase.nameAr),
        backgroundColor: AppColors.background,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  children: [
                    Icon(
                      _getPhaseIcon(widget.phase),
                      size: 48.w,
                      color: Colors.white,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      widget.phase.nameAr,
                      style: AppTextStyles.h3.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'اختر القسم الذي تريد إدارته',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),

              // Sections grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16.w,
                    mainAxisSpacing: 16.h,
                    childAspectRatio: 1.1,
                  ),
                  itemCount: _availableSections.length,
                  itemBuilder: (context, index) {
                    final section = _availableSections[index];
                    return _buildSectionCard(section);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard(PhaseSection section) {
    final sectionInfo = _getSectionInfo(section);

    return GestureDetector(
      onTap: () => _navigateToSectionContent(section),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: AppColors.border,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: (sectionInfo['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                sectionInfo['icon'],
                size: 30.w,
                color: sectionInfo['color'],
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              section.nameAr,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              sectionInfo['description'],
              style: AppTextStyles.caption.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPhaseIcon(ConstructionPhase phase) {
    switch (phase) {
      case ConstructionPhase.blackWork:
        return Icons.foundation;
      case ConstructionPhase.civilWork:
        return Icons.electrical_services;
      case ConstructionPhase.finishing:
        return Icons.palette;
    }
  }

  Map<String, dynamic> _getSectionInfo(PhaseSection section) {
    switch (section) {
      case PhaseSection.plans:
        return {
          'icon': Icons.architecture,
          'description': 'المخططات والرسوم الهندسية',
          'color': const Color(0xFF1976D2),
        };
      case PhaseSection.licenses:
        return {
          'icon': Icons.verified_user,
          'description': 'التراخيص والموافقات',
          'color': const Color(0xFF388E3C),
        };
      case PhaseSection.schedules:
        return {
          'icon': Icons.schedule,
          'description': 'المواعيد والجدولة',
          'color': const Color(0xFFE65100),
        };
      case PhaseSection.documents:
        return {
          'icon': Icons.description,
          'description': 'المستندات والملفات',
          'color': const Color(0xFF7B1FA2),
        };
      case PhaseSection.images:
        return {
          'icon': Icons.image,
          'description': 'الصور والمرفقات',
          'color': const Color(0xFFD32F2F),
        };
      case PhaseSection.videos:
        return {
          'icon': Icons.videocam,
          'description': 'الفيديوهات والتسجيلات',
          'color': const Color(0xFF1976D2),
        };
      case PhaseSection.reports:
        return {
          'icon': Icons.assessment,
          'description': 'التقارير والتحليلات',
          'color': const Color(0xFF455A64),
        };
      case PhaseSection.notes:
        return {
          'icon': Icons.note,
          'description': 'الملاحظات والتعليقات',
          'color': const Color(0xFF6A1B9A),
        };
    }
  }
}
