import 'package:equatable/equatable.dart';

/// Authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Check authentication status event
class CheckAuthStatusEvent extends AuthEvent {
  const CheckAuthStatusEvent();
}

/// Sign in with email and password event
class SignInWithEmailAndPasswordEvent extends AuthEvent {
  final String email;
  final String password;

  const SignInWithEmailAndPasswordEvent({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

/// Register with email and password event
class RegisterWithEmailAndPasswordEvent extends AuthEvent {
  final String email;
  final String password;
  final String name;

  const RegisterWithEmailAndPasswordEvent({
    required this.email,
    required this.password,
    required this.name,
  });

  @override
  List<Object?> get props => [email, password, name];
}

/// Sign out event
class SignOutEvent extends AuthEvent {
  const SignOutEvent();
}

/// Send password reset email event
class SendPasswordResetEmailEvent extends AuthEvent {
  final String email;

  const SendPasswordResetEmailEvent({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

/// Update profile event
class UpdateProfileEvent extends AuthEvent {
  final String? displayName;
  final String? photoURL;
  final String? phoneNumber;

  const UpdateProfileEvent({
    this.displayName,
    this.photoURL,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [displayName, photoURL, phoneNumber];
}

/// Update email event
class UpdateEmailEvent extends AuthEvent {
  final String email;

  const UpdateEmailEvent({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

/// Update password event
class UpdatePasswordEvent extends AuthEvent {
  final String password;

  const UpdatePasswordEvent({
    required this.password,
  });

  @override
  List<Object?> get props => [password];
}

/// Delete account event
class DeleteAccountEvent extends AuthEvent {
  const DeleteAccountEvent();
}
