import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';

/// Button types
enum ButtonType {
  primary,
  secondary,
  outlined,
  text,
}

/// Button sizes
enum ButtonSize {
  small,
  medium,
  large,
}

/// Custom button
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final IconData? icon;
  final bool iconLeading;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double? elevation;

  // Alias property for compatibility
  final Color? color;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.iconLeading = true,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.elevation,

    // Alias property for compatibility
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    // Determine button style based on type
    final ButtonStyle buttonStyle = _getButtonStyle();

    // Determine text style based on type and size
    final TextStyle textStyle = _getTextStyle();

    // Determine button size
    final double buttonHeight = height ?? _getButtonHeight();
    final double? buttonWidth = isFullWidth ? double.infinity : width;

    // Build button content
    Widget buttonContent = Text(
      text,
      style: textStyle,
      textAlign: TextAlign.center,
    );

    // Add icon if provided
    if (icon != null) {
      final iconWidget = Icon(
        icon,
        color: textStyle.color,
        size: _getIconSize(),
      );

      buttonContent = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: iconLeading
            ? [
                iconWidget,
                SizedBox(width: 8.w),
                buttonContent,
              ]
            : [
                buttonContent,
                SizedBox(width: 8.w),
                iconWidget,
              ],
      );
    }

    // Add loading indicator if loading
    if (isLoading) {
      buttonContent = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: _getIconSize(),
            height: _getIconSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2.w,
              valueColor: AlwaysStoppedAnimation<Color>(textStyle.color!),
            ),
          ),
          SizedBox(width: 8.w),
          buttonContent,
        ],
      );
    }

    return SizedBox(
      width: buttonWidth,
      height: buttonHeight,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonContent,
      ),
    );
  }

  ButtonStyle _getButtonStyle() {
    // Use color alias if provided
    final Color? bgColor = color ?? backgroundColor;

    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: bgColor ?? AppColors.primary,
          foregroundColor: textColor ?? Colors.white,
          elevation: elevation ?? 2,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8.r),
          ),
        );
      case ButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: bgColor ?? AppColors.accent,
          foregroundColor: textColor ?? Colors.white,
          elevation: elevation ?? 2,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8.r),
          ),
        );
      case ButtonType.outlined:
        return ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: textColor ?? AppColors.primary,
          elevation: elevation ?? 0,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8.r),
            side: BorderSide(
              color: borderColor ?? AppColors.primary,
              width: 1.w,
            ),
          ),
        );
      case ButtonType.text:
        return ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: textColor ?? AppColors.primary,
          elevation: elevation ?? 0,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8.r),
          ),
          shadowColor: Colors.transparent,
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return type == ButtonType.text
            ? AppTextStyles.buttonSmall.copyWith(
                color: textColor ?? AppColors.primary,
              )
            : AppTextStyles.buttonSmall.copyWith(
                color: textColor ??
                    (type == ButtonType.outlined
                        ? AppColors.primary
                        : Colors.white),
              );
      case ButtonSize.medium:
        return type == ButtonType.text
            ? AppTextStyles.buttonMedium.copyWith(
                color: textColor ?? AppColors.primary,
              )
            : AppTextStyles.buttonMedium.copyWith(
                color: textColor ??
                    (type == ButtonType.outlined
                        ? AppColors.primary
                        : Colors.white),
              );
      case ButtonSize.large:
        return type == ButtonType.text
            ? AppTextStyles.buttonLarge.copyWith(
                color: textColor ?? AppColors.primary,
              )
            : AppTextStyles.buttonLarge.copyWith(
                color: textColor ??
                    (type == ButtonType.outlined
                        ? AppColors.primary
                        : Colors.white),
              );
    }
  }

  double _getButtonHeight() {
    switch (size) {
      case ButtonSize.small:
        return 32.h;
      case ButtonSize.medium:
        return 44.h;
      case ButtonSize.large:
        return 56.h;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16.r;
      case ButtonSize.medium:
        return 20.r;
      case ButtonSize.large:
        return 24.r;
    }
  }
}
