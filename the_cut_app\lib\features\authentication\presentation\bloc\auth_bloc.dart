import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_current_user.dart';
import '../../domain/usecases/register_with_email_and_password.dart' as register;
import '../../domain/usecases/sign_in_with_email_and_password.dart' as sign_in;
import '../../domain/usecases/sign_out.dart';
import '../../domain/usecases/update_profile.dart' as update_profile;
import 'auth_event.dart';
import 'auth_state.dart';

/// Authentication BLoC
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final GetCurrentUser _getCurrentUser;
  final sign_in.SignInWithEmailAndPassword _signInWithEmailAndPassword;
  final register.RegisterWithEmailAndPassword _registerWithEmailAndPassword;
  final SignOut _signOut;
  final update_profile.UpdateProfile _updateProfile;

  AuthBloc({
    required GetCurrentUser getCurrentUser,
    required sign_in.SignInWithEmailAndPassword signInWithEmailAndPassword,
    required register.RegisterWithEmailAndPassword registerWithEmailAndPassword,
    required SignOut signOut,
    required update_profile.UpdateProfile updateProfile,
  })  : _getCurrentUser = getCurrentUser,
        _signInWithEmailAndPassword = signInWithEmailAndPassword,
        _registerWithEmailAndPassword = registerWithEmailAndPassword,
        _signOut = signOut,
        _updateProfile = updateProfile,
        super(const AuthInitialState()) {
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    on<SignInWithEmailAndPasswordEvent>(_onSignInWithEmailAndPassword);
    on<RegisterWithEmailAndPasswordEvent>(_onRegisterWithEmailAndPassword);
    on<SignOutEvent>(_onSignOut);
    on<UpdateProfileEvent>(_onUpdateProfile);
  }

  /// Handle check authentication status event
  Future<void> _onCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoadingState());

    final result = await _getCurrentUser();

    result.fold(
      (failure) => emit(const UnauthenticatedState()),
      (user) {
        if (user != null) {
          emit(AuthenticatedState(user: user));
        } else {
          emit(const UnauthenticatedState());
        }
      },
    );
  }

  /// Handle sign in with email and password event
  Future<void> _onSignInWithEmailAndPassword(
    SignInWithEmailAndPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoadingState());

    final result = await _signInWithEmailAndPassword(
      sign_in.Params(
        email: event.email,
        password: event.password,
      ),
    );

    result.fold(
      (failure) => emit(AuthErrorState(message: failure.message)),
      (user) => emit(AuthenticatedState(user: user)),
    );
  }

  /// Handle register with email and password event
  Future<void> _onRegisterWithEmailAndPassword(
    RegisterWithEmailAndPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoadingState());

    final result = await _registerWithEmailAndPassword(
      register.Params(
        email: event.email,
        password: event.password,
        name: event.name,
      ),
    );

    result.fold(
      (failure) => emit(AuthErrorState(message: failure.message)),
      (user) => emit(AuthenticatedState(user: user)),
    );
  }

  /// Handle sign out event
  Future<void> _onSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoadingState());

    final result = await _signOut();

    result.fold(
      (failure) => emit(AuthErrorState(message: failure.message)),
      (_) => emit(const UnauthenticatedState()),
    );
  }

  /// Handle update profile event
  Future<void> _onUpdateProfile(
    UpdateProfileEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoadingState());

    final result = await _updateProfile(
      update_profile.Params(
        displayName: event.displayName,
        photoURL: event.photoURL,
        phoneNumber: event.phoneNumber,
      ),
    );

    result.fold(
      (failure) => emit(AuthErrorState(message: failure.message)),
      (user) => emit(ProfileUpdatedState(user: user)),
    );
  }
}
