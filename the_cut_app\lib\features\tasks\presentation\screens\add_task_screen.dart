import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/services/task_service.dart';
import '../../../../core/services/team_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/task_entity.dart';

/// Add task screen
class AddTaskScreen extends StatefulWidget {
  const AddTaskScreen({super.key});

  @override
  State<AddTaskScreen> createState() => _AddTaskScreenState();
}

class _AddTaskScreenState extends State<AddTaskScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  DateTime? _dueDate;
  TaskPriority _priority = TaskPriority.medium;
  String? _selectedBranchId;
  String? _selectedBranchName;
  String? _selectedAssigneeId;
  String? _selectedAssigneeName;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BranchService>(context, listen: false).loadBranches();
      Provider.of<TeamService>(context, listen: false).loadTeamMembers();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDueDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (pickedDate != null) {
      setState(() {
        _dueDate = pickedDate;
      });
    }
  }

  Future<void> _saveTask() async {
    if (_formKey.currentState!.validate()) {
      final taskService = Provider.of<TaskService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You must be logged in to create a task'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final task = TaskEntity(
        id: '', // Will be generated by the service
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        status: TaskStatus.pending,
        priority: _priority,
        dueDate: _dueDate,
        assignedToId: _selectedAssigneeId,
        assignedToName: _selectedAssigneeName,
        createdById: currentUser.id,
        createdByName: currentUser.name ?? 'Unknown User',
        branchId: _selectedBranchId,
        branchName: _selectedBranchName,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final newTask = await taskService.createTask(task);
      
      if (newTask != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task created successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(taskService.error ?? 'Failed to create task'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final taskService = Provider.of<TaskService>(context);
    final branchService = Provider.of<BranchService>(context);
    final teamService = Provider.of<TeamService>(context);
    final branches = branchService.branches;
    final teamMembers = teamService.teamMembers;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Task'),
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Form title
                Text(
                  'Task Information',
                  style: AppTextStyles.h4,
                ),
                SizedBox(height: 24.h),
                
                // Task title
                CustomTextField(
                  controller: _titleController,
                  hintText: 'Task Title',
                  labelText: 'Task Title',
                  prefixIcon: Icons.task_outlined,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter task title';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                
                // Task description
                CustomTextField(
                  controller: _descriptionController,
                  hintText: 'Description',
                  labelText: 'Description',
                  prefixIcon: Icons.description_outlined,
                  maxLines: 5,
                  textInputAction: TextInputAction.newline,
                ),
                SizedBox(height: 16.h),
                
                // Due date
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Due Date',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    InkWell(
                      onTap: _selectDueDate,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 12.h,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.border,
                          ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today_outlined,
                              size: 20.w,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              _dueDate == null
                                  ? 'Select Due Date'
                                  : '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year}',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: _dueDate == null
                                    ? AppColors.textSecondary
                                    : AppColors.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                
                // Priority
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Priority',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.border,
                        ),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<TaskPriority>(
                          isExpanded: true,
                          value: _priority,
                          items: TaskPriority.values.map((priority) {
                            final String text;
                            final Color color;
                            
                            switch (priority) {
                              case TaskPriority.low:
                                text = 'Low';
                                color = Colors.green;
                                break;
                              case TaskPriority.medium:
                                text = 'Medium';
                                color = Colors.orange;
                                break;
                              case TaskPriority.high:
                                text = 'High';
                                color = Colors.red;
                                break;
                            }
                            
                            return DropdownMenuItem<TaskPriority>(
                              value: priority,
                              child: Row(
                                children: [
                                  Container(
                                    width: 12.w,
                                    height: 12.w,
                                    decoration: BoxDecoration(
                                      color: color,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    text,
                                    style: AppTextStyles.bodyMedium,
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _priority = value;
                              });
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                
                // Branch
                if (branchService.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (branches.isEmpty)
                  Text(
                    'No branches available. Please create a branch first.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.error,
                    ),
                  )
                else
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Branch',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.border,
                          ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: const Text('Select Branch'),
                            value: _selectedBranchId,
                            items: branches.map((branch) {
                              return DropdownMenuItem<String>(
                                value: branch.id,
                                child: Text(branch.name),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedBranchId = value;
                                _selectedBranchName = branches
                                    .firstWhere((branch) => branch.id == value)
                                    .name;
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                SizedBox(height: 16.h),
                
                // Assignee
                if (teamService.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (teamMembers.isEmpty)
                  Text(
                    'No team members available. Please add team members first.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.error,
                    ),
                  )
                else
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Assign To',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.border,
                          ),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            hint: const Text('Select Assignee'),
                            value: _selectedAssigneeId,
                            items: teamMembers.map((member) {
                              return DropdownMenuItem<String>(
                                value: member.id,
                                child: Text(member.name),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedAssigneeId = value;
                                _selectedAssigneeName = teamMembers
                                    .firstWhere((member) => member.id == value)
                                    .name;
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                SizedBox(height: 32.h),
                
                // Save button
                CustomButton(
                  text: 'Save Task',
                  isLoading: taskService.isLoading,
                  onPressed: _saveTask,
                ),
                SizedBox(height: 16.h),
                
                // Cancel button
                CustomButton(
                  text: 'Cancel',
                  color: Colors.grey.shade200,
                  textColor: AppColors.textPrimary,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
