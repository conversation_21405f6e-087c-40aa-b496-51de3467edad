import '../../domain/entities/country.dart';

/// Country model
class CountryModel extends Country {
  /// Constructor
  const CountryModel({
    required super.code,
    required super.name,
    required super.nameAr,
    required super.flagEmoji,
    required super.currencyCode,
    required super.currencySymbol,
    required super.currencyName,
    required super.currencyNameAr,
    required super.exchangeRate,
    required super.phoneCode,
    super.isPaymentSupported = true,
    required super.supportedPaymentMethods,
  });

  /// Create a model from a JSON map
  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      code: json['code'],
      name: json['name'],
      nameAr: json['name_ar'],
      flagEmoji: json['flag_emoji'],
      currencyCode: json['currency_code'],
      currencySymbol: json['currency_symbol'],
      currencyName: json['currency_name'],
      currencyNameAr: json['currency_name_ar'],
      exchangeRate: json['exchange_rate'].toDouble(),
      phoneCode: json['phone_code'],
      isPaymentSupported: json['is_payment_supported'] ?? true,
      supportedPaymentMethods: List<String>.from(json['supported_payment_methods']),
    );
  }

  /// Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'name_ar': nameAr,
      'flag_emoji': flagEmoji,
      'currency_code': currencyCode,
      'currency_symbol': currencySymbol,
      'currency_name': currencyName,
      'currency_name_ar': currencyNameAr,
      'exchange_rate': exchangeRate,
      'phone_code': phoneCode,
      'is_payment_supported': isPaymentSupported,
      'supported_payment_methods': supportedPaymentMethods,
    };
  }
}
