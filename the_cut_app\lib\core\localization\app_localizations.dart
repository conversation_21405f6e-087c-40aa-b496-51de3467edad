import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// AppLocalizations class for handling translations
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// Static member to have a simple access to the delegate from the MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  late Map<String, String> _localizedStrings;

  /// Load the language JSON file from the "lang" folder
  Future<bool> load() async {
    // Load the language JSON file from the "lang" folder
    String jsonString =
        await rootBundle.loadString('assets/lang/${locale.languageCode}.json');
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    _localizedStrings = jsonMap.map((key, value) {
      return MapEntry(key, value.toString());
    });

    return true;
  }

  /// This method will be called from every widget which needs a localized text
  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  /// Format date according to locale
  String formatDate(DateTime date) {
    return DateFormat.yMd(locale.languageCode).format(date);
  }

  /// Format time according to locale
  String formatTime(DateTime time) {
    return DateFormat.Hm(locale.languageCode).format(time);
  }

  /// Format date and time according to locale
  String formatDateTime(DateTime dateTime) {
    return DateFormat.yMd(locale.languageCode).add_Hm().format(dateTime);
  }

  /// Format currency according to locale
  String formatCurrency(double amount) {
    return NumberFormat.currency(
      locale: locale.languageCode,
      symbol: locale.languageCode == 'ar' ? 'ر.س' : '\$',
    ).format(amount);
  }

  /// Check if locale is RTL
  bool get isRtl {
    return locale.languageCode == 'ar';
  }
}

/// LocalizationsDelegate is a factory for a set of localized resources
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Include all of your supported language codes here
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    // AppLocalizations class is where the JSON loading actually runs
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
