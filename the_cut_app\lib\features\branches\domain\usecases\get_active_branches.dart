import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';
import '../repositories/branch_repository.dart';

/// Get active branches use case
class GetActiveBranches {
  final BranchRepository _repository;

  GetActiveBranches(this._repository);

  /// Call the use case
  Future<Either<Failure, List<BranchEntity>>> call() {
    return _repository.getActiveBranches();
  }
}
