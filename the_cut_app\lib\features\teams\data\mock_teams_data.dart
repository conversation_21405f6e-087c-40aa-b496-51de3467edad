import '../domain/entities/team_member_entity.dart';

/// بيانات وهمية للفرق
class MockTeamsData {
  static final List<TeamMemberEntity> teamMembers = [
    // المهندسين
    TeamMemberEntity(
      id: 'eng_001',
      name: 'أحم<PERSON> محمد الكندري',
      role: 'مهندس',
      department: 'الهندسة المدنية',
      phone: '+965 9999 1111',
      email: '<EMAIL>',
      salary: 1200.0,
      joinDate: DateTime(2023, 1, 15),
      isActive: true,
      skills: ['تصميم إنشائي', 'إدارة مشاريع', 'AutoCAD'],
      notes: 'مهندس متميز في التصميم الإنشائي',
    ),
    TeamMemberEntity(
      id: 'eng_002',
      name: 'فاطمة علي العتيبي',
      role: 'مهندس',
      department: 'الهندسة المعمارية',
      phone: '+965 9999 2222',
      email: '<EMAIL>',
      salary: 1100.0,
      joinDate: DateTime(2023, 3, 10),
      isActive: true,
      skills: ['التصميم المعماري', 'Revit', '3D Max'],
      notes: 'متخصصة في التصميم المعماري الحديث',
    ),
    TeamMemberEntity(
      id: 'eng_003',
      name: 'خالد سعد الرشيد',
      role: 'مهندس',
      department: 'الهندسة الكهربائية',
      phone: '+965 9999 3333',
      email: '<EMAIL>',
      salary: 1150.0,
      joinDate: DateTime(2022, 11, 5),
      isActive: true,
      skills: ['الأنظمة الكهربائية', 'التحكم الآلي', 'PLC'],
      notes: 'خبير في الأنظمة الكهربائية للمباني',
    ),

    // العمال
    TeamMemberEntity(
      id: 'worker_001',
      name: 'محمد عبدالله الشمري',
      role: 'عامل',
      department: 'البناء والتشييد',
      phone: '+965 9999 4444',
      email: '<EMAIL>',
      salary: 400.0,
      joinDate: DateTime(2023, 2, 20),
      isActive: true,
      skills: ['البناء', 'الحديد', 'الخرسانة'],
      notes: 'عامل ماهر في أعمال البناء',
    ),
    TeamMemberEntity(
      id: 'worker_002',
      name: 'عبدالرحمن يوسف المطيري',
      role: 'عامل',
      department: 'التشطيبات',
      phone: '+965 9999 5555',
      email: '<EMAIL>',
      salary: 450.0,
      joinDate: DateTime(2023, 4, 12),
      isActive: true,
      skills: ['الدهان', 'البلاط', 'النجارة'],
      notes: 'متخصص في أعمال التشطيبات الداخلية',
    ),
    TeamMemberEntity(
      id: 'worker_003',
      name: 'سالم أحمد العجمي',
      role: 'عامل',
      department: 'السباكة والكهرباء',
      phone: '+965 9999 6666',
      email: '<EMAIL>',
      salary: 420.0,
      joinDate: DateTime(2022, 12, 8),
      isActive: true,
      skills: ['السباكة', 'الكهرباء', 'التكييف'],
      notes: 'فني ماهر في السباكة والكهرباء',
    ),
    TeamMemberEntity(
      id: 'worker_004',
      name: 'ناصر محمد الدوسري',
      role: 'عامل',
      department: 'البناء والتشييد',
      phone: '+965 9999 7777',
      email: '<EMAIL>',
      salary: 380.0,
      joinDate: DateTime(2023, 5, 18),
      isActive: false,
      skills: ['البناء', 'الحفر', 'النقل'],
      notes: 'في إجازة مرضية',
    ),

    // الإداريين
    TeamMemberEntity(
      id: 'admin_001',
      name: 'نورا سعد الصباح',
      role: 'إداري',
      department: 'الموارد البشرية',
      phone: '+965 9999 8888',
      email: '<EMAIL>',
      salary: 800.0,
      joinDate: DateTime(2022, 9, 1),
      isActive: true,
      skills: ['إدارة الموارد البشرية', 'التوظيف', 'التدريب'],
      notes: 'مسؤولة عن إدارة شؤون الموظفين',
    ),
    TeamMemberEntity(
      id: 'admin_002',
      name: 'عبدالعزيز فهد الخالد',
      role: 'إداري',
      department: 'المحاسبة',
      phone: '+965 9999 9999',
      email: '<EMAIL>',
      salary: 900.0,
      joinDate: DateTime(2022, 7, 15),
      isActive: true,
      skills: ['المحاسبة', 'التدقيق', 'التقارير المالية'],
      notes: 'محاسب رئيسي للشركة',
    ),
    TeamMemberEntity(
      id: 'admin_003',
      name: 'مريم خالد البدر',
      role: 'إداري',
      department: 'خدمة العملاء',
      phone: '+965 9999 0000',
      email: '<EMAIL>',
      salary: 650.0,
      joinDate: DateTime(2023, 1, 30),
      isActive: true,
      skills: ['خدمة العملاء', 'التواصل', 'حل المشاكل'],
      notes: 'مسؤولة عن التواصل مع العملاء',
    ),
    TeamMemberEntity(
      id: 'admin_004',
      name: 'يوسف عبدالله الهاجري',
      role: 'إداري',
      department: 'المشتريات',
      phone: '+965 9999 1010',
      email: '<EMAIL>',
      salary: 750.0,
      joinDate: DateTime(2022, 10, 22),
      isActive: true,
      skills: ['المشتريات', 'التفاوض', 'إدارة المخزون'],
      notes: 'مسؤول عن مشتريات المواد والمعدات',
    ),
  ];

  /// الحصول على الأعضاء حسب الدور
  static List<TeamMemberEntity> getMembersByRole(String role) {
    return teamMembers.where((member) => member.role == role).toList();
  }

  /// الحصول على الأعضاء النشطين
  static List<TeamMemberEntity> getActiveMembers() {
    return teamMembers.where((member) => member.isActive).toList();
  }

  /// الحصول على الأعضاء حسب القسم
  static List<TeamMemberEntity> getMembersByDepartment(String department) {
    return teamMembers.where((member) => member.department == department).toList();
  }

  /// البحث في الأعضاء
  static List<TeamMemberEntity> searchMembers(String query) {
    final lowerQuery = query.toLowerCase();
    return teamMembers.where((member) {
      return member.name.toLowerCase().contains(lowerQuery) ||
          member.role.toLowerCase().contains(lowerQuery) ||
          member.department.toLowerCase().contains(lowerQuery) ||
          member.email.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// إحصائيات الفريق
  static Map<String, int> getTeamStats() {
    final stats = <String, int>{};
    
    stats['total'] = teamMembers.length;
    stats['active'] = getActiveMembers().length;
    stats['engineers'] = getMembersByRole('مهندس').length;
    stats['workers'] = getMembersByRole('عامل').length;
    stats['admins'] = getMembersByRole('إداري').length;
    
    return stats;
  }

  /// متوسط الراتب حسب الدور
  static Map<String, double> getAverageSalaryByRole() {
    final salaries = <String, List<double>>{};
    
    for (final member in teamMembers) {
      if (!salaries.containsKey(member.role)) {
        salaries[member.role] = [];
      }
      salaries[member.role]!.add(member.salary);
    }
    
    final averages = <String, double>{};
    salaries.forEach((role, salaryList) {
      averages[role] = salaryList.reduce((a, b) => a + b) / salaryList.length;
    });
    
    return averages;
  }
}
