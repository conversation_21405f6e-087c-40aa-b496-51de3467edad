import 'package:equatable/equatable.dart';
import '../../domain/entities/branch_entity.dart';

/// Branch states
abstract class BranchState extends Equatable {
  const BranchState();

  @override
  List<Object?> get props => [];
}

/// Initial branch state
class BranchInitialState extends BranchState {
  const BranchInitialState();
}

/// Loading branch state
class BranchLoadingState extends BranchState {
  const BranchLoadingState();
}

/// Loaded all branches state
class AllBranchesLoadedState extends BranchState {
  final List<BranchEntity> branches;

  const AllBranchesLoadedState({
    required this.branches,
  });

  @override
  List<Object?> get props => [branches];
}

/// Loaded branch state
class BranchLoadedState extends BranchState {
  final BranchEntity branch;

  const BranchLoadedState({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}

/// Branch created state
class BranchCreatedState extends BranchState {
  final BranchEntity branch;

  const BranchCreatedState({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}

/// Branch updated state
class BranchUpdatedState extends BranchState {
  final BranchEntity branch;

  const BranchUpdatedState({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}

/// Branch deleted state
class BranchDeletedState extends BranchState {
  const BranchDeletedState();
}

/// Loaded active branches state
class ActiveBranchesLoadedState extends BranchState {
  final List<BranchEntity> branches;

  const ActiveBranchesLoadedState({
    required this.branches,
  });

  @override
  List<Object?> get props => [branches];
}

/// Branch error state
class BranchErrorState extends BranchState {
  final String message;

  const BranchErrorState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}
