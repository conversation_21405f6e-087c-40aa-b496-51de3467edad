import 'package:flutter/material.dart';
import '../../features/finance/domain/entities/transaction_entity.dart';
import '../data/mock_data.dart';

/// Finance service
class FinanceService extends ChangeNotifier {
  List<TransactionEntity> _transactions = [];
  TransactionEntity? _selectedTransaction;
  bool _isLoading = false;
  String? _error;

  /// Get all transactions
  List<TransactionEntity> get transactions => _transactions;

  /// Get selected transaction
  TransactionEntity? get selectedTransaction => _selectedTransaction;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Load all transactions
  Future<void> loadTransactions() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get transactions from mock data
      _transactions = List.from(MockData.transactions);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get transactions by branch ID
  Future<List<TransactionEntity>> getTransactionsByBranchId(String branchId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get transactions from mock data
      final branchTransactions = MockData.getTransactionsByBranchId(branchId);
      _isLoading = false;
      notifyListeners();
      return branchTransactions;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Get transactions by type
  Future<List<TransactionEntity>> getTransactionsByType(TransactionType type) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get transactions from mock data
      final typeTransactions = MockData.getTransactionsByType(type);
      _isLoading = false;
      notifyListeners();
      return typeTransactions;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  /// Get transaction by ID
  Future<TransactionEntity?> getTransactionById(String transactionId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get transaction from mock data
      _selectedTransaction = MockData.getTransactionById(transactionId);
      _isLoading = false;
      notifyListeners();
      return _selectedTransaction;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Create transaction
  Future<TransactionEntity?> createTransaction(TransactionEntity transaction) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Add transaction to mock data
      final newTransaction = MockData.addTransaction(transaction);
      _transactions.add(newTransaction);
      _selectedTransaction = newTransaction;
      _isLoading = false;
      notifyListeners();
      return newTransaction;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update transaction
  Future<TransactionEntity?> updateTransaction(TransactionEntity transaction) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Update transaction in mock data
      final updatedTransaction = MockData.updateTransaction(transaction);
      
      // Update transaction in list
      final index = _transactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _transactions[index] = updatedTransaction;
      }
      
      _selectedTransaction = updatedTransaction;
      _isLoading = false;
      notifyListeners();
      return updatedTransaction;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Delete transaction
  Future<bool> deleteTransaction(String transactionId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Delete transaction from mock data
      MockData.deleteTransaction(transactionId);
      
      // Remove transaction from list
      _transactions.removeWhere((transaction) => transaction.id == transactionId);
      
      if (_selectedTransaction?.id == transactionId) {
        _selectedTransaction = null;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Calculate total income
  double calculateTotalIncome() {
    return _transactions
        .where((transaction) => transaction.type == TransactionType.income)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  /// Calculate total expenses
  double calculateTotalExpenses() {
    return _transactions
        .where((transaction) => transaction.type == TransactionType.expense)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  /// Calculate balance
  double calculateBalance() {
    return calculateTotalIncome() - calculateTotalExpenses();
  }

  /// Set selected transaction
  void setSelectedTransaction(TransactionEntity transaction) {
    _selectedTransaction = transaction;
    notifyListeners();
  }

  /// Clear selected transaction
  void clearSelectedTransaction() {
    _selectedTransaction = null;
    notifyListeners();
  }
}
