import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Sign in with email and password use case
class SignInWithEmailAndPassword {
  final AuthRepository _repository;

  SignInWithEmailAndPassword(this._repository);

  /// Call the use case
  Future<Either<AuthFailure, UserEntity>> call(Params params) {
    return _repository.signInWithEmailAndPassword(
      params.email,
      params.password,
    );
  }
}

/// Sign in with email and password parameters
class Params extends Equatable {
  final String email;
  final String password;

  const Params({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}
