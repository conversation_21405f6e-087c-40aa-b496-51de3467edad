import 'package:equatable/equatable.dart';

/// Base class for failures
abstract class Failure extends Equatable {
  @override
  List<Object?> get props => [];
}

/// Server failure
class ServerFailure extends Failure {}

/// Cache failure
class CacheFailure extends Failure {}

/// Network failure
class NetworkFailure extends Failure {}

/// Not found failure
class NotFoundFailure extends Failure {}

/// Authentication failure
class AuthFailure extends Failure {
  /// Error message
  final String message;

  /// Constructor
  AuthFailure(this.message);

  @override
  List<Object?> get props => [message];
}

/// Validation failure
class ValidationFailure extends Failure {
  /// Error message
  final String message;

  /// Constructor
  ValidationFailure(this.message);

  @override
  List<Object?> get props => [message];
}

/// Permission failure
class PermissionFailure extends Failure {
  /// Error message
  final String message;

  /// Constructor
  PermissionFailure(this.message);

  @override
  List<Object?> get props => [message];
}

/// Payment failure
class PaymentFailure extends Failure {
  /// Error message
  final String message;

  /// Constructor
  PaymentFailure(this.message);

  @override
  List<Object?> get props => [message];
}
