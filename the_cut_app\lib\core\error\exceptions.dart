/// Exception thrown when a server error occurs
class ServerException implements Exception {}

/// Exception thrown when a cache error occurs
class CacheException implements Exception {}

/// Exception thrown when a network error occurs
class NetworkException implements Exception {}

/// Exception thrown when a resource is not found
class NotFoundException implements Exception {}

/// Exception thrown when an authentication error occurs
class AuthException implements Exception {
  /// Error message
  final String message;

  /// Constructor
  AuthException(this.message);
}

/// Exception thrown when a validation error occurs
class ValidationException implements Exception {
  /// Error message
  final String message;

  /// Constructor
  ValidationException(this.message);
}

/// Exception thrown when a permission error occurs
class PermissionException implements Exception {
  /// Error message
  final String message;

  /// Constructor
  PermissionException(this.message);
}

/// Exception thrown when a payment error occurs
class PaymentException implements Exception {
  /// Error message
  final String message;

  /// Constructor
  PaymentException(this.message);
}
