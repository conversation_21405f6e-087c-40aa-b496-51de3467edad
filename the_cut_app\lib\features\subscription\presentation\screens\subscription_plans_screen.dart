import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/font_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/country.dart';
import '../../domain/entities/subscription_plan.dart';
import '../bloc/subscription_bloc.dart';
import '../widgets/country_selector.dart';
import '../widgets/subscription_plan_card.dart';

/// Screen to display subscription plans
class SubscriptionPlansScreen extends StatefulWidget {
  /// Constructor
  const SubscriptionPlansScreen({super.key});

  @override
  State<SubscriptionPlansScreen> createState() =>
      _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends State<SubscriptionPlansScreen> {
  Country? _selectedCountry;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    final bloc = context.read<SubscriptionBloc>();
    bloc.add(LoadSupportedCountries());
    bloc.add(LoadSubscriptionPlans());
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final fontService = FontService.of(context);
    final authService = Provider.of<AuthService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('subscription_plans')),
      ),
      body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state is CountrySelected) {
            setState(() {
              _selectedCountry = state.country;
            });
          } else if (state is SubscriptionError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is SubscribedToPlan) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text(localizations.translate('subscription_successful')),
                backgroundColor: AppColors.success,
              ),
            );
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          if (state is SupportedCountriesLoading) {
            return const LoadingIndicator();
          } else if (state is SubscriptionPlansLoading) {
            return const LoadingIndicator();
          } else if (state is SubscriptionPlansLoaded) {
            return _buildPlansContent(
              context,
              state.plans,
              localizations,
              fontService,
              authService,
            );
          } else if (state is SubscriptionPlansError) {
            return _buildErrorContent(context, state.message, localizations);
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _buildPlansContent(
    BuildContext context,
    List<SubscriptionPlan> plans,
    AppLocalizations localizations,
    FontService fontService,
    AuthService authService,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Country selector
        Padding(
          padding: EdgeInsets.all(16.w),
          child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
            builder: (context, state) {
              if (state is SupportedCountriesLoaded) {
                return CountrySelector(
                  countries: state.countries,
                  selectedCountry: _selectedCountry ?? state.countries.first,
                  onCountrySelected: (country) {
                    context
                        .read<SubscriptionBloc>()
                        .add(SelectCountry(country));
                  },
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),
        ),

        // Title
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            localizations.translate('choose_subscription_plan'),
            style: GoogleFonts.getFont(
              fontService.fontFamily,
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        SizedBox(height: 16.h),

        // Plans list
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: plans.length,
            itemBuilder: (context, index) {
              final plan = plans[index];
              return SubscriptionPlanCard(
                plan: plan,
                onSubscribe: () => _subscribeToPlan(context, plan, authService),
                currencySymbol: _selectedCountry?.currencySymbol ?? '\$',
                exchangeRate: _selectedCountry?.exchangeRate ?? 1.0,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent(
    BuildContext context,
    String message,
    AppLocalizations localizations,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(message),
          SizedBox(height: 16.h),
          CustomButton(
            text: localizations.translate('retry'),
            onPressed: _loadData,
          ),
        ],
      ),
    );
  }

  void _subscribeToPlan(
    BuildContext context,
    SubscriptionPlan plan,
    AuthService authService,
  ) {
    if (_selectedCountry == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              AppLocalizations.of(context).translate('select_country_first')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (authService.currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text(AppLocalizations.of(context).translate('login_required')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Navigate to payment screen
    Navigator.pushNamed(
      context,
      '/payment',
      arguments: {
        'plan': plan,
        'country': _selectedCountry,
      },
    );
  }
}
