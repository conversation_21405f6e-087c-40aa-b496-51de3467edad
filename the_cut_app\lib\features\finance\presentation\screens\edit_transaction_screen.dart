import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/branch_service.dart';
import '../../../../core/services/finance_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/transaction_entity.dart';

/// Edit transaction screen
class EditTransactionScreen extends StatefulWidget {
  /// Transaction ID
  final String transactionId;

  /// Constructor
  const EditTransactionScreen({
    super.key,
    required this.transactionId,
  });

  @override
  State<EditTransactionScreen> createState() => _EditTransactionScreenState();
}

class _EditTransactionScreenState extends State<EditTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _amountController;
  TransactionType _type = TransactionType.income;
  TransactionCategory _category = TransactionCategory.services;
  DateTime _date = DateTime.now();
  String? _selectedBranchId;
  String? _selectedBranchName;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _amountController = TextEditingController();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransaction();
      Provider.of<BranchService>(context, listen: false).loadBranches();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _loadTransaction() async {
    final financeService = Provider.of<FinanceService>(context, listen: false);
    final transaction = await financeService.getTransactionById(widget.transactionId);
    
    if (transaction != null && mounted) {
      setState(() {
        _titleController.text = transaction.title;
        _descriptionController.text = transaction.description ?? '';
        _amountController.text = transaction.amount.toString();
        _type = transaction.type;
        _category = transaction.category;
        _date = transaction.date;
        _selectedBranchId = transaction.branchId;
        _selectedBranchName = transaction.branchName;
        _isLoading = false;
      });
    } else if (mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Transaction not found'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _date,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    
    if (pickedDate != null) {
      setState(() {
        _date = pickedDate;
      });
    }
  }

  Future<void> _updateTransaction() async {
    if (_formKey.currentState!.validate()) {
      final financeService = Provider.of<FinanceService>(context, listen: false);
      final currentTransaction = financeService.selectedTransaction;
      
      if (currentTransaction == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction not found'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final amount = double.tryParse(_amountController.text.trim()) ?? 0.0;
      
      final updatedTransaction = currentTransaction.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        amount: amount,
        type: _type,
        category: _category,
        date: _date,
        branchId: _selectedBranchId,
        branchName: _selectedBranchName,
        updatedAt: DateTime.now(),
      );
      
      final result = await financeService.updateTransaction(updatedTransaction);
      
      if (result != null && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction updated successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(financeService.error ?? 'Failed to update transaction'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final financeService = Provider.of<FinanceService>(context);
    final branchService = Provider.of<BranchService>(context);
    final branches = branchService.branches;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Transaction'),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SafeArea(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Form title
                      Text(
                        'Transaction Information',
                        style: AppTextStyles.h4,
                      ),
                      SizedBox(height: 24.h),
                      
                      // Transaction type
                      Text(
                        'Transaction Type',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Expanded(
                            child: _buildTypeButton(
                              type: TransactionType.income,
                              title: 'Income',
                              icon: Icons.arrow_upward,
                              color: AppColors.success,
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: _buildTypeButton(
                              type: TransactionType.expense,
                              title: 'Expense',
                              icon: Icons.arrow_downward,
                              color: AppColors.error,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Transaction title
                      CustomTextField(
                        controller: _titleController,
                        hintText: 'Transaction Title',
                        labelText: 'Title',
                        prefixIcon: Icons.title_outlined,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter transaction title';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Transaction amount
                      CustomTextField(
                        controller: _amountController,
                        hintText: 'Amount',
                        labelText: 'Amount',
                        prefixIcon: Icons.attach_money_outlined,
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter transaction amount';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Please enter a valid number';
                          }
                          if (double.parse(value) <= 0) {
                            return 'Amount must be greater than zero';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),
                      
                      // Transaction description
                      CustomTextField(
                        controller: _descriptionController,
                        hintText: 'Description',
                        labelText: 'Description',
                        prefixIcon: Icons.description_outlined,
                        maxLines: 3,
                        textInputAction: TextInputAction.newline,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Transaction category
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Category',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColors.border,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<TransactionCategory>(
                                isExpanded: true,
                                value: _category,
                                items: TransactionCategory.values.map((category) {
                                  final String text;
                                  
                                  switch (category) {
                                    case TransactionCategory.sales:
                                      text = 'Sales';
                                      break;
                                    case TransactionCategory.services:
                                      text = 'Services';
                                      break;
                                    case TransactionCategory.salary:
                                      text = 'Salary';
                                      break;
                                    case TransactionCategory.rent:
                                      text = 'Rent';
                                      break;
                                    case TransactionCategory.utilities:
                                      text = 'Utilities';
                                      break;
                                    case TransactionCategory.supplies:
                                      text = 'Supplies';
                                      break;
                                    case TransactionCategory.equipment:
                                      text = 'Equipment';
                                      break;
                                    case TransactionCategory.marketing:
                                      text = 'Marketing';
                                      break;
                                    case TransactionCategory.taxes:
                                      text = 'Taxes';
                                      break;
                                    case TransactionCategory.insurance:
                                      text = 'Insurance';
                                      break;
                                    case TransactionCategory.other:
                                      text = 'Other';
                                      break;
                                  }
                                  
                                  return DropdownMenuItem<TransactionCategory>(
                                    value: category,
                                    child: Text(
                                      text,
                                      style: AppTextStyles.bodyMedium,
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _category = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Transaction date
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Date',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          InkWell(
                            onTap: _selectDate,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 12.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 20.w,
                                    color: AppColors.textSecondary,
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    '${_date.day}/${_date.month}/${_date.year}',
                                    style: AppTextStyles.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      
                      // Branch
                      if (branchService.isLoading)
                        const Center(
                          child: CircularProgressIndicator(),
                        )
                      else if (branches.isEmpty)
                        Text(
                          'No branches available. Please create a branch first.',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.error,
                          ),
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Branch',
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.border,
                                ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  isExpanded: true,
                                  hint: const Text('Select Branch'),
                                  value: _selectedBranchId,
                                  items: branches.map((branch) {
                                    return DropdownMenuItem<String>(
                                      value: branch.id,
                                      child: Text(branch.name),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedBranchId = value;
                                      _selectedBranchName = branches
                                          .firstWhere((branch) => branch.id == value)
                                          .name;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      SizedBox(height: 32.h),
                      
                      // Update button
                      CustomButton(
                        text: 'Update Transaction',
                        isLoading: financeService.isLoading,
                        onPressed: _updateTransaction,
                      ),
                      SizedBox(height: 16.h),
                      
                      // Cancel button
                      CustomButton(
                        text: 'Cancel',
                        color: Colors.grey.shade200,
                        textColor: AppColors.textPrimary,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildTypeButton({
    required TransactionType type,
    required String title,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _type == type;
    
    return InkWell(
      onTap: () {
        setState(() {
          _type = type;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 20.w,
              color: isSelected ? color : AppColors.textSecondary,
            ),
            SizedBox(width: 8.w),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected ? color : AppColors.textPrimary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
