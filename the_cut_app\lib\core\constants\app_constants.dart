/// App constants
class AppConstants {
  // App info
  static const String appName = 'The Cut';
  static const String appVersion = '1.0.0';
  
  // API endpoints
  static const String baseUrl = 'https://api.thecut.com';
  
  // Shared preferences keys
  static const String tokenKey = 'token';
  static const String userKey = 'user';
  static const String themeKey = 'theme';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';
  
  // Pagination
  static const int defaultPageSize = 10;
  
  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Firebase collections
  static const String usersCollection = 'users';
  static const String branchesCollection = 'branches';
  static const String teamsCollection = 'teams';
  static const String tasksCollection = 'tasks';
  static const String financesCollection = 'finances';
  
  // Image paths
  static const String logoPath = 'assets/images/logo.png';
  static const String placeholderImagePath = 'assets/images/placeholder.png';
  static const String userPlaceholderPath = 'assets/images/user_placeholder.png';
  
  // Animation durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);
}
