import '../../features/supervision/domain/entities/supervisor_entity.dart';
import '../../features/real_estate_projects/domain/entities/project_entity.dart';
import 'mock_real_estate_data.dart';

/// بيانات وهمية للإشراف
class MockSupervisionData {
  /// المشرفون الوهميون
  static final List<SupervisorEntity> supervisors = [
    SupervisorEntity(
      id: 'supervisor1',
      fullName: 'خالد أحمد السعد',
      email: '<EMAIL>',
      phoneNumber: '+966501234567',
      type: SupervisorType.civil,
      specialization: 'هندسة مدنية - إنشائية',
      experienceYears: 12,
      licenseNumber: 'CE-2024-001',
      company: 'مكتب السعد الهندسي',
      position: 'مهندس مدني أول',
      bio: 'مهندس مدني متخصص في الأعمال الإنشائية مع خبرة 12 عام في إدارة المشاريع السكنية والتجارية',
      skills: ['إدارة المشاريع', 'التصميم الإنشائي', 'الإشراف على التنفيذ', 'مراقبة الجودة'],
      certifications: ['PMP', 'مهندس معتمد من الهيئة السعودية للمهندسين'],
      status: SupervisorStatus.active,
      rating: 4.8,
      completedProjects: 25,
      currentProjects: 3,
      registrationDate: DateTime.now().subtract(const Duration(days: 800)),
      lastLoginAt: DateTime.now().subtract(const Duration(hours: 2)),
      isVerified: true,
      isEmailVerified: true,
      isPhoneVerified: true,
    ),
    SupervisorEntity(
      id: 'supervisor2',
      fullName: 'سارة محمد أحمد',
      email: '<EMAIL>',
      phoneNumber: '+966507654321',
      type: SupervisorType.architect,
      specialization: 'هندسة معمارية',
      experienceYears: 8,
      licenseNumber: 'AR-2024-002',
      company: 'استوديو التصميم المعماري',
      position: 'مهندسة معمارية',
      bio: 'مهندسة معمارية متخصصة في التصميم والإشراف على المشاريع التجارية والسكنية الحديثة',
      skills: ['التصميم المعماري', 'التخطيط العمراني', 'الإشراف المعماري', 'استخدام البرامج الهندسية'],
      certifications: ['LEED AP', 'مهندسة معتمدة من الهيئة السعودية للمهندسين'],
      status: SupervisorStatus.active,
      rating: 4.6,
      completedProjects: 18,
      currentProjects: 2,
      registrationDate: DateTime.now().subtract(const Duration(days: 600)),
      lastLoginAt: DateTime.now().subtract(const Duration(hours: 5)),
      isVerified: true,
      isEmailVerified: true,
      isPhoneVerified: true,
    ),
    SupervisorEntity(
      id: 'supervisor3',
      fullName: 'عبدالرحمن علي الشمري',
      email: '<EMAIL>',
      phoneNumber: '+966509876543',
      type: SupervisorType.electrical,
      specialization: 'هندسة كهربائية',
      experienceYears: 15,
      licenseNumber: 'EE-2024-003',
      company: 'مؤسسة الشمري للاستشارات الهندسية',
      position: 'مهندس كهربائي استشاري',
      bio: 'مهندس كهربائي خبير في أنظمة الكهرباء والتحكم الآلي للمباني السكنية والتجارية',
      skills: ['أنظمة الكهرباء', 'التحكم الآلي', 'أنظمة الأمان', 'الطاقة المتجددة'],
      certifications: ['IEEE Member', 'مهندس معتمد من الهيئة السعودية للمهندسين'],
      status: SupervisorStatus.active,
      rating: 4.9,
      completedProjects: 35,
      currentProjects: 4,
      registrationDate: DateTime.now().subtract(const Duration(days: 1200)),
      lastLoginAt: DateTime.now().subtract(const Duration(days: 1)),
      isVerified: true,
      isEmailVerified: true,
      isPhoneVerified: true,
    ),
    SupervisorEntity(
      id: 'supervisor4',
      fullName: 'فاطمة عبدالله النصر',
      email: '<EMAIL>',
      phoneNumber: '+966502468135',
      type: SupervisorType.projectManager,
      specialization: 'إدارة المشاريع',
      experienceYears: 10,
      licenseNumber: 'PM-2024-004',
      company: 'شركة النصر لإدارة المشاريع',
      position: 'مديرة مشاريع أولى',
      bio: 'مديرة مشاريع متخصصة في إدارة المشاريع العقارية الكبيرة وضمان التسليم في الوقت المحدد',
      skills: ['إدارة المشاريع', 'التخطيط الزمني', 'إدارة المخاطر', 'إدارة الفرق'],
      certifications: ['PMP', 'Prince2', 'مديرة مشاريع معتمدة'],
      status: SupervisorStatus.pending,
      rating: 4.7,
      completedProjects: 22,
      currentProjects: 1,
      registrationDate: DateTime.now().subtract(const Duration(days: 30)),
      lastLoginAt: DateTime.now().subtract(const Duration(hours: 12)),
      isVerified: false,
      isEmailVerified: true,
      isPhoneVerified: false,
    ),
  ];

  /// الحصول على مشرف بالبريد الإلكتروني وكلمة المرور
  static SupervisorEntity? getSupervisorByEmailAndPassword(String email, String password) {
    // في التطبيق الحقيقي، سيتم التحقق من كلمة المرور المشفرة
    // هنا نتحقق فقط من البريد الإلكتروني للبيانات الوهمية
    try {
      return supervisors.firstWhere(
        (supervisor) => supervisor.email == email,
      );
    } catch (e) {
      throw Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
  }

  /// الحصول على مشرف بالبريد الإلكتروني
  static SupervisorEntity? getSupervisorByEmail(String email) {
    try {
      return supervisors.firstWhere(
        (supervisor) => supervisor.email == email,
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على مشرف بالمعرف
  static SupervisorEntity? getSupervisorById(String id) {
    try {
      return supervisors.firstWhere((supervisor) => supervisor.id == id);
    } catch (e) {
      return null;
    }
  }

  /// إضافة مشرف جديد
  static SupervisorEntity addSupervisor(SupervisorEntity supervisor) {
    final newSupervisor = supervisor.copyWith(
      id: 'supervisor${supervisors.length + 1}',
      registrationDate: DateTime.now(),
    );
    supervisors.add(newSupervisor);
    return newSupervisor;
  }

  /// تحديث مشرف
  static SupervisorEntity updateSupervisor(SupervisorEntity supervisor) {
    final index = supervisors.indexWhere((s) => s.id == supervisor.id);
    if (index != -1) {
      final updatedSupervisor = supervisor.copyWith(
        updatedAt: DateTime.now(),
      );
      supervisors[index] = updatedSupervisor;
      return updatedSupervisor;
    }
    throw Exception('المشرف غير موجود');
  }

  /// تحديث حالة المشرف
  static void updateSupervisorStatus(String supervisorId, SupervisorStatus status) {
    final index = supervisors.indexWhere((s) => s.id == supervisorId);
    if (index != -1) {
      supervisors[index] = supervisors[index].copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
    }
  }

  /// تحديث تقييم المشرف
  static void updateSupervisorRating(String supervisorId, double rating) {
    final index = supervisors.indexWhere((s) => s.id == supervisorId);
    if (index != -1) {
      supervisors[index] = supervisors[index].copyWith(
        rating: rating,
        updatedAt: DateTime.now(),
      );
    }
  }

  /// الحصول على مشاريع المشرف
  static List<ProjectEntity> getProjectsBySupervisorId(String supervisorId) {
    return MockRealEstateData.projects
        .where((project) => project.supervisorId == supervisorId)
        .toList();
  }

  /// الحصول على المشرفين حسب النوع
  static List<SupervisorEntity> getSupervisorsByType(SupervisorType type) {
    return supervisors.where((supervisor) => supervisor.type == type).toList();
  }

  /// الحصول على المشرفين النشطين
  static List<SupervisorEntity> getActiveSupervisors() {
    return supervisors
        .where((supervisor) => supervisor.status == SupervisorStatus.active)
        .toList();
  }

  /// الحصول على المشرفين المتاحين
  static List<SupervisorEntity> getAvailableSupervisors() {
    return supervisors.where((supervisor) => supervisor.isAvailable).toList();
  }

  /// البحث في المشرفين
  static List<SupervisorEntity> searchSupervisors(String query) {
    if (query.isEmpty) return supervisors;
    
    return supervisors.where((supervisor) {
      return supervisor.fullName.toLowerCase().contains(query.toLowerCase()) ||
             supervisor.email.toLowerCase().contains(query.toLowerCase()) ||
             supervisor.specialization?.toLowerCase().contains(query.toLowerCase()) == true ||
             supervisor.company?.toLowerCase().contains(query.toLowerCase()) == true;
    }).toList();
  }

  /// حذف مشرف
  static void deleteSupervisor(String supervisorId) {
    supervisors.removeWhere((supervisor) => supervisor.id == supervisorId);
  }

  /// إحصائيات المشرفين
  static Map<String, int> getSupervisorStatistics() {
    return {
      'total': supervisors.length,
      'active': supervisors.where((s) => s.status == SupervisorStatus.active).length,
      'pending': supervisors.where((s) => s.status == SupervisorStatus.pending).length,
      'inactive': supervisors.where((s) => s.status == SupervisorStatus.inactive).length,
      'suspended': supervisors.where((s) => s.status == SupervisorStatus.suspended).length,
    };
  }

  /// الحصول على أفضل المشرفين (حسب التقييم)
  static List<SupervisorEntity> getTopRatedSupervisors({int limit = 5}) {
    final sortedSupervisors = List<SupervisorEntity>.from(supervisors);
    sortedSupervisors.sort((a, b) {
      final ratingA = a.rating ?? 0.0;
      final ratingB = b.rating ?? 0.0;
      return ratingB.compareTo(ratingA);
    });
    return sortedSupervisors.take(limit).toList();
  }

  /// الحصول على المشرفين الأكثر خبرة
  static List<SupervisorEntity> getMostExperiencedSupervisors({int limit = 5}) {
    final sortedSupervisors = List<SupervisorEntity>.from(supervisors);
    sortedSupervisors.sort((a, b) {
      final experienceA = a.experienceYears ?? 0;
      final experienceB = b.experienceYears ?? 0;
      return experienceB.compareTo(experienceA);
    });
    return sortedSupervisors.take(limit).toList();
  }
}
