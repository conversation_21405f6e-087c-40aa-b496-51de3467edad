import 'package:equatable/equatable.dart';

/// Subscriber entity
class Subscriber extends Equatable {
  /// Unique identifier for the subscriber
  final String id;

  /// User ID associated with this subscriber
  final String userId;

  /// Subscription plan ID
  final String subscriptionPlanId;

  /// Start date of the subscription
  final DateTime startDate;

  /// End date of the subscription
  final DateTime endDate;

  /// Whether the subscription is active
  final bool isActive;

  /// Whether the subscription is in trial period
  final bool isInTrial;

  /// Whether the subscription will auto-renew
  final bool autoRenew;

  /// Payment method used (e.g., credit_card, paypal, apple_pay, google_pay)
  final String paymentMethod;

  /// Last 4 digits of the payment card (if applicable)
  final String? cardLast4;

  /// Payment card brand (if applicable)
  final String? cardBrand;

  /// Payment card expiry month (if applicable)
  final int? cardExpiryMonth;

  /// Payment card expiry year (if applicable)
  final int? cardExpiryYear;

  /// Country code of the subscriber
  final String countryCode;

  /// Currency code used for billing
  final String currencyCode;

  /// Next billing date
  final DateTime nextBillingDate;

  /// Billing cycle (e.g., monthly, yearly)
  final String billingCycle;

  /// Billing amount in the currency specified
  final double billingAmount;

  /// Billing status (e.g., active, past_due, unpaid, cancelled)
  final String billingStatus;

  /// Cancellation date (if cancelled)
  final DateTime? cancellationDate;

  /// Cancellation reason (if cancelled)
  final String? cancellationReason;

  /// Date when the subscription was created
  final DateTime createdAt;

  /// Date when the subscription was last updated
  final DateTime updatedAt;

  /// Receipt ID from the app store or play store
  final String? storeReceiptId;

  /// Original transaction ID from the app store or play store
  final String? originalTransactionId;

  /// Constructor
  const Subscriber({
    required this.id,
    required this.userId,
    required this.subscriptionPlanId,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    this.isInTrial = false,
    this.autoRenew = true,
    required this.paymentMethod,
    this.cardLast4,
    this.cardBrand,
    this.cardExpiryMonth,
    this.cardExpiryYear,
    required this.countryCode,
    required this.currencyCode,
    required this.nextBillingDate,
    required this.billingCycle,
    required this.billingAmount,
    required this.billingStatus,
    this.cancellationDate,
    this.cancellationReason,
    required this.createdAt,
    required this.updatedAt,
    this.storeReceiptId,
    this.originalTransactionId,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        subscriptionPlanId,
        startDate,
        endDate,
        isActive,
        isInTrial,
        autoRenew,
        paymentMethod,
        cardLast4,
        cardBrand,
        cardExpiryMonth,
        cardExpiryYear,
        countryCode,
        currencyCode,
        nextBillingDate,
        billingCycle,
        billingAmount,
        billingStatus,
        cancellationDate,
        cancellationReason,
        createdAt,
        updatedAt,
        storeReceiptId,
        originalTransactionId,
      ];
}
