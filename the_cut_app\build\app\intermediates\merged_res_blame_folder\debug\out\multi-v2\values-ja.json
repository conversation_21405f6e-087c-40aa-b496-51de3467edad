{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "7479,35245", "endColumns": "60,68", "endOffsets": "7535,35309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5345", "endColumns": "121", "endOffsets": "5462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2829,2890,3348,3779,3849,3940,4029,4084,4168,4246,4307,4382,4462,4517,4591,4639,4710,4796,4862,4928,4973,5041,5125,5168,5211,5280,5338,5397,5508,5619,5711,5774,5844,5908,5986,6054,6121,6184,6259,6323,6399,6478,6527,6621,6666,6721,6783,6842,6910,6969,7032,7110,7170", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,44,67,83,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,75,78,48,93,44,54,61,58,67,58,62,77,59,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2824,2885,3343,3774,3844,3935,4024,4079,4163,4241,4302,4377,4457,4512,4586,4634,4705,4791,4857,4923,4968,5036,5120,5163,5206,5275,5333,5392,5503,5614,5706,5769,5839,5903,5981,6049,6116,6179,6254,6318,6394,6473,6522,6616,6661,6716,6778,6837,6905,6964,7027,7105,7165,7227"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13160,13223,13294,13361,13423,13493,13547,13803,13856,13927,13993,14196,14355,14441,14580,14920,15185,16165,16379,16460,16523,16664,16742,16817,16961,17235,17313,17383,17468,17537,17616,17689,17759,17834,17915,18052,18262,18636,18698,18759,19838,20269,20339,20430,20519,20574,20658,20736,20797,20872,20952,21007,21501,21549,21620,21706,21772,22078,22397,22465,22549,22592,22865,22934,22992,23051,23162,23273,23540,23603,24371,24435,24581,24649,24935,24998,25136,25931,26007,30228,30277,30371,30416,30758,32107,32166,32234,32293,32356,32434,33548", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,44,67,83,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,75,78,48,93,44,54,61,58,67,58,62,77,59,61", "endOffsets": "13218,13289,13356,13418,13488,13542,13602,13851,13922,13988,14058,14277,14436,14511,14659,14989,15253,16242,16455,16518,16596,16737,16812,16896,17031,17308,17378,17463,17532,17611,17684,17754,17829,17910,17974,18119,18352,18693,18754,19212,20264,20334,20425,20514,20569,20653,20731,20792,20867,20947,21002,21076,21544,21615,21701,21767,21833,22118,22460,22544,22587,22630,22929,22987,23046,23157,23268,23360,23598,23668,24430,24508,24644,24711,24993,25068,25195,26002,26081,30272,30366,30411,30466,30815,32161,32229,32288,32351,32429,32489,33605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,12415", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,12489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,325,453,621,701", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "167,250,320,448,616,696,772"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6384,6947,11866,12137,34663,35089,35169", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "6446,7025,11931,12260,34826,35164,35240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4350,6730,7030", "endColumns": "74,70,73", "endOffsets": "4420,6796,7099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14123,14864,14994,15053,15127,15381,15910,16098,16313,16601,16901,17172", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "14191,14915,15048,15122,15180,15438,15962,16160,16374,16659,16956,17230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4186,4272,6544,6633,6801,7604,7682,11706,11791,11936,12000,12265,12339,12494,34831,34907,34972", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4267,4345,6628,6725,6879,7677,7755,11786,11861,11995,12059,12334,12410,12558,34902,34967,35084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,199,334,377,437,517,581,742,799,879,936,990,1054,1247,1308,1369,1422,1473,1544,1646,1733,1787,1864,1932,2006,2069,2145,2337,2407,2481,2536,2594,2655,2713,2803,2882,2984,3073,3152,3230,3314,3439,3573,3672,3726,4073,4130", "endColumns": "143,134,42,59,79,63,160,56,79,56,53,63,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,89,78,101,88,78,77,83,124,133,98,53,346,56,54", "endOffsets": "194,329,372,432,512,576,737,794,874,931,985,1049,1242,1303,1364,1417,1468,1539,1641,1728,1782,1859,1927,2001,2064,2140,2332,2402,2476,2531,2589,2650,2708,2798,2877,2979,3068,3147,3225,3309,3434,3568,3667,3721,4068,4125,4180"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18357,18501,19217,19260,19320,19400,19464,19625,19682,21081,21290,21437,21838,22275,22336,22750,23489,23747,23818,23920,24007,24061,24513,24716,25073,25200,25276,25468,25538,25668,25812,25870,26086,26622,27486,28725,28827,28916,28995,29073,30032,30471,30605,30704,30820,31167,32494", "endColumns": "143,134,42,59,79,63,160,56,79,56,53,63,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,89,78,101,88,78,77,83,124,133,98,53,346,56,54", "endOffsets": "18496,18631,19255,19315,19395,19459,19620,19677,19757,21133,21339,21496,22026,22331,22392,22798,23535,23813,23915,24002,24056,24133,24576,24785,25131,25271,25463,25533,25607,25718,25865,25926,26139,26707,27560,28822,28911,28990,29068,29152,30152,30600,30699,30753,31162,31219,32544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3252,3344,3444,3538,3634,3727,3820,12563", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3339,3439,3533,3629,3722,3815,3916,12659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,203,296,370,448,537,677,822,930,1015,1097,1184,1271,1384,1471,1558,1695,1789,1868,1926,1987,2065,2145,2231,2370,2437,2510,2587,2651,2723,2794,2862,2949,3030,3112,3179,3244,3300,3387,3475,3563,3671,3756,3824,3890,3960,4023,4109,4200", "endColumns": "71,75,92,73,77,88,139,144,107,84,81,86,86,112,86,86,136,93,78,57,60,77,79,85,138,66,72,76,63,71,70,67,86,80,81,66,64,55,86,87,87,107,84,67,65,69,62,85,90,78", "endOffsets": "122,198,291,365,443,532,672,817,925,1010,1092,1179,1266,1379,1466,1553,1690,1784,1863,1921,1982,2060,2140,2226,2365,2432,2505,2582,2646,2718,2789,2857,2944,3025,3107,3174,3239,3295,3382,3470,3558,3666,3751,3819,3885,3955,4018,4104,4195,4274"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13669,19762,21344,22123,22197,25723,26144,26284,26429,26537,26712,26794,26881,26968,27081,27168,27255,27392,27565,27644,27702,27763,27841,27921,28007,28146,28213,28286,28363,28427,28499,28570,28638,29157,29238,29320,29387,29452,29508,29595,29683,29771,29879,29964,32041,32549,32619,32682,32768,32859", "endColumns": "71,75,92,73,77,88,139,144,107,84,81,86,86,112,86,86,136,93,78,57,60,77,79,85,138,66,72,76,63,71,70,67,86,80,81,66,64,55,86,87,87,107,84,67,65,69,62,85,90,78", "endOffsets": "13736,19833,21432,22192,22270,25807,26279,26424,26532,26617,26789,26876,26963,27076,27163,27250,27387,27481,27639,27697,27758,27836,27916,28002,28141,28208,28281,28358,28422,28494,28565,28633,28720,29233,29315,29382,29447,29503,29590,29678,29766,29874,29959,30027,32102,32614,32677,32763,32854,32933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4529,4663,4783,4889,5021,5141,5246,5467,5601,5702,5835,5954,6074,6194,6254,6313", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4524,4658,4778,4884,5016,5136,5241,5340,5596,5697,5830,5949,6069,6189,6249,6308,6379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2511,2590,2665,2718,2769,2835,2905,2975,3052,3122,3186,3257,3325,3388,3479,3558,3621,3701,3783,3855,3926,3998,4046,4118,4182,4257,4334,4396,4460,4523,4590,4676,4762,4843,4926,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2506,2585,2660,2713,2764,2830,2900,2970,3047,3117,3181,3252,3320,3383,3474,3553,3616,3696,3778,3850,3921,3993,4041,4113,4177,4252,4329,4391,4455,4518,4585,4671,4757,4838,4921,4978,5033,5106"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3921,4006,4110,6884,7104,7540,7760,7818,7899,7960,8024,8079,8138,8195,8249,8342,8398,8455,8509,8575,8675,8751,8832,8954,9016,9078,9179,9258,9333,9386,9437,9503,9573,9643,9720,9790,9854,9925,9993,10056,10147,10226,10289,10369,10451,10523,10594,10666,10714,10786,10850,10925,11002,11064,11128,11191,11258,11344,11430,11511,11594,11651,12064", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "298,2951,3015,3084,3165,3247,4001,4105,4181,6942,7183,7599,7813,7894,7955,8019,8074,8133,8190,8244,8337,8393,8450,8504,8570,8670,8746,8827,8949,9011,9073,9174,9253,9328,9381,9432,9498,9568,9638,9715,9785,9849,9920,9988,10051,10142,10221,10284,10364,10446,10518,10589,10661,10709,10781,10845,10920,10997,11059,11123,11186,11253,11339,11425,11506,11589,11646,11701,12132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,179,266,322,393,446,500,564,645,789,992,1210,1282,1352,1422,1501,1584,1686,1757,1820,1952,2015,2071,2133,2200,2313,2403,2484,2561,2629,2697,2758", "endColumns": "61,61,86,55,70,52,53,63,80,143,202,217,71,69,69,78,82,101,70,62,131,62,55,61,66,112,89,80,76,67,67,60,114", "endOffsets": "112,174,261,317,388,441,495,559,640,784,987,1205,1277,1347,1417,1496,1579,1681,1752,1815,1947,2010,2066,2128,2195,2308,2398,2479,2556,2624,2692,2753,2868"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13607,13741,24284,25612,30157,31224,31277,31331,31395,31476,31620,31823,32938,33010,33080,33150,33229,33312,33414,33485,33610,33742,33805,33861,33923,33990,34103,34193,34274,34351,34419,34487,34548", "endColumns": "61,61,86,55,70,52,53,63,80,143,202,217,71,69,69,78,82,101,70,62,131,62,55,61,66,112,89,80,76,67,67,60,114", "endOffsets": "13664,13798,24366,25663,30223,31272,31326,31390,31471,31615,31818,32036,33005,33075,33145,33224,33307,33409,33480,33543,33737,33800,33856,33918,33985,34098,34188,34269,34346,34414,34482,34543,34658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12664,12743,12832,12899,12966,13029,13099", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "12738,12827,12894,12961,13024,13094,13155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "6451,7188,7283,7384", "endColumns": "92,94,100,94", "endOffsets": "6539,7278,7379,7474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14063,14282,14516,14664,14739,14803,15258,15320,15443,15503,15577,15651,15718,15778,15846,15967,16034,16247,17036,17095,17979,18124,18190,21138,21213,22031,22635,22684,22803,23365,23423,24138,24217,24790,24863", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "14118,14350,14575,14734,14798,14859,15315,15376,15498,15572,15646,15713,15773,15841,15905,16029,16093,16308,17090,17167,18047,18185,18257,21208,21285,22073,22679,22745,22860,23418,23484,24212,24279,24858,24930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "23673", "endColumns": "73", "endOffsets": "23742"}}]}]}