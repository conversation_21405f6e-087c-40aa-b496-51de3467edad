import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Register with email and password use case
class RegisterWithEmailAndPassword {
  final AuthRepository _repository;

  RegisterWithEmailAndPassword(this._repository);

  /// Call the use case
  Future<Either<AuthFailure, UserEntity>> call(Params params) {
    return _repository.registerWithEmailAndPassword(
      params.email,
      params.password,
      params.name,
    );
  }
}

/// Register with email and password parameters
class Params extends Equatable {
  final String email;
  final String password;
  final String name;

  const Params({
    required this.email,
    required this.password,
    required this.name,
  });

  @override
  List<Object?> get props => [email, password, name];
}
