import '../../domain/entities/transaction_record.dart';

/// Transaction record model
class TransactionRecordModel extends TransactionRecord {
  /// Constructor
  const TransactionRecordModel({
    required super.id,
    required super.userId,
    required super.subscriberId,
    required super.subscriptionPlanId,
    required super.amount,
    required super.currencyCode,
    required super.status,
    required super.type,
    required super.paymentMethod,
    required super.paymentProcessor,
    required super.processorTransactionId,
    super.errorMessage,
    super.errorCode,
    super.billingAddress,
    super.billingCity,
    super.billingState,
    super.billingPostalCode,
    super.billingCountry,
    super.ipAddress,
    super.userAgent,
    super.receiptUrl,
    super.invoiceUrl,
    super.storeReceiptId,
    super.originalTransactionId,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create a model from a JSON map
  factory TransactionRecordModel.fromJson(Map<String, dynamic> json) {
    return TransactionRecordModel(
      id: json['id'],
      userId: json['user_id'],
      subscriberId: json['subscriber_id'],
      subscriptionPlanId: json['subscription_plan_id'],
      amount: json['amount'].toDouble(),
      currencyCode: json['currency_code'],
      status: json['status'],
      type: json['type'],
      paymentMethod: json['payment_method'],
      paymentProcessor: json['payment_processor'],
      processorTransactionId: json['processor_transaction_id'],
      errorMessage: json['error_message'],
      errorCode: json['error_code'],
      billingAddress: json['billing_address'],
      billingCity: json['billing_city'],
      billingState: json['billing_state'],
      billingPostalCode: json['billing_postal_code'],
      billingCountry: json['billing_country'],
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      receiptUrl: json['receipt_url'],
      invoiceUrl: json['invoice_url'],
      storeReceiptId: json['store_receipt_id'],
      originalTransactionId: json['original_transaction_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Convert the model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'subscriber_id': subscriberId,
      'subscription_plan_id': subscriptionPlanId,
      'amount': amount,
      'currency_code': currencyCode,
      'status': status,
      'type': type,
      'payment_method': paymentMethod,
      'payment_processor': paymentProcessor,
      'processor_transaction_id': processorTransactionId,
      'error_message': errorMessage,
      'error_code': errorCode,
      'billing_address': billingAddress,
      'billing_city': billingCity,
      'billing_state': billingState,
      'billing_postal_code': billingPostalCode,
      'billing_country': billingCountry,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'receipt_url': receiptUrl,
      'invoice_url': invoiceUrl,
      'store_receipt_id': storeReceiptId,
      'original_transaction_id': originalTransactionId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
