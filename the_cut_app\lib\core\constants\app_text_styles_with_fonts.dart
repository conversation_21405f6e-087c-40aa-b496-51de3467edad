import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../services/font_service.dart';
import 'app_colors.dart';

/// Text styles with Google Fonts support
class AppTextStylesWithFonts {
  /// Get heading 1 style with the appropriate font
  static TextStyle getH1(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getHeadingStyle(
      fontSize: 32.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
      height: 1.2,
    );
  }

  /// Get heading 2 style with the appropriate font
  static TextStyle getH2(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getHeadingStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
      height: 1.2,
    );
  }

  /// Get heading 3 style with the appropriate font
  static TextStyle getH3(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getHeadingStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
      height: 1.2,
    );
  }

  /// Get heading 4 style with the appropriate font
  static TextStyle getH4(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getHeadingStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
      height: 1.2,
    );
  }

  /// Get heading 5 style with the appropriate font
  static TextStyle getH5(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getHeadingStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
      height: 1.2,
    );
  }

  /// Get body large style with the appropriate font
  static TextStyle getBodyLarge(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getBodyStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.normal,
      color: AppColors.textPrimary,
      height: 1.5,
    );
  }

  /// Get body medium style with the appropriate font
  static TextStyle getBodyMedium(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getBodyStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      color: AppColors.textPrimary,
      height: 1.5,
    );
  }

  /// Get body small style with the appropriate font
  static TextStyle getBodySmall(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getBodyStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.normal,
      color: AppColors.textSecondary,
      height: 1.5,
    );
  }

  /// Get button large style with the appropriate font
  static TextStyle getButtonLarge(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getButtonStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.bold,
      color: Colors.white,
      height: 1.2,
    );
  }

  /// Get button medium style with the appropriate font
  static TextStyle getButtonMedium(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getButtonStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.bold,
      color: Colors.white,
      height: 1.2,
    );
  }

  /// Get button small style with the appropriate font
  static TextStyle getButtonSmall(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getButtonStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.bold,
      color: Colors.white,
      height: 1.2,
    );
  }

  /// Get caption style with the appropriate font
  static TextStyle getCaption(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getBodyStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.normal,
      color: AppColors.textSecondary,
      height: 1.2,
      decoration: TextDecoration.none,
    );
  }

  /// Get overline style with the appropriate font
  static TextStyle getOverline(BuildContext context) {
    final fontService = FontService.of(context);
    return fontService.getBodyStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.normal,
      color: AppColors.textSecondary,
      height: 1.2,
      decoration: TextDecoration.none,
    );
  }
}
