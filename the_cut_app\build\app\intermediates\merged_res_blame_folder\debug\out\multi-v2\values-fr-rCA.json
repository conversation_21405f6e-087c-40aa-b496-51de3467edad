{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "69,76,144,148,429,433,434", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7285,7893,13546,13851,42754,43193,43279", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "7350,7985,13618,13989,42918,43274,43354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14424,14514,14620,14687,14755,14820,14895", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "14509,14615,14682,14750,14815,14890,14958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,249,401,517,620,744,915,1114,1287,1377,1487,1589,1691,1886,2013,2125,2386,2497,2596,2657,2723,2808,2910,3015,3259,3334,3409,3493,3560,3644,3715,3784,3905,4002,4105,4182,4253,4318,4433,4550,4661,4801,4908,4980,5060,5161,5237,5362,5519", "endColumns": "86,106,151,115,102,123,170,198,172,89,109,101,101,194,126,111,260,110,98,60,65,84,101,104,243,74,74,83,66,83,70,68,120,96,102,76,70,64,114,116,110,139,106,71,79,100,75,124,156,88", "endOffsets": "137,244,396,512,615,739,910,1109,1282,1372,1482,1584,1686,1881,2008,2120,2381,2492,2591,2652,2718,2803,2905,3010,3254,3329,3404,3488,3555,3639,3710,3779,3900,3997,4100,4177,4248,4313,4428,4545,4656,4796,4903,4975,5055,5156,5232,5357,5514,5603"}, "to": {"startLines": "169,248,265,275,276,324,330,331,332,333,335,336,337,338,339,340,341,342,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,364,365,366,367,368,369,370,371,372,373,374,394,402,403,404,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15548,22940,25127,26169,26285,30855,31364,31535,31734,31907,32106,32216,32318,32420,32615,32742,32854,33115,33325,33424,33485,33551,33636,33738,33843,34087,34162,34237,34321,34388,34472,34543,34612,35198,35295,35398,35475,35546,35611,35726,35843,35954,36094,36201,39436,40029,40130,40206,40331,40488", "endColumns": "86,106,151,115,102,123,170,198,172,89,109,101,101,194,126,111,260,110,98,60,65,84,101,104,243,74,74,83,66,83,70,68,120,96,102,76,70,64,114,116,110,139,106,71,79,100,75,124,156,88", "endOffsets": "15630,23042,25274,26280,26383,30974,31530,31729,31902,31992,32211,32313,32415,32610,32737,32849,33110,33221,33419,33480,33546,33631,33733,33838,34082,34157,34232,34316,34383,34467,34538,34607,34728,35290,35393,35470,35541,35606,35721,35838,35949,36089,36196,36268,39511,40125,40201,40326,40483,40572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,430,431,432", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4608,4707,7462,7560,7740,8631,8711,13367,13459,13623,13694,13994,14075,14247,42923,43002,43071", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4702,4790,7555,7661,7822,8706,8800,13454,13541,13689,13757,14070,14155,14318,42997,43066,43188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4795,7666,7990", "endColumns": "74,73,76", "endOffsets": "4865,7735,8062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4976,5156,5286,5395,5566,5699,5820,6094,6289,6401,6586,6722,6882,7061,7134,7201", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "4971,5151,5281,5390,5561,5694,5815,5928,6284,6396,6581,6717,6877,7056,7129,7196,7280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3578,3676,3778,3877,3979,4083,4187,14323", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3671,3773,3872,3974,4078,4182,4296,14419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5933", "endColumns": "160", "endOffsets": "6089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,303,304,312,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16029,16280,16553,16700,16785,16856,17349,17421,17565,17629,17697,17767,17827,17887,17961,18083,18154,18403,19288,19353,20445,20614,20706,24872,24990,26070,26809,26859,27011,27811,27886,28672,28783,29584,29696", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "16089,16357,16615,16780,16851,16912,17416,17489,17624,17692,17762,17822,17882,17956,18020,18149,18212,18463,19348,19432,20521,20701,20799,24985,25067,26116,26854,26951,27068,27881,27952,28778,28867,29691,29803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,3010,3103,3182,3237,3288,3354,3433,3514,3605,3685,3757,3835,3910,3982,4093,4190,4267,4365,4463,4541,4622,4722,4779,4863,4929,5012,5099,5161,5225,5288,5364,5466,5573,5670,5776,5835,5890", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,3005,3098,3177,3232,3283,3349,3428,3509,3600,3680,3752,3830,3905,3977,4088,4185,4262,4360,4458,4536,4617,4717,4774,4858,4924,5007,5094,5156,5220,5283,5359,5461,5568,5665,5771,5830,5885,5974"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,4301,4401,4523,7827,8067,8551,8805,8867,8959,9026,9100,9161,9240,9304,9358,9474,9533,9595,9649,9731,9860,9952,10036,10180,10259,10340,10487,10580,10659,10714,10765,10831,10910,10991,11082,11162,11234,11312,11387,11459,11570,11667,11744,11842,11940,12018,12099,12199,12256,12340,12406,12489,12576,12638,12702,12765,12841,12943,13050,13147,13253,13312,13762", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "328,3185,3293,3375,3476,3573,4396,4518,4603,7888,8159,8626,8862,8954,9021,9095,9156,9235,9299,9353,9469,9528,9590,9644,9726,9855,9947,10031,10175,10254,10335,10482,10575,10654,10709,10760,10826,10905,10986,11077,11157,11229,11307,11382,11454,11565,11662,11739,11837,11935,12013,12094,12194,12251,12335,12401,12484,12571,12633,12697,12760,12836,12938,13045,13142,13248,13307,13362,13846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,217,354,429,525,587,645,724,848,1028,1346,1682,1775,1845,1937,2034,2165,2307,2386,2458,2621,2690,2751,2827,2921,3068,3186,3295,3387,3463,3545,3622", "endColumns": "78,82,136,74,95,61,57,78,123,179,317,335,92,69,91,96,130,141,78,71,162,68,60,75,93,146,117,108,91,75,81,76,157", "endOffsets": "129,212,349,424,520,582,640,719,843,1023,1341,1677,1770,1840,1932,2029,2160,2302,2381,2453,2616,2685,2746,2822,2916,3063,3181,3290,3382,3458,3540,3617,3775"}, "to": {"startLines": "168,170,305,322,376,387,388,389,390,391,392,393,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,425,426,427,428", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15469,15635,28872,30703,36471,38279,38341,38399,38478,38602,38782,39100,40577,40670,40740,40832,40929,41060,41202,41281,41432,41595,41664,41725,41801,41895,42042,42160,42269,42361,42437,42519,42596", "endColumns": "78,82,136,74,95,61,57,78,123,179,317,335,92,69,91,96,130,141,78,71,162,68,60,75,93,146,117,108,91,75,81,76,157", "endOffsets": "15543,15713,29004,30773,36562,38336,38394,38473,38597,38777,39095,39431,40665,40735,40827,40924,41055,41197,41276,41348,41590,41659,41720,41796,41890,42037,42155,42264,42356,42432,42514,42591,42749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16094,16917,17059,17121,17203,17494,18025,18217,18468,18798,19146,19437", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "16171,16972,17116,17198,17260,17560,18078,18294,18536,18860,19201,19502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,429,476,548,652,727,1000,1063,1158,1232,1287,1351,1675,1742,1809,1864,1919,2013,2163,2276,2335,2422,2510,2603,2674,2777,3079,3160,3239,3316,3378,3440,3518,3627,3726,3844,3947,4021,4100,4191,4389,4599,4732,4794,5661,5726", "endColumns": "162,210,46,71,103,74,272,62,94,73,54,63,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,108,98,117,102,73,78,90,197,209,132,61,866,64,57", "endOffsets": "213,424,471,543,647,722,995,1058,1153,1227,1282,1346,1670,1737,1804,1859,1914,2008,2158,2271,2330,2417,2505,2598,2669,2772,3074,3155,3234,3311,3373,3435,3513,3622,3721,3839,3942,4016,4095,4186,4384,4594,4727,4789,5656,5721,5779"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,298,299,300,301,302,308,311,316,318,319,320,321,323,325,326,329,334,343,359,360,361,362,363,375,381,382,383,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20926,21089,22211,22258,22330,22434,22509,22782,22845,24798,25072,25279,25746,26388,26455,26956,27957,28169,28263,28413,28526,28585,29203,29491,29987,30138,30241,30543,30624,30778,30979,31041,31286,31997,33226,34733,34851,34954,35028,35107,36273,36873,37083,37216,37347,38214,39971", "endColumns": "162,210,46,71,103,74,272,62,94,73,54,63,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,108,98,117,102,73,78,90,197,209,132,61,866,64,57", "endOffsets": "21084,21295,22253,22325,22429,22504,22777,22840,22935,24867,25122,25338,26065,26450,26517,27006,28007,28258,28408,28521,28580,28667,29286,29579,30053,30236,30538,30619,30698,30850,31036,31098,31359,32101,33320,34846,34949,35023,35102,35193,36466,37078,37211,37273,38209,38274,40024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3368,3433,4196,4935,5014,5128,5239,5294,5405,5517,5591,5697,5803,5859,5947,5997,6080,6198,6273,6350,6398,6486,6588,6636,6685,6754,6812,6877,7078,7274,7423,7491,7580,7666,7774,7873,7974,8057,8153,8233,8315,8416,8470,8615,8667,8722,8791,8861,8936,9006,9080,9170,9246", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,47,87,101,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,81,100,53,144,51,54,68,69,74,69,73,89,75,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3363,3428,4191,4930,5009,5123,5234,5289,5400,5512,5586,5692,5798,5854,5942,5992,6075,6193,6268,6345,6393,6481,6583,6631,6680,6749,6807,6872,7073,7269,7418,7486,7575,7661,7769,7868,7969,8052,8148,8228,8310,8411,8465,8610,8662,8717,8786,8856,8931,9001,9075,9165,9241,9320"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,306,307,309,310,314,315,317,327,328,377,378,379,380,384,395,396,397,398,399,400,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14963,15036,15118,15180,15256,15341,15402,15718,15769,15854,15942,16176,16362,16466,16620,16977,17265,18299,18541,18636,18706,18865,18954,19039,19206,19507,19599,19676,19775,19858,19965,20065,20161,20253,20363,20526,20804,21300,21383,21448,23047,23786,23865,23979,24090,24145,24256,24368,24442,24548,24654,24710,25343,25393,25476,25594,25669,26121,26522,26610,26712,26760,27073,27142,27200,27265,27466,27662,28012,28080,29009,29095,29291,29390,29808,29891,30058,31103,31185,36567,36621,36766,36818,37278,39516,39586,39661,39731,39805,39895,41353", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,47,87,101,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,81,100,53,144,51,54,68,69,74,69,73,89,75,78", "endOffsets": "15031,15113,15175,15251,15336,15397,15464,15764,15849,15937,16024,16275,16461,16548,16695,17054,17344,18398,18631,18701,18793,18949,19034,19141,19283,19594,19671,19770,19853,19960,20060,20156,20248,20358,20440,20609,20921,21378,21443,22206,23781,23860,23974,24085,24140,24251,24363,24437,24543,24649,24705,24793,25388,25471,25589,25664,25741,26164,26605,26707,26755,26804,27137,27195,27260,27461,27657,27806,28075,28164,29090,29198,29385,29486,29886,29982,30133,31180,31281,36616,36761,36813,36868,37342,39581,39656,39726,39800,39890,39966,41427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,14160", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,14242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7355,8164,8266,8385", "endColumns": "106,101,118,104", "endOffsets": "7457,8261,8380,8485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "82,435", "startColumns": "4,4", "startOffsets": "8490,43359", "endColumns": "60,78", "endOffsets": "8546,43433"}}]}]}