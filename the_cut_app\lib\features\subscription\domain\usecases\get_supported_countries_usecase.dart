import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/country.dart';
import '../repositories/subscription_repository.dart';

/// Use case to get all supported countries
class GetSupportedCountriesUseCase implements UseCase<List<Country>, NoParams> {
  final SubscriptionRepository repository;

  /// Constructor
  GetSupportedCountriesUseCase(this.repository);

  @override
  Future<Either<Failure, List<Country>>> call(NoParams params) {
    return repository.getSupportedCountries();
  }
}
