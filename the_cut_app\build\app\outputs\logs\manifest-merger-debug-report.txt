-- Merging decision tree log ---
application
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-15:19
MERGED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-15:19
MERGED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:20:5-75:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\685699fa4309ca50fd69a877860bc02b\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\685699fa4309ca50fd69a877860bc02b\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\25ce22bb72a4fddb37d9e95934317678\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\25ce22bb72a4fddb37d9e95934317678\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:7:5-48:19
MERGED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:7:5-48:19
MERGED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:7:5-32:19
MERGED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:7:5-32:19
MERGED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:14:5-84:19
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:14:5-84:19
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4eb9373a7ee3dd3301a844fcb555a14\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4eb9373a7ee3dd3301a844fcb555a14\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\4299239815b7f9a5883b5cb01612a4c3\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\4299239815b7f9a5883b5cb01612a4c3\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2f00402bfd0b088249804b0b98cc42e3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2f00402bfd0b088249804b0b98cc42e3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\de588f3e41baed2cae83b920285ea65c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\de588f3e41baed2cae83b920285ea65c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df0b063bde49692f417bea1c2447a4ba\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df0b063bde49692f417bea1c2447a4ba\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e368023e492899f701c19ed1c11e9ce7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e368023e492899f701c19ed1c11e9ce7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\78cec0b01c3d17b124e522fd77b8536c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\78cec0b01c3d17b124e522fd77b8536c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
	android:extractNativeLibs
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:connectivity_plus] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_image_compress_common] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_image_compress_common\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:in_app_purchase_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:1-17:12
MERGED from [:shared_preferences_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\stripe_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:2:1-77:12
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:2:1-38:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\685699fa4309ca50fd69a877860bc02b\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d3f8b9c9581f39891fcdb7eef0026e8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\25ce22bb72a4fddb37d9e95934317678\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:stripe-android:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b210ccd119d42918ca07d154a3ec0c1\transformed\jetified-stripe-android-20.44.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:2:1-50:12
MERGED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:2:1-34:12
MERGED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:2:1-86:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\55a5477172d2941b7cce3cc5b8425236\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7dfbd571e75b3c5e829a11f8c3c8804\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4eb9373a7ee3dd3301a844fcb555a14\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd005e293c1eb53732a0b5d3e7f5ddfa\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2a01d92e8771285da956a3986b03d7c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b751b9688789e82163d76292e65b0e3\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08103da82948330208052d435fc8a0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae11824af70003071d744482c8268ba3\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c87355b6f3e3da3026816a84304e4577\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\cf35fd16ad89193b236e95de3c44d535\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.stripe:stripe-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d8f0c43b715b185ebfed0e6fe335db8d\transformed\jetified-stripe-ui-core-20.44.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:payments-model:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\184421f82e0246495d8e381cf951d126\transformed\jetified-payments-model-20.44.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bef774bac08091f67bb9959ed680515\transformed\jetified-stripe-core-20.44.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\966f05d9bf231469117b5beee1a42aa6\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\7e5928b359139ff821a4e9a2b659efff\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e56f46112ded64bc39c3171d9c8c1405\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a973c349864793d7306541d057ac1475\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a67a2ad5d64e85592c2192cfc184c4c9\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded4bb8e87476415f3e5690fb4fc570e\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\95110e5d817d6b16d8cd9f1aa70c1423\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\d784acfb950a92b3ccf55037fd259613\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e41d9fa2dbb9f6d9b0373aed46628dd\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39b1694acb51889574ace2d8e4921946\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89f2cea4cbc186372550bf72e577da39\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\35895a720c3f22946f4f2fbace1b3127\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e44092faf47a7835f8e41ab52e9a63e\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2b68a7f68b639d262456d696bd09bc\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1f55635895d39f693596925f61627a1f\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9339f3eb685af3badf7424ee1098dbdc\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e94261c9f130d7eb165912ce8589ad35\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\433242dcbb2c31df4376c186925aa114\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f6349f4e63714b7ebf7a937830450a5c\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7230d3d41ee8a2afb778cf482e969cc\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bf2871141c92a856e6ed9f3f83844900\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6428a4957e6c8308bac49795838da874\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb0735eb473b6cb8e71f72d6b9f66910\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7405faed8859703a42eda0e78e19950\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0b6805e8ea0f5db23ef8d6347fbafa1c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a514412d7c8067403d63e8e9b80b5015\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\d463491c37d4f221583d93d6092daeac\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\e03cbe2fee1b39189bf716c53f88720a\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21370f5cb50e63839e6b7250a08d65e\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\4299239815b7f9a5883b5cb01612a4c3\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2f00402bfd0b088249804b0b98cc42e3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:hcaptcha:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b58daadc921f951a852046faf39c73c5\transformed\jetified-hcaptcha-20.44.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdbb878f296b2af8feacc2e3a93f8edd\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f92abf393023a99f09b879d3586a2fd7\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2012c24c0de3282c94d3967593d612\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a3361001e2576cce1e78ccd15bd41ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6957bc3b43b9b6db5acff416ba03f4df\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\956cbcd2d5ca0ae8ac3d67eebeb2cf38\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e7b1b474706c7a79d5c2eda43fe9363b\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d6c84e875ac7ce95ff64b594f0837032\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e297fda208dad41e1a2cb3aa1b4cf4de\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2fca6cb20f70e1308d2b4359f1e30c1\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f04c459438658001480b9aa51150ac1\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\321466f8d49233556092fdef7225ca70\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\025c4462321b6639ab3351e5ef3d225a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a52a2f3840a107d64d879c445b88f206\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7004ef3bd45d32860f8759be33e971a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e694091f62610ffbc06cce4406c319f9\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\14bc6f3b324f73f761c1fb983e9d06ad\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\276eb0009f09718a8c9e42a78d423afe\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\68894b9791d5587db8480cd28ae65169\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\de588f3e41baed2cae83b920285ea65c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df0b063bde49692f417bea1c2447a4ba\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\deedb2820f21e44393b3024ae8203b14\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\6e03e4ba7904df6e3ad8f033bb548a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\89a13065f26dc6cc1d474c2d1acd31cf\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\618540dd7395acbf2c1cfbba06e55aee\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\60ea357b6991ae60cfc29170380c5c9a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f6329023dd3191e366944ee356e8ce7\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\822651ea552a80822c1a43e4f12a07a2\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bc9c0a5ecbc53fd5f73d1cdae8fa99f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8ee11738761d99fe90b8edd2b1c42c7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\40066afe43e505c11a4e4ea5f7ca677b\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82b9bc1044be575831fe904f6a0e062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3603ecfcbad93a9cfd1383ed55537837\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4edc67630626ed8b4fffa80b0e9b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65427501b3119b9b001981abc47914b1\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa3904431ead64e8c3c583e83557be1e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\638672b7bfdde71f6a7bae19d93a4f92\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db71eb47f27e39f120ae288f8cd4a2c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\04c9c23113d736e8f65be68affc9efdb\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\18a7a5cbe425467ea21724947b513291\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fcaa49d26d534f0ecad24a88c2cf57af\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8db0e57fe2796b1e09bd24a0771f63d9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d474d1fc20e1c057cce2522301ff5d39\transformed\heifwriter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\fe1848d7b4fd8d15e8ea305e1def08f9\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0988952d1021d8449a3e15ea166a776\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6ca9d94d5156bef21beac0b68b018e8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e368023e492899f701c19ed1c11e9ce7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48d600a7604f9a69686e9bf232a5c521\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\78cec0b01c3d17b124e522fd77b8536c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a22e1160783e483400676ceb4a17992b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a172cad81a42d497d39d980b645a9c56\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\24433f89ae8f54d5d1ae39fb943784c3\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\004d897fd1a88ff65e571796e31ba0d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b93fe91324c1b99d544cb8fd0e3aeacb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dda66f9b4d6a30a4519ae2fc72ba5c8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6496bea76a07f2cc9fff56655fa74233\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef759e02d7a6017394b6578dbe631031\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dab232964e2fd4aa21d9e8733b6dab43\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4be3f0219841a1a97978e8443f4669f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\17fcbdd3c2256521c0db572b37155d11\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
	package
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
MERGED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:10:5-18:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:8:5-12:15
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:connectivity_plus] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_image_compress_common] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_image_compress_common\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:55
MERGED from [:flutter_image_compress_common] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_image_compress_common\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:55
MERGED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-44
MERGED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-44
MERGED from [:shared_preferences_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\stripe_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\stripe_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\685699fa4309ca50fd69a877860bc02b\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\685699fa4309ca50fd69a877860bc02b\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d3f8b9c9581f39891fcdb7eef0026e8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d3f8b9c9581f39891fcdb7eef0026e8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\25ce22bb72a4fddb37d9e95934317678\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\25ce22bb72a4fddb37d9e95934317678\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b210ccd119d42918ca07d154a3ec0c1\transformed\jetified-stripe-android-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b210ccd119d42918ca07d154a3ec0c1\transformed\jetified-stripe-android-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\55a5477172d2941b7cce3cc5b8425236\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\55a5477172d2941b7cce3cc5b8425236\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7dfbd571e75b3c5e829a11f8c3c8804\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7dfbd571e75b3c5e829a11f8c3c8804\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4eb9373a7ee3dd3301a844fcb555a14\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e4eb9373a7ee3dd3301a844fcb555a14\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd005e293c1eb53732a0b5d3e7f5ddfa\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bd005e293c1eb53732a0b5d3e7f5ddfa\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2a01d92e8771285da956a3986b03d7c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2a01d92e8771285da956a3986b03d7c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b751b9688789e82163d76292e65b0e3\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b751b9688789e82163d76292e65b0e3\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08103da82948330208052d435fc8a0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e08103da82948330208052d435fc8a0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae11824af70003071d744482c8268ba3\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ae11824af70003071d744482c8268ba3\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c87355b6f3e3da3026816a84304e4577\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c87355b6f3e3da3026816a84304e4577\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\cf35fd16ad89193b236e95de3c44d535\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\cf35fd16ad89193b236e95de3c44d535\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.stripe:stripe-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d8f0c43b715b185ebfed0e6fe335db8d\transformed\jetified-stripe-ui-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d8f0c43b715b185ebfed0e6fe335db8d\transformed\jetified-stripe-ui-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\184421f82e0246495d8e381cf951d126\transformed\jetified-payments-model-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\184421f82e0246495d8e381cf951d126\transformed\jetified-payments-model-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bef774bac08091f67bb9959ed680515\transformed\jetified-stripe-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bef774bac08091f67bb9959ed680515\transformed\jetified-stripe-core-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\966f05d9bf231469117b5beee1a42aa6\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\966f05d9bf231469117b5beee1a42aa6\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\7e5928b359139ff821a4e9a2b659efff\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\7e5928b359139ff821a4e9a2b659efff\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e56f46112ded64bc39c3171d9c8c1405\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e56f46112ded64bc39c3171d9c8c1405\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a973c349864793d7306541d057ac1475\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a973c349864793d7306541d057ac1475\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a67a2ad5d64e85592c2192cfc184c4c9\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\a67a2ad5d64e85592c2192cfc184c4c9\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded4bb8e87476415f3e5690fb4fc570e\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\ded4bb8e87476415f3e5690fb4fc570e\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\95110e5d817d6b16d8cd9f1aa70c1423\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\95110e5d817d6b16d8cd9f1aa70c1423\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\d784acfb950a92b3ccf55037fd259613\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\d784acfb950a92b3ccf55037fd259613\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e41d9fa2dbb9f6d9b0373aed46628dd\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e41d9fa2dbb9f6d9b0373aed46628dd\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39b1694acb51889574ace2d8e4921946\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39b1694acb51889574ace2d8e4921946\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89f2cea4cbc186372550bf72e577da39\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\89f2cea4cbc186372550bf72e577da39\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\35895a720c3f22946f4f2fbace1b3127\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\35895a720c3f22946f4f2fbace1b3127\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e44092faf47a7835f8e41ab52e9a63e\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e44092faf47a7835f8e41ab52e9a63e\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2b68a7f68b639d262456d696bd09bc\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2b68a7f68b639d262456d696bd09bc\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1f55635895d39f693596925f61627a1f\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1f55635895d39f693596925f61627a1f\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9339f3eb685af3badf7424ee1098dbdc\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9339f3eb685af3badf7424ee1098dbdc\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e94261c9f130d7eb165912ce8589ad35\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e94261c9f130d7eb165912ce8589ad35\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\433242dcbb2c31df4376c186925aa114\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\433242dcbb2c31df4376c186925aa114\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f6349f4e63714b7ebf7a937830450a5c\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f6349f4e63714b7ebf7a937830450a5c\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7230d3d41ee8a2afb778cf482e969cc\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c7230d3d41ee8a2afb778cf482e969cc\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bf2871141c92a856e6ed9f3f83844900\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bf2871141c92a856e6ed9f3f83844900\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6428a4957e6c8308bac49795838da874\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6428a4957e6c8308bac49795838da874\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb0735eb473b6cb8e71f72d6b9f66910\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb0735eb473b6cb8e71f72d6b9f66910\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7405faed8859703a42eda0e78e19950\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f7405faed8859703a42eda0e78e19950\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0b6805e8ea0f5db23ef8d6347fbafa1c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0b6805e8ea0f5db23ef8d6347fbafa1c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a514412d7c8067403d63e8e9b80b5015\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a514412d7c8067403d63e8e9b80b5015\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\d463491c37d4f221583d93d6092daeac\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\d463491c37d4f221583d93d6092daeac\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\e03cbe2fee1b39189bf716c53f88720a\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\e03cbe2fee1b39189bf716c53f88720a\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21370f5cb50e63839e6b7250a08d65e\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\f21370f5cb50e63839e6b7250a08d65e\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\4299239815b7f9a5883b5cb01612a4c3\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\4299239815b7f9a5883b5cb01612a4c3\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2f00402bfd0b088249804b0b98cc42e3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\2f00402bfd0b088249804b0b98cc42e3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.stripe:hcaptcha:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b58daadc921f951a852046faf39c73c5\transformed\jetified-hcaptcha-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b58daadc921f951a852046faf39c73c5\transformed\jetified-hcaptcha-20.44.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdbb878f296b2af8feacc2e3a93f8edd\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bdbb878f296b2af8feacc2e3a93f8edd\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f92abf393023a99f09b879d3586a2fd7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f92abf393023a99f09b879d3586a2fd7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2012c24c0de3282c94d3967593d612\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a2012c24c0de3282c94d3967593d612\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a3361001e2576cce1e78ccd15bd41ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a3361001e2576cce1e78ccd15bd41ec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6957bc3b43b9b6db5acff416ba03f4df\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6957bc3b43b9b6db5acff416ba03f4df\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\956cbcd2d5ca0ae8ac3d67eebeb2cf38\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\956cbcd2d5ca0ae8ac3d67eebeb2cf38\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e7b1b474706c7a79d5c2eda43fe9363b\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e7b1b474706c7a79d5c2eda43fe9363b\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d6c84e875ac7ce95ff64b594f0837032\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d6c84e875ac7ce95ff64b594f0837032\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e297fda208dad41e1a2cb3aa1b4cf4de\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e297fda208dad41e1a2cb3aa1b4cf4de\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2fca6cb20f70e1308d2b4359f1e30c1\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2fca6cb20f70e1308d2b4359f1e30c1\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f04c459438658001480b9aa51150ac1\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f04c459438658001480b9aa51150ac1\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\321466f8d49233556092fdef7225ca70\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\321466f8d49233556092fdef7225ca70\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\025c4462321b6639ab3351e5ef3d225a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\025c4462321b6639ab3351e5ef3d225a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a52a2f3840a107d64d879c445b88f206\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a52a2f3840a107d64d879c445b88f206\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7004ef3bd45d32860f8759be33e971a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7004ef3bd45d32860f8759be33e971a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e694091f62610ffbc06cce4406c319f9\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\e694091f62610ffbc06cce4406c319f9\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\14bc6f3b324f73f761c1fb983e9d06ad\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\14bc6f3b324f73f761c1fb983e9d06ad\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\276eb0009f09718a8c9e42a78d423afe\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\276eb0009f09718a8c9e42a78d423afe\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\68894b9791d5587db8480cd28ae65169\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\68894b9791d5587db8480cd28ae65169\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\de588f3e41baed2cae83b920285ea65c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\de588f3e41baed2cae83b920285ea65c\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df0b063bde49692f417bea1c2447a4ba\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df0b063bde49692f417bea1c2447a4ba\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\deedb2820f21e44393b3024ae8203b14\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\deedb2820f21e44393b3024ae8203b14\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\6e03e4ba7904df6e3ad8f033bb548a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\6e03e4ba7904df6e3ad8f033bb548a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\89a13065f26dc6cc1d474c2d1acd31cf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\89a13065f26dc6cc1d474c2d1acd31cf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\618540dd7395acbf2c1cfbba06e55aee\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\618540dd7395acbf2c1cfbba06e55aee\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\60ea357b6991ae60cfc29170380c5c9a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\60ea357b6991ae60cfc29170380c5c9a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f6329023dd3191e366944ee356e8ce7\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f6329023dd3191e366944ee356e8ce7\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\822651ea552a80822c1a43e4f12a07a2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\822651ea552a80822c1a43e4f12a07a2\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bc9c0a5ecbc53fd5f73d1cdae8fa99f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bc9c0a5ecbc53fd5f73d1cdae8fa99f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8ee11738761d99fe90b8edd2b1c42c7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b8ee11738761d99fe90b8edd2b1c42c7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\40066afe43e505c11a4e4ea5f7ca677b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\40066afe43e505c11a4e4ea5f7ca677b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82b9bc1044be575831fe904f6a0e062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b82b9bc1044be575831fe904f6a0e062\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3603ecfcbad93a9cfd1383ed55537837\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3603ecfcbad93a9cfd1383ed55537837\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4edc67630626ed8b4fffa80b0e9b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4edc67630626ed8b4fffa80b0e9b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65427501b3119b9b001981abc47914b1\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65427501b3119b9b001981abc47914b1\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa3904431ead64e8c3c583e83557be1e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa3904431ead64e8c3c583e83557be1e\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\638672b7bfdde71f6a7bae19d93a4f92\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\638672b7bfdde71f6a7bae19d93a4f92\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db71eb47f27e39f120ae288f8cd4a2c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db71eb47f27e39f120ae288f8cd4a2c8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\04c9c23113d736e8f65be68affc9efdb\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\04c9c23113d736e8f65be68affc9efdb\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\18a7a5cbe425467ea21724947b513291\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\18a7a5cbe425467ea21724947b513291\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fcaa49d26d534f0ecad24a88c2cf57af\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fcaa49d26d534f0ecad24a88c2cf57af\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8db0e57fe2796b1e09bd24a0771f63d9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\8db0e57fe2796b1e09bd24a0771f63d9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d474d1fc20e1c057cce2522301ff5d39\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d474d1fc20e1c057cce2522301ff5d39\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\fe1848d7b4fd8d15e8ea305e1def08f9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\fe1848d7b4fd8d15e8ea305e1def08f9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0988952d1021d8449a3e15ea166a776\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0988952d1021d8449a3e15ea166a776\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6ca9d94d5156bef21beac0b68b018e8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6ca9d94d5156bef21beac0b68b018e8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e368023e492899f701c19ed1c11e9ce7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e368023e492899f701c19ed1c11e9ce7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48d600a7604f9a69686e9bf232a5c521\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48d600a7604f9a69686e9bf232a5c521\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\78cec0b01c3d17b124e522fd77b8536c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\78cec0b01c3d17b124e522fd77b8536c\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a22e1160783e483400676ceb4a17992b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a22e1160783e483400676ceb4a17992b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a172cad81a42d497d39d980b645a9c56\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a172cad81a42d497d39d980b645a9c56\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\24433f89ae8f54d5d1ae39fb943784c3\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\24433f89ae8f54d5d1ae39fb943784c3\transformed\jetified-viewbinding-8.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\004d897fd1a88ff65e571796e31ba0d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\004d897fd1a88ff65e571796e31ba0d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b93fe91324c1b99d544cb8fd0e3aeacb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b93fe91324c1b99d544cb8fd0e3aeacb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dda66f9b4d6a30a4519ae2fc72ba5c8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dda66f9b4d6a30a4519ae2fc72ba5c8\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6496bea76a07f2cc9fff56655fa74233\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6496bea76a07f2cc9fff56655fa74233\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef759e02d7a6017394b6578dbe631031\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef759e02d7a6017394b6578dbe631031\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dab232964e2fd4aa21d9e8733b6dab43\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dab232964e2fd4aa21d9e8733b6dab43\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4be3f0219841a1a97978e8443f4669f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4be3f0219841a1a97978e8443f4669f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\17fcbdd3c2256521c0db572b37155d11\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\17fcbdd3c2256521c0db572b37155d11\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [:flutter_image_compress_common] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\flutter_image_compress_common\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-52
	android:targetSdkVersion
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\All Projects Apps Khamsat\App 20250515\the_cut_app\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.stripe:stripe-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bef774bac08091f67bb9959ed680515\transformed\jetified-stripe-core-20.44.2\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\2bef774bac08091f67bb9959ed680515\transformed\jetified-stripe-core-20.44.2\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:connectivity_plus] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
	android:name
		ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
meta-data#com.google.android.gms.wallet.api.enabled
ADDED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-14:36
	android:value
		ADDED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
	android:name
		ADDED from [:pay_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\pay_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-69
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\All Projects Apps Khamsat\App 20250515\the_cut_app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:13:9-17:18
action#android.intent.action.VIEW
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:13-65
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:14:21-62
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.thecut.the_cut_app/cancel+data:path:/com.thecut.the_cut_app/success+data:pathPrefix:/com.thecut.the_cut_app+data:pathPrefix:/com.thecut.the_cut_app+data:pathPrefix:/com.thecut.the_cut_app/authentication_return+data:pathPrefix:/com.thecut.the_cut_app/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:25:13-64:29
category#android.intent.category.DEFAULT
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:17-76
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:28:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:17-78
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:29:27-75
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\18523a02b73041f656a862fd2bc4fa5e\transformed\jetified-financial-connections-20.44.2\AndroidManifest.xml:71:13-110
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b62df31ded6203d6ca3e8c5e3cbe173\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:8:9-12:58
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:12:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:13:9-17:58
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:17:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:15:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:16:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:14:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:18:9-22:58
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:22:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:20:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:21:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:19:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:23:9-27:58
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:27:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:26:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:24:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:29:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:33:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:36:9-39:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:39:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:37:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:40:9-43:68
	android:exported
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:43:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:41:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:44:9-47:58
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:47:13-55
	android:theme
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:46:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\b19304103023ebfaead8885b69b81460\transformed\jetified-paymentsheet-20.44.2\AndroidManifest.xml:45:13-121
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:8:9-13:61
	android:launchMode
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:12:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:10:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:11:13-115
	android:theme
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:13:13-58
	android:name
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:9:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:14:9-31:20
	android:launchMode
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:18:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:16:13-49
	android:exported
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:19:13-58
	android:name
		ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:15:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:20:13-30:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.thecut.the_cut_app+data:scheme:link-popup
ADDED from [com.stripe:link:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\d318f3a2bf1506a2482775d69cf4bab5\transformed\jetified-link-20.44.2\AndroidManifest.xml:20:13-30:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\834ec2fb3e00f66c29bead5c64ccea85\transformed\jetified-payments-ui-core-20.44.2\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.AddPaymentMethodActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:16:13-76
activity#com.stripe.android.view.PaymentMethodsActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:19:9-22:57
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:22:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:20:13-74
activity#com.stripe.android.view.PaymentFlowActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:23:9-26:57
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:26:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:24:13-71
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:27:9-31:58
	android:windowSoftInputMode
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:31:13-55
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:30:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:28:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:32:9-35:61
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:35:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:33:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:41:9-45:61
	android:launchMode
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:44:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:43:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:45:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:42:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:46:9-63:20
	android:launchMode
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:49:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:48:13-36
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:50:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:47:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:51:13-62:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.thecut.the_cut_app+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:51:13-62:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:64:9-67:57
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:66:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:67:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:65:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:68:9-71:66
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:70:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:71:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:69:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:72:9-75:66
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:74:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:75:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:73:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:76:9-79:68
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:78:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:79:13-65
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:77:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:80:9-83:61
	android:exported
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:82:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:83:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.44.2] C:\Users\<USER>\.gradle\caches\transforms-3\e48aba25dd75b0c781cfa94324700f3b\transformed\jetified-payments-core-20.44.2\AndroidManifest.xml:81:13-97
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\9034b3e6eef8f7eda6dfc506ba74b4c4\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\40dfa3abad0911287578f4f4eae4d17b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce1528c20d487084e323d6c5185fa58\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\78d07cc6b66ce6c14277923bf5e56d69\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ab6932265b895dbad4c2d8e8ef6cd15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\63e00e461a4c897579eae255cf766db5\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9375bf5b1202157bbabc2d7b1c6c06f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15bdd5c1085abfb408a9873a2860ab8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.thecut.the_cut_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4daf50f7558cf94dc7d393e450025472\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d3c9ef8bacbc7ba7c2d37a5b4c5b6a15\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\362b4ff426c5ee5cf77aa03328a1059d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\7d89dbcecd15cc1055e5308bf93ac6ef\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0864baa939715fcb2b2e8951ba4bcb46\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
