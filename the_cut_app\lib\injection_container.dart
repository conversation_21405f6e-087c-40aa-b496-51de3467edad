import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

// Features
import 'features/authentication/data/datasources/auth_remote_data_source.dart';
import 'features/authentication/data/repositories/auth_repository_impl.dart';
import 'features/authentication/domain/repositories/auth_repository.dart';
import 'features/authentication/domain/usecases/get_current_user.dart';
import 'features/authentication/domain/usecases/register_with_email_and_password.dart';
import 'features/authentication/domain/usecases/sign_in_with_email_and_password.dart';
import 'features/authentication/domain/usecases/sign_out.dart';
import 'features/authentication/domain/usecases/update_profile.dart';
import 'features/authentication/presentation/bloc/auth_bloc.dart';

// Real estate and supervision imports will be added here when needed

final sl = GetIt.instance;

/// Initialize dependency injection
Future<void> init() async {
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  sl.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);
  sl.registerLazySingleton<FirebaseFirestore>(() => FirebaseFirestore.instance);
  sl.registerLazySingleton<FirebaseStorage>(() => FirebaseStorage.instance);
  sl.registerLazySingleton<Connectivity>(() => Connectivity());

  //! Core
  // TODO: Register core dependencies

  //! Features
  // TODO: Register feature-specific dependencies

  // Authentication
  _initAuthenticationDependencies();
}

/// Initialize authentication dependencies
void _initAuthenticationDependencies() {
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      sl<FirebaseAuth>(),
      sl<FirebaseFirestore>(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl<AuthRemoteDataSource>()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetCurrentUser(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => SignInWithEmailAndPassword(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => RegisterWithEmailAndPassword(sl<AuthRepository>()));
  sl.registerLazySingleton(() => SignOut(sl<AuthRepository>()));
  sl.registerLazySingleton(() => UpdateProfile(sl<AuthRepository>()));

  // BLoC
  sl.registerFactory(
    () => AuthBloc(
      getCurrentUser: sl<GetCurrentUser>(),
      signInWithEmailAndPassword: sl<SignInWithEmailAndPassword>(),
      registerWithEmailAndPassword: sl<RegisterWithEmailAndPassword>(),
      signOut: sl<SignOut>(),
      updateProfile: sl<UpdateProfile>(),
    ),
  );
}
