import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

// Features
import 'features/authentication/data/datasources/auth_remote_data_source.dart';
import 'features/authentication/data/repositories/auth_repository_impl.dart';
import 'features/authentication/domain/repositories/auth_repository.dart';
import 'features/authentication/domain/usecases/get_current_user.dart';
import 'features/authentication/domain/usecases/register_with_email_and_password.dart';
import 'features/authentication/domain/usecases/sign_in_with_email_and_password.dart';
import 'features/authentication/domain/usecases/sign_out.dart';
import 'features/authentication/domain/usecases/update_profile.dart';
import 'features/authentication/presentation/bloc/auth_bloc.dart';

import 'features/branches/data/datasources/branch_remote_data_source.dart';
import 'features/branches/data/repositories/branch_repository_impl.dart';
import 'features/branches/domain/repositories/branch_repository.dart';
import 'features/branches/domain/usecases/create_branch.dart';
import 'features/branches/domain/usecases/delete_branch.dart';
import 'features/branches/domain/usecases/get_active_branches.dart';
import 'features/branches/domain/usecases/get_all_branches.dart';
import 'features/branches/domain/usecases/get_branch_by_id.dart';
import 'features/branches/domain/usecases/update_branch.dart';
import 'features/branches/presentation/bloc/branch_bloc.dart';

final sl = GetIt.instance;

/// Initialize dependency injection
Future<void> init() async {
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  sl.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);
  sl.registerLazySingleton<FirebaseFirestore>(() => FirebaseFirestore.instance);
  sl.registerLazySingleton<FirebaseStorage>(() => FirebaseStorage.instance);
  sl.registerLazySingleton<Connectivity>(() => Connectivity());

  //! Core
  // TODO: Register core dependencies

  //! Features
  // TODO: Register feature-specific dependencies

  // Authentication
  _initAuthenticationDependencies();

  // Branches
  _initBranchesDependencies();

  // Team
  _initTeamDependencies();

  // Tasks
  _initTasksDependencies();

  // Finance
  _initFinanceDependencies();
}

/// Initialize authentication dependencies
void _initAuthenticationDependencies() {
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      sl<FirebaseAuth>(),
      sl<FirebaseFirestore>(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl<AuthRemoteDataSource>()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetCurrentUser(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => SignInWithEmailAndPassword(sl<AuthRepository>()));
  sl.registerLazySingleton(
      () => RegisterWithEmailAndPassword(sl<AuthRepository>()));
  sl.registerLazySingleton(() => SignOut(sl<AuthRepository>()));
  sl.registerLazySingleton(() => UpdateProfile(sl<AuthRepository>()));

  // BLoC
  sl.registerFactory(
    () => AuthBloc(
      getCurrentUser: sl<GetCurrentUser>(),
      signInWithEmailAndPassword: sl<SignInWithEmailAndPassword>(),
      registerWithEmailAndPassword: sl<RegisterWithEmailAndPassword>(),
      signOut: sl<SignOut>(),
      updateProfile: sl<UpdateProfile>(),
    ),
  );
}

/// Initialize branches dependencies
void _initBranchesDependencies() {
  // Data sources
  sl.registerLazySingleton<BranchRemoteDataSource>(
    () => BranchRemoteDataSourceImpl(sl<FirebaseFirestore>()),
  );

  // Repositories
  sl.registerLazySingleton<BranchRepository>(
    () => BranchRepositoryImpl(sl<BranchRemoteDataSource>()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAllBranches(sl<BranchRepository>()));
  sl.registerLazySingleton(() => GetBranchById(sl<BranchRepository>()));
  sl.registerLazySingleton(() => CreateBranch(sl<BranchRepository>()));
  sl.registerLazySingleton(() => UpdateBranch(sl<BranchRepository>()));
  sl.registerLazySingleton(() => DeleteBranch(sl<BranchRepository>()));
  sl.registerLazySingleton(() => GetActiveBranches(sl<BranchRepository>()));

  // BLoC
  sl.registerFactory(
    () => BranchBloc(
      getAllBranches: sl<GetAllBranches>(),
      getBranchById: sl<GetBranchById>(),
      createBranch: sl<CreateBranch>(),
      updateBranch: sl<UpdateBranch>(),
      deleteBranch: sl<DeleteBranch>(),
      getActiveBranches: sl<GetActiveBranches>(),
    ),
  );
}

/// Initialize team dependencies
void _initTeamDependencies() {
  // Data sources

  // Repositories

  // Use cases

  // BLoC
}

/// Initialize tasks dependencies
void _initTasksDependencies() {
  // Data sources

  // Repositories

  // Use cases

  // BLoC
}

/// Initialize finance dependencies
void _initFinanceDependencies() {
  // Data sources

  // Repositories

  // Use cases

  // BLoC
}
