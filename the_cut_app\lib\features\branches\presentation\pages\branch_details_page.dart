import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/branch_entity.dart';
import '../bloc/branch_bloc.dart';
import '../bloc/branch_event.dart';
import '../bloc/branch_state.dart';
import 'edit_branch_page.dart';

/// Branch details page
class BranchDetailsPage extends StatefulWidget {
  final BranchEntity branch;

  const BranchDetailsPage({
    super.key,
    required this.branch,
  });

  @override
  State<BranchDetailsPage> createState() => _BranchDetailsPageState();
}

class _BranchDetailsPageState extends State<BranchDetailsPage> {
  late BranchEntity _branch;

  @override
  void initState() {
    super.initState();
    _branch = widget.branch;
    _loadBranchDetails();
  }

  void _loadBranchDetails() {
    context.read<BranchBloc>().add(
          GetBranchByIdEvent(branchId: _branch.id),
        );
  }

  void _navigateToEditBranch() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditBranchPage(branch: _branch),
      ),
    ).then((_) => _loadBranchDetails());
  }

  void _deleteBranch() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Branch'),
        content: const Text('Are you sure you want to delete this branch?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<BranchBloc>().add(
                    DeleteBranchEvent(branchId: _branch.id),
                  );
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleBranchStatus() {
    final updatedBranch = _branch.copyWith(
      isActive: !_branch.isActive,
    );

    context.read<BranchBloc>().add(
          UpdateBranchEvent(branch: updatedBranch),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Branch Details',
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: _navigateToEditBranch,
          ),
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: _deleteBranch,
          ),
        ],
      ),
      body: BlocConsumer<BranchBloc, BranchState>(
        listener: (context, state) {
          if (state is BranchErrorState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is BranchLoadedState) {
            setState(() {
              _branch = state.branch;
            });
          } else if (state is BranchUpdatedState) {
            setState(() {
              _branch = state.branch;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Branch updated successfully'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is BranchDeletedState) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Branch deleted successfully'),
                backgroundColor: AppColors.success,
              ),
            );
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          if (state is BranchLoadingState) {
            return const Center(
              child: LoadingIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Branch header
                Row(
                  children: [
                    // Branch logo or placeholder
                    Container(
                      width: 80.w,
                      height: 80.w,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: _branch.logoUrl != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(12.r),
                              child: Image.network(
                                _branch.logoUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(
                                  Icons.business,
                                  color: AppColors.primary,
                                  size: 40,
                                ),
                              ),
                            )
                          : const Icon(
                              Icons.business,
                              color: AppColors.primary,
                              size: 40,
                            ),
                    ),
                    SizedBox(width: 16.w),
                    
                    // Branch name and status
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _branch.name,
                            style: AppTextStyles.h3,
                          ),
                          SizedBox(height: 8.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 12.w,
                              vertical: 6.h,
                            ),
                            decoration: BoxDecoration(
                              color: _branch.isActive
                                  ? AppColors.success.withOpacity(0.1)
                                  : AppColors.error.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              _branch.isActive ? 'Active' : 'Inactive',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: _branch.isActive
                                    ? AppColors.success
                                    : AppColors.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                
                // Branch details
                Text(
                  'Branch Information',
                  style: AppTextStyles.h4,
                ),
                SizedBox(height: 16.h),
                
                // Address
                if (_branch.address != null) ...[
                  _buildInfoItem(
                    icon: Icons.location_on_outlined,
                    title: 'Address',
                    value: _branch.address!,
                  ),
                  SizedBox(height: 16.h),
                ],
                
                // Phone
                if (_branch.phoneNumber != null) ...[
                  _buildInfoItem(
                    icon: Icons.phone_outlined,
                    title: 'Phone Number',
                    value: _branch.phoneNumber!,
                  ),
                  SizedBox(height: 16.h),
                ],
                
                // Email
                if (_branch.email != null) ...[
                  _buildInfoItem(
                    icon: Icons.email_outlined,
                    title: 'Email',
                    value: _branch.email!,
                  ),
                  SizedBox(height: 16.h),
                ],
                
                // Manager
                if (_branch.managerName != null) ...[
                  _buildInfoItem(
                    icon: Icons.person_outlined,
                    title: 'Manager',
                    value: _branch.managerName!,
                  ),
                  SizedBox(height: 16.h),
                ],
                
                // Created at
                if (_branch.createdAt != null) ...[
                  _buildInfoItem(
                    icon: Icons.calendar_today_outlined,
                    title: 'Created At',
                    value: _branch.createdAt.toString().split(' ')[0],
                  ),
                  SizedBox(height: 16.h),
                ],
                
                // Updated at
                if (_branch.updatedAt != null) ...[
                  _buildInfoItem(
                    icon: Icons.update_outlined,
                    title: 'Last Updated',
                    value: _branch.updatedAt.toString().split(' ')[0],
                  ),
                  SizedBox(height: 32.h),
                ],
                
                // Actions
                Row(
                  children: [
                    Expanded(
                      child: CustomButton(
                        text: _branch.isActive
                            ? 'Deactivate Branch'
                            : 'Activate Branch',
                        onPressed: _toggleBranchStatus,
                        type: _branch.isActive
                            ? ButtonType.outlined
                            : ButtonType.primary,
                        size: ButtonSize.large,
                        icon: _branch.isActive
                            ? Icons.cancel_outlined
                            : Icons.check_circle_outlined,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: CustomButton(
                        text: 'Edit Branch',
                        onPressed: _navigateToEditBranch,
                        type: ButtonType.primary,
                        size: ButtonSize.large,
                        icon: Icons.edit_outlined,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                CustomButton(
                  text: 'Delete Branch',
                  onPressed: _deleteBranch,
                  type: ButtonType.outlined,
                  size: ButtonSize.large,
                  icon: Icons.delete_outlined,
                  textColor: AppColors.error,
                  borderColor: AppColors.error,
                  isFullWidth: true,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20.r,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: AppTextStyles.bodyLarge,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
