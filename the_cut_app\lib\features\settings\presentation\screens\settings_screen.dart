import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/language_service.dart';

/// Settings screen - إعدادات التطبيق الكاملة
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkMode = false;
  bool _autoBackupEnabled = true;
  bool _soundEnabled = true;

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final authService = Provider.of<AuthService>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: AppTextStyles.h4.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        elevation: 2,
        shadowColor: AppColors.primary.withOpacity(0.3),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Section
            _buildProfileSection(authService),
            SizedBox(height: 24.h),

            // Language & Display Section
            _buildSectionCard(
              title: 'اللغة والعرض',
              icon: Icons.language,
              children: [
                _buildLanguageSelector(languageService),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'الوضع الليلي',
                  subtitle: 'تفعيل المظهر الداكن',
                  icon: Icons.dark_mode,
                  value: _darkMode,
                  onChanged: (value) {
                    setState(() {
                      _darkMode = value;
                    });
                    _showFeatureComingSoon('الوضع الليلي');
                  },
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Notifications Section
            _buildSectionCard(
              title: 'الإشعارات',
              icon: Icons.notifications,
              children: [
                _buildSwitchTile(
                  title: 'إشعارات التطبيق',
                  subtitle: 'تلقي إشعارات حول المشاريع والمواعيد',
                  icon: Icons.notifications_active,
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    _showFeatureComingSoon('الإشعارات');
                  },
                ),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'النسخ الاحتياطي التلقائي',
                  subtitle: 'حفظ البيانات تلقائياً',
                  icon: Icons.backup,
                  value: _autoBackupEnabled,
                  onChanged: (value) {
                    setState(() {
                      _autoBackupEnabled = value;
                    });
                    _showFeatureComingSoon('النسخ الاحتياطي');
                  },
                ),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'الأصوات',
                  subtitle: 'تفعيل أصوات التطبيق',
                  icon: Icons.volume_up,
                  value: _soundEnabled,
                  onChanged: (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                    _showFeatureComingSoon('الأصوات');
                  },
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // App Info Section
            _buildSectionCard(
              title: 'معلومات التطبيق',
              icon: Icons.info,
              children: [
                _buildInfoTile(
                  title: 'إصدار التطبيق',
                  value: '1.0.0',
                  icon: Icons.app_settings_alt,
                ),
                _buildDivider(),
                _buildInfoTile(
                  title: 'تاريخ آخر تحديث',
                  value: '2024-01-15',
                  icon: Icons.update,
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'شروط الاستخدام',
                  icon: Icons.description,
                  onTap: () => _showTermsDialog(),
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'سياسة الخصوصية',
                  icon: Icons.privacy_tip,
                  onTap: () => _showPrivacyDialog(),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Support Section
            _buildSectionCard(
              title: 'الدعم والمساعدة',
              icon: Icons.support,
              children: [
                _buildActionTile(
                  title: 'مركز المساعدة',
                  icon: Icons.help_center,
                  onTap: () => _showHelpCenter(),
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'تواصل معنا',
                  icon: Icons.contact_support,
                  onTap: () => _showContactDialog(),
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'تقييم التطبيق',
                  icon: Icons.star_rate,
                  onTap: () => _showRatingDialog(),
                ),
              ],
            ),
            SizedBox(height: 24.h),

            // Logout Button
            _buildLogoutButton(authService),
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(AuthService authService) {
    final user = authService.currentUser;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primaryLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40.r,
            backgroundColor: Colors.white,
            child: Icon(
              Icons.person,
              size: 40.w,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            user?.name ?? 'مستخدم',
            style: AppTextStyles.h5.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            user?.email ?? '<EMAIL>',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: () => _showFeatureComingSoon('تعديل الملف الشخصي'),
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('تعديل الملف الشخصي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shadowColor: AppColors.primary.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primary,
                    size: 20.w,
                  ),
                ),
                SizedBox(width: 12.w),
                Text(
                  title,
                  style: AppTextStyles.h5.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector(LanguageService languageService) {
    return ListTile(
      leading: Icon(Icons.language, color: AppColors.primary),
      title: const Text('اللغة'),
      subtitle: Text(languageService.currentLocale.languageCode == 'ar' ? 'العربية' : 'English'),
      trailing: Icon(Icons.arrow_forward_ios, size: 16.w),
      onTap: () => _showLanguageDialog(languageService),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primary),
      title: Text(title),
      trailing: Text(
        value,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primary),
      title: Text(title),
      trailing: Icon(Icons.arrow_forward_ios, size: 16.w),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: AppColors.border,
    );
  }

  Widget _buildLogoutButton(AuthService authService) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _showLogoutDialog(authService),
        icon: const Icon(Icons.logout),
        label: const Text('تسجيل الخروج'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
      ),
    );
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showLanguageDialog(LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('العربية'),
              leading: Radio<String>(
                value: 'ar',
                groupValue: languageService.currentLocale.languageCode,
                onChanged: (value) {
                  // languageService.setLocale(const Locale('ar'));
                  _showFeatureComingSoon('تغيير اللغة');
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('English'),
              leading: Radio<String>(
                value: 'en',
                groupValue: languageService.currentLocale.languageCode,
                onChanged: (value) {
                  // languageService.setLocale(const Locale('en'));
                  _showFeatureComingSoon('Language Change');
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // authService.logout();
              _showFeatureComingSoon('تسجيل الخروج');
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showTermsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شروط الاستخدام'),
        content: const SingleChildScrollView(
          child: Text(
            'شروط الاستخدام لتطبيق إدارة مشاريع البناء الكويتي:\n\n'
            '1. يجب استخدام التطبيق وفقاً للقوانين الكويتية\n'
            '2. المحافظة على سرية البيانات\n'
            '3. عدم مشاركة معلومات المشاريع مع أطراف خارجية\n'
            '4. الالتزام بمعايير البناء الكويتية\n'
            '5. تحديث البيانات بانتظام\n\n'
            'للمزيد من التفاصيل، يرجى زيارة موقعنا الإلكتروني.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سياسة الخصوصية'),
        content: const SingleChildScrollView(
          child: Text(
            'سياسة الخصوصية لتطبيق إدارة مشاريع البناء الكويتي:\n\n'
            '• نحن نحترم خصوصيتك ونحمي بياناتك الشخصية\n'
            '• لا نشارك معلوماتك مع أطراف ثالثة\n'
            '• نستخدم التشفير لحماية البيانات\n'
            '• يمكنك طلب حذف بياناتك في أي وقت\n'
            '• نلتزم بقوانين حماية البيانات الكويتية\n\n'
            'لأي استفسارات، تواصل معنا عبر البريد الإلكتروني.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showHelpCenter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مركز المساعدة'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الأسئلة الشائعة:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• كيف أضيف مشروع جديد؟'),
              Text('• كيف أرفع الملفات؟'),
              Text('• كيف أدير المواعيد؟'),
              Text('• كيف أتواصل مع المشرفين؟'),
              SizedBox(height: 16),
              Text('للمساعدة الفورية:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('📞 الهاتف: +965 1234 5678'),
              Text('📧 البريد: <EMAIL>'),
              Text('💬 الدردشة: متاحة 24/7'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تواصل معنا'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              leading: Icon(Icons.phone),
              title: Text('الهاتف'),
              subtitle: Text('+965 1234 5678'),
            ),
            ListTile(
              leading: Icon(Icons.email),
              title: Text('البريد الإلكتروني'),
              subtitle: Text('<EMAIL>'),
            ),
            ListTile(
              leading: Icon(Icons.location_on),
              title: Text('العنوان'),
              subtitle: Text('الكويت - مدينة الكويت'),
            ),
            ListTile(
              leading: Icon(Icons.schedule),
              title: Text('ساعات العمل'),
              subtitle: Text('الأحد - الخميس: 8:00 - 17:00'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقييم التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ما رأيك في التطبيق؟'),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
              ],
            ),
            SizedBox(height: 16),
            Text('شكراً لك على استخدام التطبيق!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('شكراً لتقييمك!')),
              );
            },
            child: const Text('إرسال التقييم'),
          ),
        ],
      ),
    );
  }
}
