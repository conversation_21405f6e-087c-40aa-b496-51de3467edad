import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/services/font_service.dart';
import '../../../../core/services/language_service.dart';

/// Settings screen
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkMode = false;

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final authService = Provider.of<AuthService>(context);
    final currentUser = authService.currentUser;
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('settings')),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account section
            _buildSectionTitle(localizations.translate('account_settings')),
            SizedBox(height: 8.h),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  // Profile
                  _buildSettingItem(
                    icon: Icons.person_outline,
                    title: localizations.translate('profile'),
                    onTap: () {
                      Navigator.pushNamed(context, '/profile');
                    },
                  ),

                  _buildDivider(),

                  // Logout
                  _buildSettingItem(
                    icon: Icons.logout,
                    title: localizations.translate('logout'),
                    onTap: () {
                      _showLogoutConfirmationDialog();
                    },
                    iconColor: AppColors.error,
                    textColor: AppColors.error,
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Appearance section
            _buildSectionTitle(localizations.translate('appearance_settings')),
            SizedBox(height: 8.h),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  // Language
                  _buildSettingItem(
                    icon: Icons.language,
                    title: localizations.translate('language'),
                    subtitle: languageService.getLanguageName(
                      languageService.currentLocale.languageCode,
                    ),
                    onTap: () {
                      _showLanguageSelectionDialog();
                    },
                  ),

                  _buildDivider(),

                  // Dark mode
                  _buildSwitchSettingItem(
                    icon: Icons.dark_mode_outlined,
                    title: localizations.translate('dark_mode'),
                    value: _darkMode,
                    onChanged: (value) {
                      setState(() {
                        _darkMode = value;
                      });
                      // TODO: Implement dark mode
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            localizations.translate('coming_soon'),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Notifications section
            _buildSectionTitle(
                localizations.translate('notification_settings')),
            SizedBox(height: 8.h),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  // Enable notifications
                  _buildSwitchSettingItem(
                    icon: Icons.notifications_outlined,
                    title: localizations.translate('enable_notifications'),
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _notificationsEnabled = value;
                      });
                      // TODO: Implement notifications
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            localizations.translate('coming_soon'),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Subscription section
            _buildSectionTitle(localizations.translate('subscription')),
            SizedBox(height: 8.h),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  // Subscription plans
                  _buildSettingItem(
                    icon: Icons.card_membership,
                    title: localizations.translate('subscription_plans'),
                    onTap: () {
                      Navigator.pushNamed(context, '/subscription_plans');
                    },
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // About section
            _buildSectionTitle(localizations.translate('app_info')),
            SizedBox(height: 8.h),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  // About
                  _buildSettingItem(
                    icon: Icons.info_outline,
                    title: localizations.translate('about'),
                    onTap: () {
                      _showAboutDialog();
                    },
                  ),

                  _buildDivider(),

                  // Contact us
                  _buildSettingItem(
                    icon: Icons.support_agent_outlined,
                    title: localizations.translate('contact_us'),
                    onTap: () {
                      // TODO: Implement contact us
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            localizations.translate('coming_soon'),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Version info
            Center(
              child: Text(
                '${localizations.translate('version')} 1.0.0',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(height: 8.h),

            // Copyright
            Center(
              child: Text(
                '© 2025 The Cut. ${localizations.translate('all_rights_reserved')}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: Text(
        title,
        style: AppTextStyles.h4,
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? AppColors.primary,
        size: 24.w,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          color: textColor ?? AppColors.textPrimary,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            )
          : null,
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.textSecondary,
        size: 16.w,
      ),
      onTap: onTap,
    );
  }

  Widget _buildSwitchSettingItem({
    required IconData icon,
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      secondary: Icon(
        icon,
        color: AppColors.primary,
        size: 24.w,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium,
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: AppColors.border,
      indent: 16.w,
      endIndent: 16.w,
    );
  }

  void _showLogoutConfirmationDialog() {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.translate('logout')),
        content: Text(localizations.translate('are_you_sure')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.translate('cancel')),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<AuthService>(context, listen: false).signOut();
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false,
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: Text(localizations.translate('logout')),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelectionDialog() {
    final localizations = AppLocalizations.of(context);
    final languageService =
        Provider.of<LanguageService>(context, listen: false);
    final fontService = Provider.of<FontService>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.translate('change_language')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('English'),
              leading: Radio<String>(
                value: 'en',
                groupValue: languageService.currentLocale.languageCode,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    languageService.setLanguage(Locale(value),
                        fontService: fontService);
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('العربية'),
              leading: Radio<String>(
                value: 'ar',
                groupValue: languageService.currentLocale.languageCode,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    languageService.setLanguage(Locale(value),
                        fontService: fontService);
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.translate('cancel')),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.translate('about')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'The Cut',
              style: AppTextStyles.h3,
            ),
            SizedBox(height: 8.h),
            Text(
              '${localizations.translate('version')}: 1.0.0',
              style: AppTextStyles.bodyMedium,
            ),
            SizedBox(height: 16.h),
            Text(
              'The Cut is a comprehensive salon management application designed to streamline operations for salon businesses.',
              style: AppTextStyles.bodyMedium,
            ),
            SizedBox(height: 16.h),
            Text(
              '© 2025 The Cut. ${localizations.translate('all_rights_reserved')}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.translate('close')),
          ),
        ],
      ),
    );
  }
}
