import 'package:equatable/equatable.dart';

/// Task status
enum TaskStatus {
  /// Task is pending
  pending,

  /// Task is in progress
  inProgress,

  /// Task is completed
  completed,

  /// Task is cancelled
  cancelled,
}

/// Task priority
enum TaskPriority {
  /// Task has low priority
  low,

  /// Task has medium priority
  medium,

  /// Task has high priority
  high,
}

/// Task entity
class TaskEntity extends Equatable {
  /// Task ID
  final String id;

  /// Task title
  final String title;

  /// Task description
  final String? description;

  /// Task status
  final TaskStatus status;

  /// Task priority
  final TaskPriority priority;

  /// Task due date
  final DateTime? dueDate;

  /// Task assigned to user ID
  final String? assignedToId;

  /// Task assigned to user name
  final String? assignedToName;

  /// Task created by user ID
  final String createdById;

  /// Task created by user name
  final String createdByName;

  /// Task branch ID
  final String? branchId;

  /// Task branch name
  final String? branchName;

  /// Task created at
  final DateTime createdAt;

  /// Task updated at
  final DateTime? updatedAt;

  /// Task completed at
  final DateTime? completedAt;

  /// Task attachments
  final List<String>? attachments;

  /// Task comments
  final List<TaskCommentEntity>? comments;

  /// Constructor
  const TaskEntity({
    required this.id,
    required this.title,
    this.description,
    this.status = TaskStatus.pending,
    this.priority = TaskPriority.medium,
    this.dueDate,
    this.assignedToId,
    this.assignedToName,
    required this.createdById,
    required this.createdByName,
    this.branchId,
    this.branchName,
    required this.createdAt,
    this.updatedAt,
    this.completedAt,
    this.attachments,
    this.comments,
  });

  /// Copy with
  TaskEntity copyWith({
    String? id,
    String? title,
    String? description,
    TaskStatus? status,
    TaskPriority? priority,
    DateTime? dueDate,
    String? assignedToId,
    String? assignedToName,
    String? createdById,
    String? createdByName,
    String? branchId,
    String? branchName,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    List<String>? attachments,
    List<TaskCommentEntity>? comments,
  }) {
    return TaskEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      dueDate: dueDate ?? this.dueDate,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      branchId: branchId ?? this.branchId,
      branchName: branchName ?? this.branchName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        status,
        priority,
        dueDate,
        assignedToId,
        assignedToName,
        createdById,
        createdByName,
        branchId,
        branchName,
        createdAt,
        updatedAt,
        completedAt,
        attachments,
        comments,
      ];
}

/// Task comment entity
class TaskCommentEntity extends Equatable {
  /// Comment ID
  final String id;

  /// Comment text
  final String text;

  /// Comment created by user ID
  final String createdById;

  /// Comment created by user name
  final String createdByName;

  /// Comment created at
  final DateTime createdAt;

  /// Constructor
  const TaskCommentEntity({
    required this.id,
    required this.text,
    required this.createdById,
    required this.createdByName,
    required this.createdAt,
  });

  /// Copy with
  TaskCommentEntity copyWith({
    String? id,
    String? text,
    String? createdById,
    String? createdByName,
    DateTime? createdAt,
  }) {
    return TaskCommentEntity(
      id: id ?? this.id,
      text: text ?? this.text,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        text,
        createdById,
        createdByName,
        createdAt,
      ];
}
