import 'package:equatable/equatable.dart';

/// Subscription plan entity
class SubscriptionPlan extends Equatable {
  /// Unique identifier for the subscription plan
  final String id;

  /// Name of the subscription plan
  final String name;

  /// Description of the subscription plan
  final String description;

  /// Price of the subscription plan in the base currency (USD)
  final double price;

  /// Currency code (e.g., USD, EUR, SAR)
  final String currencyCode;

  /// Duration in months
  final int durationMonths;

  /// Features included in this subscription plan
  final List<String> features;

  /// Whether this is a popular plan
  final bool isPopular;

  /// Product ID for in-app purchase
  final String productId;

  /// Discount percentage (0-100)
  final double discountPercentage;

  /// Original price before discount
  final double originalPrice;

  /// Whether this plan has a free trial
  final bool hasFreeTrial;

  /// Free trial duration in days
  final int freeTrialDays;

  /// Maximum number of branches allowed
  final int maxBranches;

  /// Maximum number of team members allowed
  final int maxTeamMembers;

  /// Maximum number of clients allowed
  final int maxClients;

  /// Whether this plan includes financial reports
  final bool includesFinancialReports;

  /// Whether this plan includes advanced analytics
  final bool includesAdvancedAnalytics;

  /// Whether this plan includes priority support
  final bool includesPrioritySupport;

  /// Whether this plan includes custom branding
  final bool includesCustomBranding;

  /// Whether this plan includes API access
  final bool includesApiAccess;

  /// Whether this plan includes offline mode
  final bool includesOfflineMode;

  /// Whether this plan includes data export
  final bool includesDataExport;

  /// Whether this plan includes multi-device sync
  final bool includesMultiDeviceSync;

  /// Whether this plan includes appointment scheduling
  final bool includesAppointmentScheduling;

  /// Whether this plan includes client management
  final bool includesClientManagement;

  /// Whether this plan includes inventory management
  final bool includesInventoryManagement;

  /// Whether this plan includes marketing tools
  final bool includesMarketingTools;

  /// Whether this plan includes SMS notifications
  final bool includesSmsNotifications;

  /// Whether this plan includes email marketing
  final bool includesEmailMarketing;

  /// Whether this plan includes website integration
  final bool includesWebsiteIntegration;

  /// Whether this plan includes online booking
  final bool includesOnlineBooking;

  /// Whether this plan includes POS integration
  final bool includesPosIntegration;

  /// Whether this plan includes loyalty program
  final bool includesLoyaltyProgram;

  /// Whether this plan includes gift cards
  final bool includesGiftCards;

  /// Whether this plan includes customer feedback
  final bool includesCustomerFeedback;

  /// Whether this plan includes staff management
  final bool includesStaffManagement;

  /// Whether this plan includes payroll management
  final bool includesPayrollManagement;

  /// Whether this plan includes commission tracking
  final bool includesCommissionTracking;

  /// Whether this plan includes expense tracking
  final bool includesExpenseTracking;

  /// Whether this plan includes tax reporting
  final bool includesTaxReporting;

  /// Whether this plan includes multi-location support
  final bool includesMultiLocationSupport;

  /// Whether this plan includes custom roles and permissions
  final bool includesCustomRolesPermissions;

  /// Whether this plan includes white-label mobile app
  final bool includesWhiteLabelApp;

  /// Constructor
  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currencyCode,
    required this.durationMonths,
    required this.features,
    this.isPopular = false,
    required this.productId,
    this.discountPercentage = 0,
    required this.originalPrice,
    this.hasFreeTrial = false,
    this.freeTrialDays = 0,
    required this.maxBranches,
    required this.maxTeamMembers,
    required this.maxClients,
    this.includesFinancialReports = false,
    this.includesAdvancedAnalytics = false,
    this.includesPrioritySupport = false,
    this.includesCustomBranding = false,
    this.includesApiAccess = false,
    this.includesOfflineMode = false,
    this.includesDataExport = false,
    this.includesMultiDeviceSync = false,
    this.includesAppointmentScheduling = false,
    this.includesClientManagement = false,
    this.includesInventoryManagement = false,
    this.includesMarketingTools = false,
    this.includesSmsNotifications = false,
    this.includesEmailMarketing = false,
    this.includesWebsiteIntegration = false,
    this.includesOnlineBooking = false,
    this.includesPosIntegration = false,
    this.includesLoyaltyProgram = false,
    this.includesGiftCards = false,
    this.includesCustomerFeedback = false,
    this.includesStaffManagement = false,
    this.includesPayrollManagement = false,
    this.includesCommissionTracking = false,
    this.includesExpenseTracking = false,
    this.includesTaxReporting = false,
    this.includesMultiLocationSupport = false,
    this.includesCustomRolesPermissions = false,
    this.includesWhiteLabelApp = false,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        currencyCode,
        durationMonths,
        features,
        isPopular,
        productId,
        discountPercentage,
        originalPrice,
        hasFreeTrial,
        freeTrialDays,
        maxBranches,
        maxTeamMembers,
        maxClients,
      ];
}
