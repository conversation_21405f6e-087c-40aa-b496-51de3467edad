import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';
import '../repositories/branch_repository.dart';

/// Create branch use case
class CreateBranch {
  final BranchRepository _repository;

  CreateBranch(this._repository);

  /// Call the use case
  Future<Either<Failure, BranchEntity>> call(Params params) {
    return _repository.createBranch(params.branch);
  }
}

/// Create branch parameters
class Params extends Equatable {
  final BranchEntity branch;

  const Params({
    required this.branch,
  });

  @override
  List<Object?> get props => [branch];
}
