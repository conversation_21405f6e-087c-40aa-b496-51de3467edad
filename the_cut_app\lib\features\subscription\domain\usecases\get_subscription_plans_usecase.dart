import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/subscription_plan.dart';
import '../repositories/subscription_repository.dart';

/// Use case to get all available subscription plans
class GetSubscriptionPlansUseCase implements UseCase<List<SubscriptionPlan>, NoParams> {
  final SubscriptionRepository repository;

  /// Constructor
  GetSubscriptionPlansUseCase(this.repository);

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> call(NoParams params) {
    return repository.getSubscriptionPlans();
  }
}
