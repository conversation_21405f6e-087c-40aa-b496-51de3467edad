import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/subscriber.dart';
import '../repositories/subscription_repository.dart';

/// Use case to subscribe to a plan
class SubscribeToPlanUseCase implements UseCase<Subscriber, SubscribeParams> {
  final SubscriptionRepository repository;

  /// Constructor
  SubscribeToPlanUseCase(this.repository);

  @override
  Future<Either<Failure, Subscriber>> call(SubscribeParams params) {
    return repository.subscribeToPlan(
      userId: params.userId,
      subscriptionPlanId: params.subscriptionPlanId,
      paymentMethod: params.paymentMethod,
      countryCode: params.countryCode,
      currencyCode: params.currencyCode,
      cardToken: params.cardToken,
      cardLast4: params.cardLast4,
      cardBrand: params.cardBrand,
      cardExpiryMonth: params.cardExpiryMonth,
      cardExpiryYear: params.cardExpiryYear,
      receiptData: params.receiptData,
    );
  }
}

/// Parameters for SubscribeToPlanUseCase
class SubscribeParams extends Equatable {
  /// User ID
  final String userId;

  /// Subscription plan ID
  final String subscriptionPlanId;

  /// Payment method
  final String paymentMethod;

  /// Country code
  final String countryCode;

  /// Currency code
  final String currencyCode;

  /// Card token (for card payments)
  final String? cardToken;

  /// Last 4 digits of the card
  final String? cardLast4;

  /// Card brand
  final String? cardBrand;

  /// Card expiry month
  final int? cardExpiryMonth;

  /// Card expiry year
  final int? cardExpiryYear;

  /// Receipt data (for App Store or Play Store purchases)
  final String? receiptData;

  /// Constructor
  const SubscribeParams({
    required this.userId,
    required this.subscriptionPlanId,
    required this.paymentMethod,
    required this.countryCode,
    required this.currencyCode,
    this.cardToken,
    this.cardLast4,
    this.cardBrand,
    this.cardExpiryMonth,
    this.cardExpiryYear,
    this.receiptData,
  });

  @override
  List<Object?> get props => [
        userId,
        subscriptionPlanId,
        paymentMethod,
        countryCode,
        currencyCode,
        cardToken,
        cardLast4,
        cardBrand,
        cardExpiryMonth,
        cardExpiryYear,
        receiptData,
      ];
}
