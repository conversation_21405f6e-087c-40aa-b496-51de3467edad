{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "48,49,71,72,74,84,85,142,143,145,146,149,150,152,431,432,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,7404,7504,7687,8581,8660,13304,13396,13561,13632,13931,14012,14184,42969,43047,43116", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4657,4745,7499,7599,7769,8655,8747,13391,13478,13627,13695,14007,14092,14255,43042,43111,43233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78d07cc6b66ce6c14277923bf5e56d69\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,454,529", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "140,246,313,381,449,524,592"}, "to": {"startLines": "154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14361,14451,14557,14624,14692,14760,14835", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "14446,14552,14619,14687,14755,14830,14898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4825,4931,5111,5241,5350,5521,5654,5775,6053,6231,6343,6528,6664,6824,7003,7076,7143", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4926,5106,5236,5345,5516,5649,5770,5883,6226,6338,6523,6659,6819,6998,7071,7138,7222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,213", "endColumns": "74,82,76", "endOffsets": "125,208,285"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4750,7604,7937", "endColumns": "74,82,76", "endOffsets": "4820,7682,8009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5888", "endColumns": "164", "endOffsets": "6048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,14260", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,14356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,14097", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,14179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,201,268,353,424,485,557,630,692,764,834,902,962,1036,1112,1183,1246,1311,1376,1461,1542,1633,1730,1859,1941,1992,2040,2132,2196,2271,2342,2462,2551,2663", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "115,196,263,348,419,480,552,625,687,759,829,897,957,1031,1107,1178,1241,1306,1371,1456,1537,1628,1725,1854,1936,1987,2035,2127,2191,2266,2337,2457,2546,2658,2770"}, "to": {"startLines": "175,178,181,183,184,185,192,193,195,196,197,198,199,200,201,203,204,207,218,219,231,233,234,262,263,273,283,284,286,293,294,304,305,313,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15998,16247,16519,16669,16754,16825,17330,17402,17546,17608,17680,17750,17818,17878,17952,18086,18157,18406,19321,19386,20455,20621,20712,24795,24924,26002,26743,26791,26938,27764,27839,28712,28832,29642,29754", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "16058,16323,16581,16749,16820,16881,17397,17470,17603,17675,17745,17813,17873,17947,18023,18152,18215,18466,19381,19466,20531,20707,20804,24919,25001,26048,26786,26878,26997,27834,27905,28827,28916,29749,29861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,428,475,547,651,726,1003,1066,1161,1235,1290,1354,1675,1742,1809,1864,1919,2013,2163,2276,2335,2422,2510,2598,2669,2759,3061,3142,3221,3298,3360,3422,3486,3612,3711,3829,3932,4006,4085,4176,4374,4583,4713,4775,5576,5641", "endColumns": "162,209,46,71,103,74,276,62,94,73,54,63,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,125,98,117,102,73,78,90,197,208,129,61,800,64,57", "endOffsets": "213,423,470,542,646,721,998,1061,1156,1230,1285,1349,1670,1737,1804,1859,1914,2008,2158,2271,2330,2417,2505,2593,2664,2754,3056,3137,3216,3293,3355,3417,3481,3607,3706,3824,3927,4001,4080,4171,4369,4578,4708,4770,5571,5636,5694"}, "to": {"startLines": "236,237,241,242,243,244,245,246,247,261,264,266,272,277,278,285,295,299,300,301,302,303,309,312,317,319,320,321,322,324,326,327,330,335,344,360,361,362,363,364,376,382,383,384,386,387,402", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20931,21094,22215,22262,22334,22438,22513,22790,22853,24721,25006,25213,25681,26321,26388,26883,27910,28209,28303,28453,28566,28625,29248,29554,30045,30196,30286,30588,30669,30814,31015,31077,31344,32041,33272,34791,34909,35012,35086,35165,36340,36967,37176,37306,37437,38238,39999", "endColumns": "162,209,46,71,103,74,276,62,94,73,54,63,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,125,98,117,102,73,78,90,197,208,129,61,800,64,57", "endOffsets": "21089,21299,22257,22329,22433,22508,22785,22848,22943,24790,25056,25272,25997,26383,26450,26933,27960,28298,28448,28561,28620,28707,29331,29637,30111,30281,30583,30664,30743,30886,31072,31134,31403,32162,33366,34904,35007,35081,35160,35251,36533,37171,37301,37363,38233,38298,40052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,228,290,366,451,512,571,649,727,815,901,1004,1107,1195,1278,1360,1450,1554,1649,1719,1811,1900,1997,2122,2204,2296,2373,2472,2548,2648,2748,2843,2928,3043,3118,3203,3325,3408,3473,4236,4907,4986,5100,5211,5266,5377,5489,5557,5658,5758,5814,5902,5952,6035,6153,6229,6306,6354,6442,6545,6593,6642,6728,6786,6850,7051,7247,7404,7472,7561,7647,7755,7863,7973,8056,8152,8232,8334,8437,8491,8636,8688,8770,8839,8909,8985,9055,9129,9227,9307", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,47,87,102,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,101,102,53,144,51,81,68,69,75,69,73,97,79,78", "endOffsets": "132,223,285,361,446,507,566,644,722,810,896,999,1102,1190,1273,1355,1445,1549,1644,1714,1806,1895,1992,2117,2199,2291,2368,2467,2543,2643,2743,2838,2923,3038,3113,3198,3320,3403,3468,4231,4902,4981,5095,5206,5261,5372,5484,5552,5653,5753,5809,5897,5947,6030,6148,6224,6301,6349,6437,6540,6588,6637,6723,6781,6845,7046,7242,7399,7467,7556,7642,7750,7858,7968,8051,8147,8227,8329,8432,8486,8631,8683,8765,8834,8904,8980,9050,9124,9222,9302,9381"}, "to": {"startLines": "161,162,163,164,165,166,167,171,172,173,174,177,179,180,182,187,191,206,209,210,211,213,214,215,217,221,222,223,224,225,226,227,228,229,230,232,235,238,239,240,249,250,251,252,253,254,255,256,257,258,259,260,267,268,269,270,271,274,279,280,281,282,287,288,289,290,291,292,296,297,307,308,310,311,315,316,318,328,329,378,379,380,381,385,396,397,398,399,400,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14903,14985,15076,15138,15214,15299,15360,15668,15746,15824,15912,16144,16328,16431,16586,16946,17240,18302,18544,18639,18709,18868,18957,19054,19239,19541,19633,19710,19809,19885,19985,20085,20180,20265,20380,20536,20809,21304,21387,21452,23055,23726,23805,23919,24030,24085,24196,24308,24376,24477,24577,24633,25277,25327,25410,25528,25604,26053,26455,26543,26646,26694,27002,27088,27146,27210,27411,27607,27965,28033,29054,29140,29336,29444,29866,29949,30116,31139,31241,36634,36688,36833,36885,37368,39531,39601,39677,39747,39821,39919,41374", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,47,87,102,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,101,102,53,144,51,81,68,69,75,69,73,97,79,78", "endOffsets": "14980,15071,15133,15209,15294,15355,15414,15741,15819,15907,15993,16242,16426,16514,16664,17023,17325,18401,18634,18704,18796,18952,19049,19174,19316,19628,19705,19804,19880,19980,20080,20175,20260,20375,20450,20616,20926,21382,21447,22210,23721,23800,23914,24025,24080,24191,24303,24371,24472,24572,24628,24716,25322,25405,25523,25599,25676,26096,26538,26641,26689,26738,27083,27141,27205,27406,27602,27759,28028,28117,29135,29243,29439,29549,29944,30040,30191,31236,31339,36683,36828,36880,36962,37432,39596,39672,39742,39816,39914,39994,41448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,196,258,340,408,479,537,619,692,759,819", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "131,191,253,335,403,474,532,614,687,754,814,884"}, "to": {"startLines": "176,186,188,189,190,194,202,205,208,212,216,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16063,16886,17028,17090,17172,17475,18028,18220,18471,18801,19179,19471", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "16139,16941,17085,17167,17235,17541,18081,18297,18539,18863,19234,19536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "82,436", "startColumns": "4,4", "startOffsets": "8437,43404", "endColumns": "60,78", "endOffsets": "8493,43478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,249,401,517,621,745,916,1115,1288,1378,1488,1590,1692,1885,2007,2109,2372,2483,2582,2643,2709,2794,2896,3001,3254,3324,3399,3492,3559,3643,3714,3783,3903,4000,4103,4191,4262,4327,4442,4559,4666,4808,4915,4987,5067,5168,5244,5369,5526", "endColumns": "86,106,151,115,103,123,170,198,172,89,109,101,101,192,121,101,262,110,98,60,65,84,101,104,252,69,74,92,66,83,70,68,119,96,102,87,70,64,114,116,106,141,106,71,79,100,75,124,156,85", "endOffsets": "137,244,396,512,616,740,911,1110,1283,1373,1483,1585,1687,1880,2002,2104,2367,2478,2577,2638,2704,2789,2891,2996,3249,3319,3394,3487,3554,3638,3709,3778,3898,3995,4098,4186,4257,4322,4437,4554,4661,4803,4910,4982,5062,5163,5239,5364,5521,5607"}, "to": {"startLines": "169,248,265,275,276,325,331,332,333,334,336,337,338,339,340,341,342,343,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,365,366,367,368,369,370,371,372,373,374,375,395,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15498,22948,25061,26101,26217,30891,31408,31579,31778,31951,32167,32277,32379,32481,32674,32796,32898,33161,33371,33470,33531,33597,33682,33784,33889,34142,34212,34287,34380,34447,34531,34602,34671,35256,35353,35456,35544,35615,35680,35795,35912,36019,36161,36268,39451,40057,40158,40234,40359,40516", "endColumns": "86,106,151,115,103,123,170,198,172,89,109,101,101,192,121,101,262,110,98,60,65,84,101,104,252,69,74,92,66,83,70,68,119,96,102,87,70,64,114,116,106,141,106,71,79,100,75,124,156,85", "endOffsets": "15580,23050,25208,26212,26316,31010,31574,31773,31946,32036,32272,32374,32476,32669,32791,32893,33156,33267,33465,33526,33592,33677,33779,33884,34137,34207,34282,34375,34442,34526,34597,34666,34786,35348,35451,35539,35610,35675,35790,35907,36014,36156,36263,36335,39526,40153,40229,40354,40511,40597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7297,8111,8213,8332", "endColumns": "106,101,118,104", "endOffsets": "7399,8208,8327,8432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,7774,8014,8498,8752,8818,8920,8985,9060,9116,9195,9255,9309,9431,9490,9552,9606,9688,9823,9915,9999,10143,10222,10303,10444,10537,10616,10671,10722,10788,10868,10949,11052,11132,11205,11283,11356,11428,11540,11633,11705,11797,11889,11963,12047,12139,12196,12280,12346,12429,12516,12578,12642,12705,12783,12885,12989,13086,13190,13249,13700", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,7835,8106,8576,8813,8915,8980,9055,9111,9190,9250,9304,9426,9485,9547,9601,9683,9818,9910,9994,10138,10217,10298,10439,10532,10611,10666,10717,10783,10863,10944,11047,11127,11200,11278,11351,11423,11535,11628,11700,11792,11884,11958,12042,12134,12191,12275,12341,12424,12511,12573,12637,12700,12778,12880,12984,13081,13185,13244,13299,13784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "28122", "endColumns": "86", "endOffsets": "28204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "69,76,144,148,430,434,435", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7227,7840,13483,13789,42800,43238,43324", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "7292,7932,13556,13926,42964,43319,43399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,217,350,416,512,574,632,711,835,1015,1326,1660,1748,1818,1910,2007,2138,2280,2360,2432,2596,2665,2726,2802,2896,3052,3173,3294,3386,3462,3544,3621", "endColumns": "78,82,132,65,95,61,57,78,123,179,310,333,87,69,91,96,130,141,79,71,163,68,60,75,93,155,120,120,91,75,81,76,157", "endOffsets": "129,212,345,411,507,569,627,706,830,1010,1321,1655,1743,1813,1905,2002,2133,2275,2355,2427,2591,2660,2721,2797,2891,3047,3168,3289,3381,3457,3539,3616,3774"}, "to": {"startLines": "168,170,306,323,377,388,389,390,391,392,393,394,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15419,15585,28921,30748,36538,38303,38365,38423,38502,38626,38806,39117,40602,40690,40760,40852,40949,41080,41222,41302,41453,41617,41686,41747,41823,41917,42073,42194,42315,42407,42483,42565,42642", "endColumns": "78,82,132,65,95,61,57,78,123,179,310,333,87,69,91,96,130,141,79,71,163,68,60,75,93,155,120,120,91,75,81,76,157", "endOffsets": "15493,15663,29049,30809,36629,38360,38418,38497,38621,38801,39112,39446,40685,40755,40847,40944,41075,41217,41297,41369,41612,41681,41742,41818,41912,42068,42189,42310,42402,42478,42560,42637,42795"}}]}]}