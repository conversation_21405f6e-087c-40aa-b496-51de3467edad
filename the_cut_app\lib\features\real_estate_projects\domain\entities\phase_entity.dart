import 'package:equatable/equatable.dart';
import 'project_entity.dart';

/// نوع القسم في المرحلة
enum PhaseSection {
  plans,        // مخططات
  licenses,     // تراخيص
  schedules,    // مواعيد
  documents,    // مستندات
  images,       // صور
  videos,       // فيديوهات
  reports,      // تقارير
  notes,        // ملاحظات
}

/// حالة المرحلة
enum PhaseStatus {
  notStarted,   // لم تبدأ
  inProgress,   // قيد التنفيذ
  completed,    // مكتملة
  onHold,       // متوقفة
}

/// كيان المرحلة
class PhaseEntity extends Equatable {
  /// معرف المرحلة
  final String id;

  /// معرف المشروع
  final String projectId;

  /// نوع المرحلة
  final ConstructionPhase phaseType;

  /// اسم المرحلة
  final String name;

  /// وصف المرحلة
  final String? description;

  /// حالة المرحلة
  final PhaseStatus status;

  /// تاريخ البدء
  final DateTime? startDate;

  /// تاريخ الانتهاء المتوقع
  final DateTime? expectedEndDate;

  /// تاريخ الانتهاء الفعلي
  final DateTime? actualEndDate;

  /// نسبة الإنجاز (0-100)
  final double progressPercentage;

  /// الميزانية المخصصة للمرحلة
  final double? allocatedBudget;

  /// التكلفة المنفقة
  final double? spentAmount;

  /// الأقسام المتاحة في هذه المرحلة
  final List<PhaseSection> availableSections;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// Constructor
  const PhaseEntity({
    required this.id,
    required this.projectId,
    required this.phaseType,
    required this.name,
    this.description,
    this.status = PhaseStatus.notStarted,
    this.startDate,
    this.expectedEndDate,
    this.actualEndDate,
    this.progressPercentage = 0.0,
    this.allocatedBudget,
    this.spentAmount,
    this.availableSections = const [],
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة محدثة من المرحلة
  PhaseEntity copyWith({
    String? id,
    String? projectId,
    ConstructionPhase? phaseType,
    String? name,
    String? description,
    PhaseStatus? status,
    DateTime? startDate,
    DateTime? expectedEndDate,
    DateTime? actualEndDate,
    double? progressPercentage,
    double? allocatedBudget,
    double? spentAmount,
    List<PhaseSection>? availableSections,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return PhaseEntity(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      phaseType: phaseType ?? this.phaseType,
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      expectedEndDate: expectedEndDate ?? this.expectedEndDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      allocatedBudget: allocatedBudget ?? this.allocatedBudget,
      spentAmount: spentAmount ?? this.spentAmount,
      availableSections: availableSections ?? this.availableSections,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        projectId,
        phaseType,
        name,
        description,
        status,
        startDate,
        expectedEndDate,
        actualEndDate,
        progressPercentage,
        allocatedBudget,
        spentAmount,
        availableSections,
        createdAt,
        updatedAt,
        metadata,
      ];
}

/// امتدادات مساعدة لنوع القسم
extension PhaseSectionExtension on PhaseSection {
  String get nameAr {
    switch (this) {
      case PhaseSection.plans:
        return 'مخططات';
      case PhaseSection.licenses:
        return 'تراخيص';
      case PhaseSection.schedules:
        return 'مواعيد';
      case PhaseSection.documents:
        return 'مستندات';
      case PhaseSection.images:
        return 'صور';
      case PhaseSection.videos:
        return 'فيديوهات';
      case PhaseSection.reports:
        return 'تقارير';
      case PhaseSection.notes:
        return 'ملاحظات';
    }
  }

  String get nameEn {
    switch (this) {
      case PhaseSection.plans:
        return 'Plans';
      case PhaseSection.licenses:
        return 'Licenses';
      case PhaseSection.schedules:
        return 'Schedules';
      case PhaseSection.documents:
        return 'Documents';
      case PhaseSection.images:
        return 'Images';
      case PhaseSection.videos:
        return 'Videos';
      case PhaseSection.reports:
        return 'Reports';
      case PhaseSection.notes:
        return 'Notes';
    }
  }

  String get iconName {
    switch (this) {
      case PhaseSection.plans:
        return 'architecture';
      case PhaseSection.licenses:
        return 'verified_user';
      case PhaseSection.schedules:
        return 'schedule';
      case PhaseSection.documents:
        return 'description';
      case PhaseSection.images:
        return 'image';
      case PhaseSection.videos:
        return 'videocam';
      case PhaseSection.reports:
        return 'assessment';
      case PhaseSection.notes:
        return 'note';
    }
  }
}

/// امتدادات مساعدة لحالة المرحلة
extension PhaseStatusExtension on PhaseStatus {
  String get nameAr {
    switch (this) {
      case PhaseStatus.notStarted:
        return 'لم تبدأ';
      case PhaseStatus.inProgress:
        return 'قيد التنفيذ';
      case PhaseStatus.completed:
        return 'مكتملة';
      case PhaseStatus.onHold:
        return 'متوقفة';
    }
  }

  String get nameEn {
    switch (this) {
      case PhaseStatus.notStarted:
        return 'Not Started';
      case PhaseStatus.inProgress:
        return 'In Progress';
      case PhaseStatus.completed:
        return 'Completed';
      case PhaseStatus.onHold:
        return 'On Hold';
    }
  }
}
