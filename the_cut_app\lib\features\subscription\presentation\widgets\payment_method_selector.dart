import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/font_service.dart';

/// Widget to select a payment method
class PaymentMethodSelector extends StatelessWidget {
  /// List of supported payment methods
  final List<String> supportedMethods;

  /// Selected payment method
  final String selectedMethod;

  /// Callback when a payment method is selected
  final ValueChanged<String> onMethodSelected;

  /// Constructor
  const PaymentMethodSelector({
    super.key,
    required this.supportedMethods,
    required this.selectedMethod,
    required this.onMethodSelected,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final fontService = FontService.of(context);

    return Column(
      children: supportedMethods.map((method) {
        return _buildPaymentMethodTile(
          context,
          method,
          localizations,
          fontService,
        );
      }).toList(),
    );
  }

  Widget _buildPaymentMethodTile(
    BuildContext context,
    String method,
    AppLocalizations localizations,
    FontService fontService,
  ) {
    final isSelected = method == selectedMethod;

    return InkWell(
      onTap: () => onMethodSelected(method),
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2.w : 1.w,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            _buildPaymentMethodIcon(method),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getPaymentMethodName(method, localizations),
                    style: GoogleFonts.getFont(
                      fontService.fontFamily,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _getPaymentMethodDescription(method, localizations),
                    style: GoogleFonts.getFont(
                      fontService.fontFamily,
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Radio<String>(
              value: method,
              groupValue: selectedMethod,
              onChanged: (value) {
                if (value != null) {
                  onMethodSelected(value);
                }
              },
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodIcon(String method) {
    IconData iconData;
    Color iconColor;

    switch (method) {
      case 'credit_card':
        iconData = Icons.credit_card;
        iconColor = Colors.blue;
        break;
      case 'paypal':
        iconData = Icons.paypal;
        iconColor = Colors.blue.shade800;
        break;
      case 'apple_pay':
        iconData = Icons.apple;
        iconColor = Colors.black;
        break;
      case 'google_pay':
        iconData = Icons.g_mobiledata;
        iconColor = Colors.green;
        break;
      case 'bank_transfer':
        iconData = Icons.account_balance;
        iconColor = Colors.purple;
        break;
      default:
        iconData = Icons.payment;
        iconColor = AppColors.primary;
    }

    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: iconColor.withAlpha(30),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24.w,
      ),
    );
  }

  String _getPaymentMethodName(String method, AppLocalizations localizations) {
    switch (method) {
      case 'credit_card':
        return localizations.translate('credit_debit_card');
      case 'paypal':
        return 'PayPal';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'bank_transfer':
        return localizations.translate('bank_transfer');
      default:
        return method;
    }
  }

  String _getPaymentMethodDescription(String method, AppLocalizations localizations) {
    switch (method) {
      case 'credit_card':
        return localizations.translate('credit_card_description');
      case 'paypal':
        return localizations.translate('paypal_description');
      case 'apple_pay':
        return localizations.translate('apple_pay_description');
      case 'google_pay':
        return localizations.translate('google_pay_description');
      case 'bank_transfer':
        return localizations.translate('bank_transfer_description');
      default:
        return '';
    }
  }
}
