import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage fonts based on the selected language
class FontService extends ChangeNotifier {
  static const String _fontPreferenceKey = 'font_family';
  
  /// Default font family for English
  static const String defaultEnglishFont = 'Poppins';
  
  /// Default font family for Arabic
  static const String defaultArabicFont = 'Cairo';
  
  /// Current font family
  String _fontFamily = defaultEnglishFont;
  
  /// Get current font family
  String get fontFamily => _fontFamily;
  
  /// Initialize the font service
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _fontFamily = prefs.getString(_fontPreferenceKey) ?? defaultEnglishFont;
    notifyListeners();
  }
  
  /// Set font based on language code
  Future<void> setFontForLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    
    if (languageCode == 'ar') {
      _fontFamily = defaultArabicFont;
    } else {
      _fontFamily = defaultEnglishFont;
    }
    
    await prefs.setString(_fontPreferenceKey, _fontFamily);
    notifyListeners();
  }
  
  /// Get text style for headings based on current font
  TextStyle getHeadingStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    if (_fontFamily == defaultArabicFont) {
      return GoogleFonts.cairo(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color,
        height: height,
        decoration: decoration,
      );
    } else {
      return GoogleFonts.poppins(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color,
        height: height,
        decoration: decoration,
      );
    }
  }
  
  /// Get text style for body text based on current font
  TextStyle getBodyStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    if (_fontFamily == defaultArabicFont) {
      return GoogleFonts.cairo(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        decoration: decoration,
      );
    } else {
      return GoogleFonts.poppins(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.normal,
        color: color,
        height: height,
        decoration: decoration,
      );
    }
  }
  
  /// Get text style for buttons based on current font
  TextStyle getButtonStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    if (_fontFamily == defaultArabicFont) {
      return GoogleFonts.cairo(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.w600,
        color: color,
        height: height,
        decoration: decoration,
      );
    } else {
      return GoogleFonts.poppins(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.w600,
        color: color,
        height: height,
        decoration: decoration,
      );
    }
  }
  
  /// Static method to get the font service from context
  static FontService of(BuildContext context) {
    return Provider.of<FontService>(context, listen: false);
  }
}
