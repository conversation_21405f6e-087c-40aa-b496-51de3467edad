import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Get current user use case
class GetCurrentUser {
  final AuthRepository _repository;

  GetCurrentUser(this._repository);

  /// Call the use case
  Future<Either<AuthFailure, UserEntity?>> call() {
    return _repository.getCurrentUser();
  }
}
