import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/team_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../domain/entities/team_member_entity.dart';

/// Team member details screen
class TeamMemberDetailsScreen extends StatefulWidget {
  /// Team member ID
  final String teamMemberId;

  /// Constructor
  const TeamMemberDetailsScreen({
    super.key,
    required this.teamMemberId,
  });

  @override
  State<TeamMemberDetailsScreen> createState() => _TeamMemberDetailsScreenState();
}

class _TeamMemberDetailsScreenState extends State<TeamMemberDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<TeamService>(context, listen: false)
          .getTeamMemberById(widget.teamMemberId);
    });
  }

  Future<void> _deleteTeamMember(TeamMemberEntity teamMember) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Team Member'),
        content: Text(
          'Are you sure you want to delete ${teamMember.name}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final teamService = Provider.of<TeamService>(context, listen: false);
      final success = await teamService.deleteTeamMember(teamMember.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Team member deleted successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              teamService.error ?? 'Failed to delete team member',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final teamService = Provider.of<TeamService>(context);
    final teamMember = teamService.selectedTeamMember;

    return Scaffold(
      appBar: AppBar(
        title: Text(teamMember?.name ?? 'Team Member Details'),
        actions: [
          if (teamMember != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/edit-team-member',
                  arguments: teamMember.id,
                );
              },
            ),
        ],
      ),
      body: teamService.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : teamMember == null
              ? _buildTeamMemberNotFound()
              : _buildTeamMemberDetails(teamMember),
    );
  }

  Widget _buildTeamMemberNotFound() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: AppColors.error.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'Team Member Not Found',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'The team member you are looking for does not exist or has been deleted.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: 'Go Back',
              icon: Icons.arrow_back,
              onPressed: () {
                Navigator.pop(context);
              },
              width: 200.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamMemberDetails(TeamMemberEntity teamMember) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Team member header
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 50.r,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      teamMember.name.isNotEmpty
                          ? teamMember.name.substring(0, 1).toUpperCase()
                          : 'U',
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Name
                  Text(
                    teamMember.name,
                    style: AppTextStyles.h3,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  
                  // Role
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      teamMember.role,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  
                  // Status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 12.w,
                        height: 12.w,
                        decoration: BoxDecoration(
                          color: teamMember.isActive
                              ? AppColors.success
                              : AppColors.error,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        teamMember.isActive ? 'Active' : 'Inactive',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: teamMember.isActive
                              ? AppColors.success
                              : AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Team member info
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Contact Information',
                    style: AppTextStyles.h4,
                  ),
                  SizedBox(height: 16.h),
                  
                  _buildInfoItem(
                    icon: Icons.email_outlined,
                    title: 'Email',
                    value: teamMember.email,
                  ),
                  SizedBox(height: 16.h),
                  
                  if (teamMember.phoneNumber != null) ...[
                    _buildInfoItem(
                      icon: Icons.phone_outlined,
                      title: 'Phone',
                      value: teamMember.phoneNumber!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  if (teamMember.branchName != null) ...[
                    _buildInfoItem(
                      icon: Icons.store_outlined,
                      title: 'Branch',
                      value: teamMember.branchName!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Created and updated dates
                  if (teamMember.createdAt != null) ...[
                    _buildInfoItem(
                      icon: Icons.calendar_today_outlined,
                      title: 'Joined',
                      value: _formatDate(teamMember.createdAt!),
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  if (teamMember.updatedAt != null) ...[
                    _buildInfoItem(
                      icon: Icons.update_outlined,
                      title: 'Last Updated',
                      value: _formatDate(teamMember.updatedAt!),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Actions
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Edit Team Member',
                  icon: Icons.edit_outlined,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/edit-team-member',
                      arguments: teamMember.id,
                    );
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CustomButton(
                  text: 'Delete Team Member',
                  icon: Icons.delete_outline,
                  color: AppColors.error,
                  onPressed: () => _deleteTeamMember(teamMember),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // Toggle status button
          CustomButton(
            text: teamMember.isActive
                ? 'Deactivate Team Member'
                : 'Activate Team Member',
            icon: teamMember.isActive
                ? Icons.toggle_off_outlined
                : Icons.toggle_on_outlined,
            color: teamMember.isActive
                ? Colors.grey.shade700
                : AppColors.success,
            onPressed: () {
              final teamService =
                  Provider.of<TeamService>(context, listen: false);
              teamService.updateTeamMember(
                teamMember.copyWith(isActive: !teamMember.isActive),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20.w,
          color: AppColors.textSecondary,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
