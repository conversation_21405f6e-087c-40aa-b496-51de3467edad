import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../features/authentication/domain/entities/user_entity.dart';
import '../data/mock_data.dart';

/// Authentication service
class AuthService extends ChangeNotifier {
  /// Static method to get the auth service from context
  static AuthService of(BuildContext context) {
    return Provider.of<AuthService>(context, listen: false);
  }

  UserEntity? _currentUser;
  bool _isLoading = false;
  String? _error;

  /// Get current user
  UserEntity? get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Get user from mock data
      _currentUser = MockData.getUserByEmailAndPassword(email, password);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Register with email and password
  Future<bool> registerWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // In a real app, we would create a new user in Firebase
      // For mock data, we'll just set the current user
      _currentUser = UserEntity(
        id: 'user${MockData.users.length + 1}',
        email: email,
        name: name,
        isEmailVerified: false,
        roles: ['employee'],
        isActive: true,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      // Add user to mock data
      MockData.users.add(_currentUser!);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    _currentUser = null;
    _isLoading = false;
    notifyListeners();
  }

  /// Update profile
  Future<bool> updateProfile({
    String? displayName,
    String? photoURL,
    String? phoneNumber,
  }) async {
    if (_currentUser == null) {
      _error = 'No user is signed in';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Update user
      _currentUser = _currentUser!.copyWith(
        name: displayName ?? _currentUser!.name,
        photoUrl: photoURL ?? _currentUser!.photoUrl,
        phoneNumber: phoneNumber ?? _currentUser!.phoneNumber,
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // In a real app, we would send a password reset email
      // For mock data, we'll just return true
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
