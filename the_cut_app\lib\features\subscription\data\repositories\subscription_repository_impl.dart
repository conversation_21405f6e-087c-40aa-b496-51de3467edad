import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/country.dart';
import '../../domain/entities/subscriber.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/entities/transaction_record.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../datasources/subscription_local_data_source.dart';
import '../datasources/subscription_remote_data_source.dart';

/// Implementation of the subscription repository
class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionRemoteDataSource remoteDataSource;
  final SubscriptionLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  /// Constructor
  SubscriptionRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans() async {
    if (await networkInfo.isConnected) {
      try {
        final remotePlans = await remoteDataSource.getSubscriptionPlans();
        localDataSource.cacheSubscriptionPlans(remotePlans);
        return Right(remotePlans);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localPlans = await localDataSource.getLastSubscriptionPlans();
        return Right(localPlans);
      } on CacheException {
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlansByCountry(
      String countryCode) async {
    if (await networkInfo.isConnected) {
      try {
        final remotePlans = await remoteDataSource.getSubscriptionPlansByCountry(countryCode);
        localDataSource.cacheSubscriptionPlansByCountry(countryCode, remotePlans);
        return Right(remotePlans);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localPlans = await localDataSource.getLastSubscriptionPlansByCountry(countryCode);
        return Right(localPlans);
      } on CacheException {
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, SubscriptionPlan>> getSubscriptionPlanById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final remotePlan = await remoteDataSource.getSubscriptionPlanById(id);
        localDataSource.cacheSubscriptionPlan(remotePlan);
        return Right(remotePlan);
      } on ServerException {
        return Left(ServerFailure());
      } on NotFoundException {
        return Left(NotFoundFailure());
      }
    } else {
      try {
        final localPlan = await localDataSource.getSubscriptionPlanById(id);
        return Right(localPlan);
      } on CacheException {
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, Subscriber?>> getCurrentSubscriber(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteSubscriber = await remoteDataSource.getCurrentSubscriber(userId);
        if (remoteSubscriber != null) {
          localDataSource.cacheCurrentSubscriber(remoteSubscriber);
        }
        return Right(remoteSubscriber);
      } on ServerException {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localSubscriber = await localDataSource.getLastCurrentSubscriber(userId);
        return Right(localSubscriber);
      } on CacheException {
        return Left(CacheFailure());
      }
    }
  }

  // Implementation of other methods would go here
  // For brevity, they are not included in this example
  
  @override
  Future<Either<Failure, Subscriber>> subscribeToPlan({
    required String userId,
    required String subscriptionPlanId,
    required String paymentMethod,
    required String countryCode,
    required String currencyCode,
    String? cardToken,
    String? cardLast4,
    String? cardBrand,
    int? cardExpiryMonth,
    int? cardExpiryYear,
    String? receiptData,
  }) {
    // TODO: implement subscribeToPlan
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, bool>> cancelSubscription({
    required String subscriberId,
    required String reason,
  }) {
    // TODO: implement cancelSubscription
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, bool>> updatePaymentMethod({
    required String subscriberId,
    required String paymentMethod,
    String? cardToken,
    String? cardLast4,
    String? cardBrand,
    int? cardExpiryMonth,
    int? cardExpiryYear,
  }) {
    // TODO: implement updatePaymentMethod
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, bool>> toggleAutoRenew({
    required String subscriberId,
    required bool autoRenew,
  }) {
    // TODO: implement toggleAutoRenew
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, List<TransactionRecord>>> getTransactionHistory(String userId) {
    // TODO: implement getTransactionHistory
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, List<Country>>> getSupportedCountries() {
    // TODO: implement getSupportedCountries
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, Country>> getCountryByCode(String countryCode) {
    // TODO: implement getCountryByCode
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, Map<String, double>>> getExchangeRates() {
    // TODO: implement getExchangeRates
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, bool>> validateReceipt({
    required String userId,
    required String receiptData,
    required String productId,
  }) {
    // TODO: implement validateReceipt
    throw UnimplementedError();
  }
  
  @override
  Future<Either<Failure, Subscriber?>> restorePurchases({
    required String userId,
  }) {
    // TODO: implement restorePurchases
    throw UnimplementedError();
  }
}
