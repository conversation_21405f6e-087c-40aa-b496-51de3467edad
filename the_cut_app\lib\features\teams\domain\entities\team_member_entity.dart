import 'package:equatable/equatable.dart';

/// كيان عضو الفريق
class TeamMemberEntity extends Equatable {
  final String id;
  final String name;
  final String role;
  final String department;
  final String phone;
  final String email;
  final double salary;
  final DateTime joinDate;
  final bool isActive;
  final String? avatarUrl;
  final List<String> skills;
  final String? notes;

  const TeamMemberEntity({
    required this.id,
    required this.name,
    required this.role,
    required this.department,
    required this.phone,
    required this.email,
    required this.salary,
    required this.joinDate,
    required this.isActive,
    this.avatarUrl,
    this.skills = const [],
    this.notes,
  });

  /// إنشاء نسخة محدثة من العضو
  TeamMemberEntity copyWith({
    String? id,
    String? name,
    String? role,
    String? department,
    String? phone,
    String? email,
    double? salary,
    DateTime? joinDate,
    bool? isActive,
    String? avatarUrl,
    List<String>? skills,
    String? notes,
  }) {
    return TeamMemberEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      department: department ?? this.department,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      salary: salary ?? this.salary,
      joinDate: joinDate ?? this.joinDate,
      isActive: isActive ?? this.isActive,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      skills: skills ?? this.skills,
      notes: notes ?? this.notes,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'role': role,
      'department': department,
      'phone': phone,
      'email': email,
      'salary': salary,
      'joinDate': joinDate.toIso8601String(),
      'isActive': isActive,
      'avatarUrl': avatarUrl,
      'skills': skills,
      'notes': notes,
    };
  }

  /// إنشاء من Map
  factory TeamMemberEntity.fromMap(Map<String, dynamic> map) {
    return TeamMemberEntity(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      role: map['role'] ?? '',
      department: map['department'] ?? '',
      phone: map['phone'] ?? '',
      email: map['email'] ?? '',
      salary: (map['salary'] ?? 0.0).toDouble(),
      joinDate: DateTime.parse(map['joinDate'] ?? DateTime.now().toIso8601String()),
      isActive: map['isActive'] ?? true,
      avatarUrl: map['avatarUrl'],
      skills: List<String>.from(map['skills'] ?? []),
      notes: map['notes'],
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        role,
        department,
        phone,
        email,
        salary,
        joinDate,
        isActive,
        avatarUrl,
        skills,
        notes,
      ];

  @override
  String toString() {
    return 'TeamMemberEntity(id: $id, name: $name, role: $role, department: $department, isActive: $isActive)';
  }
}
