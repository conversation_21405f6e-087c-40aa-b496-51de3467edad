import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/user_entity.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// Profile page
class ProfilePage extends StatefulWidget {
  final UserEntity user;

  const ProfilePage({
    super.key,
    required this.user,
  });

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.user.name);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phoneNumber);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _toggleEditing() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  void _updateProfile() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
            UpdateProfileEvent(
              displayName: _nameController.text.trim(),
              phoneNumber: _phoneController.text.trim(),
            ),
          );
    }
  }

  void _signOut() {
    context.read<AuthBloc>().add(const SignOutEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Profile',
      ),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthErrorState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ProfileUpdatedState) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile updated successfully'),
                backgroundColor: AppColors.success,
              ),
            );
            setState(() {
              _isEditing = false;
            });
          } else if (state is UnauthenticatedState) {
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/login',
              (route) => false,
            );
          }
        },
        builder: (context, state) {
          return SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile header
                    Center(
                      child: Column(
                        children: [
                          // Profile image
                          CircleAvatar(
                            radius: 50.r,
                            backgroundColor: AppColors.primary,
                            child: widget.user.photoUrl != null
                                ? null
                                : Text(
                                    widget.user.name?.isNotEmpty == true
                                        ? widget.user.name![0].toUpperCase()
                                        : widget.user.email[0].toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 40.sp,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                          SizedBox(height: 16.h),
                          
                          // User name
                          Text(
                            widget.user.name ?? 'User',
                            style: AppTextStyles.h3,
                          ),
                          SizedBox(height: 4.h),
                          
                          // User email
                          Text(
                            widget.user.email,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          
                          // User role
                          if (widget.user.roles?.isNotEmpty == true) ...[
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(16.r),
                              ),
                              child: Text(
                                widget.user.roles!.first.toUpperCase(),
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    SizedBox(height: 32.h),
                    
                    // Edit profile button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomButton(
                          text: _isEditing ? 'Cancel' : 'Edit Profile',
                          onPressed: _toggleEditing,
                          type: ButtonType.outlined,
                          size: ButtonSize.small,
                          icon: _isEditing ? Icons.close : Icons.edit,
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    
                    // Profile form
                    Text(
                      'Personal Information',
                      style: AppTextStyles.h4,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Name field
                    CustomTextField(
                      label: 'Full Name',
                      controller: _nameController,
                      enabled: _isEditing,
                      prefixIcon: Icons.person_outline,
                      validator: (value) => Validators.validateName(value),
                    ),
                    SizedBox(height: 16.h),
                    
                    // Email field
                    CustomTextField(
                      label: 'Email',
                      controller: _emailController,
                      enabled: false,
                      prefixIcon: Icons.email_outlined,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Phone field
                    CustomTextField(
                      label: 'Phone Number',
                      controller: _phoneController,
                      enabled: _isEditing,
                      prefixIcon: Icons.phone_outlined,
                      keyboardType: TextInputType.phone,
                      validator: (value) => value?.isEmpty == true
                          ? null
                          : Validators.validatePhone(value),
                    ),
                    SizedBox(height: 32.h),
                    
                    // Account information
                    Text(
                      'Account Information',
                      style: AppTextStyles.h4,
                    ),
                    SizedBox(height: 16.h),
                    
                    // Branch
                    if (widget.user.branchId != null) ...[
                      ListTile(
                        leading: const Icon(Icons.business_outlined),
                        title: const Text('Branch'),
                        subtitle: Text(widget.user.branchId!),
                        contentPadding: EdgeInsets.zero,
                      ),
                      const Divider(),
                    ],
                    
                    // Email verification status
                    ListTile(
                      leading: Icon(
                        widget.user.isEmailVerified
                            ? Icons.verified_outlined
                            : Icons.warning_amber_outlined,
                        color: widget.user.isEmailVerified
                            ? AppColors.success
                            : AppColors.warning,
                      ),
                      title: const Text('Email Verification'),
                      subtitle: Text(
                        widget.user.isEmailVerified
                            ? 'Verified'
                            : 'Not Verified',
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                    const Divider(),
                    
                    // Account created date
                    if (widget.user.createdAt != null) ...[
                      ListTile(
                        leading: const Icon(Icons.calendar_today_outlined),
                        title: const Text('Account Created'),
                        subtitle: Text(
                          widget.user.createdAt.toString().split(' ')[0],
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                      const Divider(),
                    ],
                    
                    // Last login date
                    if (widget.user.lastLoginAt != null) ...[
                      ListTile(
                        leading: const Icon(Icons.access_time_outlined),
                        title: const Text('Last Login'),
                        subtitle: Text(
                          widget.user.lastLoginAt.toString().split(' ')[0],
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                      const Divider(),
                    ],
                    SizedBox(height: 32.h),
                    
                    // Update profile button
                    if (_isEditing) ...[
                      state is AuthLoadingState
                          ? const Center(
                              child: LoadingIndicator(),
                            )
                          : CustomButton(
                              text: 'Update Profile',
                              onPressed: _updateProfile,
                              isFullWidth: true,
                              size: ButtonSize.large,
                            ),
                      SizedBox(height: 16.h),
                    ],
                    
                    // Sign out button
                    CustomButton(
                      text: 'Sign Out',
                      onPressed: _signOut,
                      isFullWidth: true,
                      type: ButtonType.outlined,
                      size: ButtonSize.large,
                      icon: Icons.logout,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
