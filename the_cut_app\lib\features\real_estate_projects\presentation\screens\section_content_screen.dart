import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/services/real_estate_service.dart';
import '../../domain/entities/project_entity.dart';
import '../../domain/entities/phase_entity.dart';
import '../../domain/entities/project_file_entity.dart';
import '../../domain/entities/schedule_entity.dart';

/// شاشة محتوى القسم
class SectionContentScreen extends StatefulWidget {
  final PropertyType propertyType;
  final ConstructionPhase phase;
  final PhaseSection section;

  const SectionContentScreen({
    super.key,
    required this.propertyType,
    required this.phase,
    required this.section,
  });

  @override
  State<SectionContentScreen> createState() => _SectionContentScreenState();
}

class _SectionContentScreenState extends State<SectionContentScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.section == PhaseSection.schedules ? 1 : 2,
      vsync: this,
    );
    
    // Load mock data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final realEstateService = Provider.of<RealEstateService>(context, listen: false);
      realEstateService.loadProjectFiles('project1');
      realEstateService.loadProjectSchedules('project1');
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(widget.section.nameAr),
        backgroundColor: AppColors.background,
        elevation: 0,
        centerTitle: true,
        bottom: widget.section == PhaseSection.schedules 
            ? null 
            : TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: [
                  Tab(text: 'الملفات'),
                  Tab(text: 'المواعيد'),
                ],
              ),
      ),
      body: widget.section == PhaseSection.schedules
          ? _buildSchedulesContent()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildFilesContent(),
                _buildSchedulesContent(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddDialog,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildFilesContent() {
    return Consumer<RealEstateService>(
      builder: (context, service, child) {
        if (service.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final files = service.files
            .where((file) => file.section == widget.section)
            .toList();

        if (files.isEmpty) {
          return _buildEmptyState('لا توجد ملفات', 'قم بإضافة ملفات لهذا القسم');
        }

        return ListView.separated(
          padding: EdgeInsets.all(16.w),
          itemCount: files.length,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) {
            final file = files[index];
            return _buildFileCard(file);
          },
        );
      },
    );
  }

  Widget _buildSchedulesContent() {
    return Consumer<RealEstateService>(
      builder: (context, service, child) {
        if (service.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final schedules = service.schedules;

        if (schedules.isEmpty) {
          return _buildEmptyState('لا توجد مواعيد', 'قم بإضافة مواعيد لهذا القسم');
        }

        return ListView.separated(
          padding: EdgeInsets.all(16.w),
          itemCount: schedules.length,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) {
            final schedule = schedules[index];
            return _buildScheduleCard(schedule);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 80.w,
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            title,
            style: AppTextStyles.h4.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            subtitle,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFileCard(ProjectFileEntity file) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: _getFileTypeColor(file.fileType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              _getFileTypeIcon(file.fileType),
              color: _getFileTypeColor(file.fileType),
              size: 24.w,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.originalFileName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  '${file.formattedFileSize} • ${file.uploadedByName}',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                if (file.description != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    file.description!,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          IconButton(
            onPressed: () => _showFileOptions(file),
            icon: Icon(
              Icons.more_vert,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleCard(ScheduleEntity schedule) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: _getScheduleTypeColor(schedule.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getScheduleTypeIcon(schedule.type),
                  color: _getScheduleTypeColor(schedule.type),
                  size: 24.w,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      schedule.title,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      schedule.type.nameAr,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getScheduleStatusColor(schedule.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  schedule.status.nameAr,
                  style: AppTextStyles.caption.copyWith(
                    color: _getScheduleStatusColor(schedule.status),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16.w,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: 4.w),
              Text(
                '${schedule.scheduledDateTime.day}/${schedule.scheduledDateTime.month} - ${schedule.scheduledDateTime.hour}:${schedule.scheduledDateTime.minute.toString().padLeft(2, '0')}',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (schedule.location != null) ...[
                SizedBox(width: 16.w),
                Icon(
                  Icons.location_on,
                  size: 16.w,
                  color: AppColors.textSecondary,
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    schedule.location!,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  void _showAddDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إضافة جديد',
              style: AppTextStyles.h4,
            ),
            SizedBox(height: 24.h),
            if (widget.section != PhaseSection.schedules) ...[
              ListTile(
                leading: Icon(Icons.upload_file, color: AppColors.primary),
                title: Text('رفع ملف'),
                onTap: () {
                  Navigator.pop(context);
                  _showUploadFileDialog();
                },
              ),
            ],
            ListTile(
              leading: Icon(Icons.event, color: AppColors.primary),
              title: Text('إضافة موعد'),
              onTap: () {
                Navigator.pop(context);
                _showAddScheduleDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showUploadFileDialog() {
    // Implementation for file upload dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطبيق رفع الملفات قريباً')),
    );
  }

  void _showAddScheduleDialog() {
    // Implementation for add schedule dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطبيق إضافة المواعيد قريباً')),
    );
  }

  void _showFileOptions(ProjectFileEntity file) {
    // Implementation for file options
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('خيارات الملف')),
    );
  }

  Color _getFileTypeColor(FileType type) {
    switch (type) {
      case FileType.image:
        return const Color(0xFFE91E63);
      case FileType.video:
        return const Color(0xFF2196F3);
      case FileType.document:
        return const Color(0xFF9C27B0);
      case FileType.pdf:
        return const Color(0xFFD32F2F);
      case FileType.audio:
        return const Color(0xFFFF9800);
      case FileType.other:
        return const Color(0xFF607D8B);
    }
  }

  IconData _getFileTypeIcon(FileType type) {
    switch (type) {
      case FileType.image:
        return Icons.image;
      case FileType.video:
        return Icons.videocam;
      case FileType.document:
        return Icons.description;
      case FileType.pdf:
        return Icons.picture_as_pdf;
      case FileType.audio:
        return Icons.audiotrack;
      case FileType.other:
        return Icons.insert_drive_file;
    }
  }

  Color _getScheduleTypeColor(ScheduleType type) {
    switch (type) {
      case ScheduleType.meeting:
        return const Color(0xFF2196F3);
      case ScheduleType.inspection:
        return const Color(0xFF4CAF50);
      case ScheduleType.delivery:
        return const Color(0xFFFF9800);
      case ScheduleType.maintenance:
        return const Color(0xFFE91E63);
      case ScheduleType.consultation:
        return const Color(0xFF9C27B0);
      case ScheduleType.other:
        return const Color(0xFF607D8B);
    }
  }

  IconData _getScheduleTypeIcon(ScheduleType type) {
    switch (type) {
      case ScheduleType.meeting:
        return Icons.groups;
      case ScheduleType.inspection:
        return Icons.search;
      case ScheduleType.delivery:
        return Icons.local_shipping;
      case ScheduleType.maintenance:
        return Icons.build;
      case ScheduleType.consultation:
        return Icons.support_agent;
      case ScheduleType.other:
        return Icons.event;
    }
  }

  Color _getScheduleStatusColor(ScheduleStatus status) {
    switch (status) {
      case ScheduleStatus.scheduled:
        return const Color(0xFF2196F3);
      case ScheduleStatus.confirmed:
        return const Color(0xFF4CAF50);
      case ScheduleStatus.inProgress:
        return const Color(0xFFFF9800);
      case ScheduleStatus.completed:
        return const Color(0xFF4CAF50);
      case ScheduleStatus.cancelled:
        return const Color(0xFFE91E63);
      case ScheduleStatus.postponed:
        return const Color(0xFF9E9E9E);
    }
  }
}
