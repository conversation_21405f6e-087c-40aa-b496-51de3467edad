import 'package:equatable/equatable.dart';

/// نوع الموعد
enum ScheduleType {
  meeting,      // اجتماع
  inspection,   // معاينة
  delivery,     // تسليم
  maintenance,  // صيانة
  consultation, // استشارة
  other,        // أخرى
}

/// حالة الموعد
enum ScheduleStatus {
  scheduled,    // مجدول
  confirmed,    // مؤكد
  inProgress,   // جاري
  completed,    // مكتمل
  cancelled,    // ملغي
  postponed,    // مؤجل
}

/// أولوية الموعد
enum SchedulePriority {
  low,          // منخفضة
  medium,       // متوسطة
  high,         // عالية
  urgent,       // عاجل
}

/// كيان الموعد
class ScheduleEntity extends Equatable {
  /// معرف الموعد
  final String id;

  /// معرف المشروع
  final String projectId;

  /// معرف المرحلة (اختياري)
  final String? phaseId;

  /// عنوان الموعد
  final String title;

  /// وصف الموعد
  final String? description;

  /// نوع الموعد
  final ScheduleType type;

  /// حالة الموعد
  final ScheduleStatus status;

  /// أولوية الموعد
  final SchedulePriority priority;

  /// تاريخ ووقت الموعد
  final DateTime scheduledDateTime;

  /// مدة الموعد بالدقائق
  final int durationMinutes;

  /// الموقع
  final String? location;

  /// معرف المنشئ
  final String createdById;

  /// اسم المنشئ
  final String createdByName;

  /// المشاركون في الموعد
  final List<String>? participantIds;

  /// أسماء المشاركين
  final List<String>? participantNames;

  /// ملاحظات
  final String? notes;

  /// تذكير قبل الموعد (بالدقائق)
  final List<int>? reminderMinutes;

  /// هل تم إرسال التذكير
  final bool reminderSent;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  /// Constructor
  const ScheduleEntity({
    required this.id,
    required this.projectId,
    this.phaseId,
    required this.title,
    this.description,
    required this.type,
    this.status = ScheduleStatus.scheduled,
    this.priority = SchedulePriority.medium,
    required this.scheduledDateTime,
    this.durationMinutes = 60,
    this.location,
    required this.createdById,
    required this.createdByName,
    this.participantIds,
    this.participantNames,
    this.notes,
    this.reminderMinutes,
    this.reminderSent = false,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// إنشاء نسخة محدثة من الموعد
  ScheduleEntity copyWith({
    String? id,
    String? projectId,
    String? phaseId,
    String? title,
    String? description,
    ScheduleType? type,
    ScheduleStatus? status,
    SchedulePriority? priority,
    DateTime? scheduledDateTime,
    int? durationMinutes,
    String? location,
    String? createdById,
    String? createdByName,
    List<String>? participantIds,
    List<String>? participantNames,
    String? notes,
    List<int>? reminderMinutes,
    bool? reminderSent,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ScheduleEntity(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      phaseId: phaseId ?? this.phaseId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      scheduledDateTime: scheduledDateTime ?? this.scheduledDateTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      location: location ?? this.location,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      participantIds: participantIds ?? this.participantIds,
      participantNames: participantNames ?? this.participantNames,
      notes: notes ?? this.notes,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
      reminderSent: reminderSent ?? this.reminderSent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// الحصول على وقت انتهاء الموعد
  DateTime get endDateTime {
    return scheduledDateTime.add(Duration(minutes: durationMinutes));
  }

  /// التحقق من انتهاء الموعد
  bool get isExpired {
    return DateTime.now().isAfter(endDateTime);
  }

  /// التحقق من قرب الموعد (خلال ساعة)
  bool get isUpcoming {
    final now = DateTime.now();
    final oneHourLater = now.add(const Duration(hours: 1));
    return scheduledDateTime.isAfter(now) && scheduledDateTime.isBefore(oneHourLater);
  }

  @override
  List<Object?> get props => [
        id,
        projectId,
        phaseId,
        title,
        description,
        type,
        status,
        priority,
        scheduledDateTime,
        durationMinutes,
        location,
        createdById,
        createdByName,
        participantIds,
        participantNames,
        notes,
        reminderMinutes,
        reminderSent,
        createdAt,
        updatedAt,
        metadata,
      ];
}

/// امتدادات مساعدة لنوع الموعد
extension ScheduleTypeExtension on ScheduleType {
  String get nameAr {
    switch (this) {
      case ScheduleType.meeting:
        return 'اجتماع';
      case ScheduleType.inspection:
        return 'معاينة';
      case ScheduleType.delivery:
        return 'تسليم';
      case ScheduleType.maintenance:
        return 'صيانة';
      case ScheduleType.consultation:
        return 'استشارة';
      case ScheduleType.other:
        return 'أخرى';
    }
  }

  String get nameEn {
    switch (this) {
      case ScheduleType.meeting:
        return 'Meeting';
      case ScheduleType.inspection:
        return 'Inspection';
      case ScheduleType.delivery:
        return 'Delivery';
      case ScheduleType.maintenance:
        return 'Maintenance';
      case ScheduleType.consultation:
        return 'Consultation';
      case ScheduleType.other:
        return 'Other';
    }
  }

  String get iconName {
    switch (this) {
      case ScheduleType.meeting:
        return 'groups';
      case ScheduleType.inspection:
        return 'search';
      case ScheduleType.delivery:
        return 'local_shipping';
      case ScheduleType.maintenance:
        return 'build';
      case ScheduleType.consultation:
        return 'support_agent';
      case ScheduleType.other:
        return 'event';
    }
  }
}

/// امتدادات مساعدة لحالة الموعد
extension ScheduleStatusExtension on ScheduleStatus {
  String get nameAr {
    switch (this) {
      case ScheduleStatus.scheduled:
        return 'مجدول';
      case ScheduleStatus.confirmed:
        return 'مؤكد';
      case ScheduleStatus.inProgress:
        return 'جاري';
      case ScheduleStatus.completed:
        return 'مكتمل';
      case ScheduleStatus.cancelled:
        return 'ملغي';
      case ScheduleStatus.postponed:
        return 'مؤجل';
    }
  }

  String get nameEn {
    switch (this) {
      case ScheduleStatus.scheduled:
        return 'Scheduled';
      case ScheduleStatus.confirmed:
        return 'Confirmed';
      case ScheduleStatus.inProgress:
        return 'In Progress';
      case ScheduleStatus.completed:
        return 'Completed';
      case ScheduleStatus.cancelled:
        return 'Cancelled';
      case ScheduleStatus.postponed:
        return 'Postponed';
    }
  }
}

/// امتدادات مساعدة لأولوية الموعد
extension SchedulePriorityExtension on SchedulePriority {
  String get nameAr {
    switch (this) {
      case SchedulePriority.low:
        return 'منخفضة';
      case SchedulePriority.medium:
        return 'متوسطة';
      case SchedulePriority.high:
        return 'عالية';
      case SchedulePriority.urgent:
        return 'عاجل';
    }
  }

  String get nameEn {
    switch (this) {
      case SchedulePriority.low:
        return 'Low';
      case SchedulePriority.medium:
        return 'Medium';
      case SchedulePriority.high:
        return 'High';
      case SchedulePriority.urgent:
        return 'Urgent';
    }
  }
}
