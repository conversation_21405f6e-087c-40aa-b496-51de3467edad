import 'package:equatable/equatable.dart';
import '../../domain/entities/user_entity.dart';

/// Authentication states
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial authentication state
class AuthInitialState extends AuthState {
  const AuthInitialState();
}

/// Loading authentication state
class AuthLoadingState extends AuthState {
  const AuthLoadingState();
}

/// Authenticated state
class AuthenticatedState extends AuthState {
  final UserEntity user;

  const AuthenticatedState({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

/// Unauthenticated state
class UnauthenticatedState extends AuthState {
  const UnauthenticatedState();
}

/// Authentication error state
class AuthErrorState extends AuthState {
  final String message;

  const AuthErrorState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

/// Password reset email sent state
class PasswordResetEmailSentState extends AuthState {
  const PasswordResetEmailSentState();
}

/// Profile updated state
class ProfileUpdatedState extends AuthState {
  final UserEntity user;

  const ProfileUpdatedState({
    required this.user,
  });

  @override
  List<Object?> get props => [user];
}

/// Email updated state
class EmailUpdatedState extends AuthState {
  const EmailUpdatedState();
}

/// Password updated state
class PasswordUpdatedState extends AuthState {
  const PasswordUpdatedState();
}

/// Account deleted state
class AccountDeletedState extends AuthState {
  const AccountDeletedState();
}
