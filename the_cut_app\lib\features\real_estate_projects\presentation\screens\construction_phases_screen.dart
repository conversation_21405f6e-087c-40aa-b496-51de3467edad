import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../domain/entities/project_entity.dart';
import '../../domain/entities/phase_entity.dart';

/// شاشة عرض مراحل البناء
class ConstructionPhasesScreen extends StatefulWidget {
  final PropertyType propertyType;

  const ConstructionPhasesScreen({
    super.key,
    required this.propertyType,
  });

  @override
  State<ConstructionPhasesScreen> createState() => _ConstructionPhasesScreenState();
}

class _ConstructionPhasesScreenState extends State<ConstructionPhasesScreen> {
  final List<ConstructionPhase> _phases = [
    ConstructionPhase.blackWork,
    ConstructionPhase.civilWork,
    ConstructionPhase.finishing,
  ];

  void _navigateToPhaseDetails(ConstructionPhase phase) {
    Navigator.pushNamed(
      context,
      '/phase-sections',
      arguments: {
        'propertyType': widget.propertyType,
        'phase': phase,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('مراحل البناء'),
        backgroundColor: AppColors.background,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Column(
                children: [
                  Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Icon(
                      widget.propertyType == PropertyType.residential
                          ? Icons.home
                          : Icons.business,
                      size: 40.w,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'مراحل البناء',
                    style: AppTextStyles.h2.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'اختر المرحلة التي تريد إدارتها في مشروعك ${widget.propertyType.nameAr}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),

              SizedBox(height: 40.h),

              // Phases list
              Expanded(
                child: ListView.separated(
                  itemCount: _phases.length,
                  separatorBuilder: (context, index) => SizedBox(height: 16.h),
                  itemBuilder: (context, index) {
                    final phase = _phases[index];
                    return _buildPhaseCard(phase, index + 1);
                  },
                ),
              ),

              SizedBox(height: 24.h),

              // Info card
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.primary,
                      size: 24.w,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        'كل مرحلة تحتوي على أقسام مختلفة مثل المخططات والتراخيص والملفات والمواعيد',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhaseCard(ConstructionPhase phase, int phaseNumber) {
    final phaseInfo = _getPhaseInfo(phase);

    return GestureDetector(
      onTap: () => _navigateToPhaseDetails(phase),
      child: Container(
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: AppColors.border,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Phase number
                Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: phaseInfo['color'],
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      phaseNumber.toString(),
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                // Phase info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        phase.nameAr,
                        style: AppTextStyles.h4.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        phaseInfo['subtitle'],
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: 16.w,
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              phaseInfo['description'],
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 12.h),
            // Features
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: (phaseInfo['features'] as List<String>).map((feature) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: (phaseInfo['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: (phaseInfo['color'] as Color).withOpacity(0.3),
                      width: 0.5,
                    ),
                  ),
                  child: Text(
                    feature,
                    style: AppTextStyles.caption.copyWith(
                      color: phaseInfo['color'],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getPhaseInfo(ConstructionPhase phase) {
    switch (phase) {
      case ConstructionPhase.blackWork:
        return {
          'subtitle': 'الأساسات والهيكل الإنشائي',
          'description': 'تشمل أعمال الحفر والأساسات والخرسانة المسلحة والهيكل الإنشائي للمبنى',
          'features': ['الحفر', 'الأساسات', 'الخرسانة', 'الحديد'],
          'color': const Color(0xFF2E7D32), // Green
        };
      case ConstructionPhase.civilWork:
        return {
          'subtitle': 'الأعمال الكهروميكانيكية',
          'description': 'تشمل أعمال الكهرباء والسباكة والتكييف وأنظمة الأمان والاتصالات',
          'features': ['الكهرباء', 'السباكة', 'التكييف', 'الأمان'],
          'color': const Color(0xFF1976D2), // Blue
        };
      case ConstructionPhase.finishing:
        return {
          'subtitle': 'التشطيبات النهائية',
          'description': 'تشمل أعمال البلاط والدهان والأبواب والنوافذ والتشطيبات الداخلية والخارجية',
          'features': ['البلاط', 'الدهان', 'الأبواب', 'النوافذ'],
          'color': const Color(0xFFE65100), // Orange
        };
    }
  }
}
