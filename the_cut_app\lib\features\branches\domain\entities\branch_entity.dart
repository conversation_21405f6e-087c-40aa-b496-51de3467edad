import 'package:equatable/equatable.dart';

/// Branch entity
class BranchEntity extends Equatable {
  final String id;
  final String name;
  final String? address;
  final String? phoneNumber;
  final String? email;
  final String? managerId;
  final String? managerName;
  final String? logoUrl;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const BranchEntity({
    required this.id,
    required this.name,
    this.address,
    this.phoneNumber,
    this.email,
    this.managerId,
    this.managerName,
    this.logoUrl,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create a copy of this branch with the given fields replaced
  BranchEntity copyWith({
    String? id,
    String? name,
    String? address,
    String? phoneNumber,
    String? email,
    String? managerId,
    String? managerName,
    String? logoUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return BranchEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      logoUrl: logoUrl ?? this.logoUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        phoneNumber,
        email,
        managerId,
        managerName,
        logoUrl,
        isActive,
        createdAt,
        updatedAt,
        metadata,
      ];
}
