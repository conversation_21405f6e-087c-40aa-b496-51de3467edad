import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_routes.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../domain/entities/branch_entity.dart';
import '../bloc/branch_bloc.dart';
import '../bloc/branch_event.dart';
import '../bloc/branch_state.dart';
import '../widgets/branch_card.dart';
import 'add_branch_page.dart';

/// Branches page
class BranchesPage extends StatefulWidget {
  const BranchesPage({super.key});

  @override
  State<BranchesPage> createState() => _BranchesPageState();
}

class _BranchesPageState extends State<BranchesPage> {
  @override
  void initState() {
    super.initState();
    _loadBranches();
  }

  void _loadBranches() {
    context.read<BranchBloc>().add(const GetAllBranchesEvent());
  }

  void _navigateToAddBranch() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddBranchPage(),
      ),
    ).then((_) => _loadBranches());
  }

  void _navigateToBranchDetails(BranchEntity branch) {
    Navigator.pushNamed(
      context,
      AppRoutes.branchDetails,
      arguments: branch,
    ).then((_) => _loadBranches());
  }

  void _deleteBranch(String branchId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Branch'),
        content: const Text('Are you sure you want to delete this branch?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<BranchBloc>().add(
                    DeleteBranchEvent(branchId: branchId),
                  );
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Branches',
      ),
      body: BlocConsumer<BranchBloc, BranchState>(
        listener: (context, state) {
          if (state is BranchErrorState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is BranchDeletedState) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Branch deleted successfully'),
                backgroundColor: AppColors.success,
              ),
            );
            _loadBranches();
          }
        },
        builder: (context, state) {
          if (state is BranchLoadingState) {
            return const Center(
              child: LoadingIndicator(),
            );
          } else if (state is AllBranchesLoadedState) {
            return _buildBranchesList(state.branches);
          } else {
            return _buildEmptyState();
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddBranch,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBranchesList(List<BranchEntity> branches) {
    if (branches.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async => _loadBranches(),
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: branches.length,
        itemBuilder: (context, index) {
          final branch = branches[index];
          return BranchCard(
            branch: branch,
            onTap: () => _navigateToBranchDetails(branch),
            onDelete: () => _deleteBranch(branch.id),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 80.r,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Branches Found',
            style: AppTextStyles.h3,
          ),
          SizedBox(height: 8.h),
          Text(
            'Add your first branch to get started',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: _navigateToAddBranch,
            icon: const Icon(Icons.add),
            label: const Text('Add Branch'),
          ),
        ],
      ),
    );
  }
}
