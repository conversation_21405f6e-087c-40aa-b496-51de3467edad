import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../entities/branch_entity.dart';
import '../repositories/branch_repository.dart';

/// Get branch by id use case
class GetBranchById {
  final BranchRepository _repository;

  GetBranchById(this._repository);

  /// Call the use case
  Future<Either<Failure, BranchEntity>> call(Params params) {
    return _repository.getBranchById(params.branchId);
  }
}

/// Get branch by id parameters
class Params extends Equatable {
  final String branchId;

  const Params({
    required this.branchId,
  });

  @override
  List<Object?> get props => [branchId];
}
