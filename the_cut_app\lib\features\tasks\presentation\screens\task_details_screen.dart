import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/services/task_service.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../domain/entities/task_entity.dart';

/// Task details screen
class TaskDetailsScreen extends StatefulWidget {
  /// Task ID
  final String taskId;

  /// Constructor
  const TaskDetailsScreen({
    super.key,
    required this.taskId,
  });

  @override
  State<TaskDetailsScreen> createState() => _TaskDetailsScreenState();
}

class _TaskDetailsScreenState extends State<TaskDetailsScreen> {
  final _commentController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<TaskService>(context, listen: false)
          .getTaskById(widget.taskId);
    });
  }
  
  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _deleteTask(TaskEntity task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: Text(
          'Are you sure you want to delete "${task.title}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final taskService = Provider.of<TaskService>(context, listen: false);
      final success = await taskService.deleteTask(task.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task deleted successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              taskService.error ?? 'Failed to delete task',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateTaskStatus(TaskEntity task, TaskStatus newStatus) async {
    final taskService = Provider.of<TaskService>(context, listen: false);
    final updatedTask = await taskService.updateTaskStatus(task.id, newStatus);
    
    if (updatedTask != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Task marked as ${_getStatusText(newStatus)}'),
          backgroundColor: AppColors.success,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            taskService.error ?? 'Failed to update task status',
          ),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _addComment() async {
    if (_commentController.text.trim().isEmpty) {
      return;
    }
    
    final taskService = Provider.of<TaskService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;
    
    if (currentUser == null) {
      return;
    }
    
    final comment = TaskCommentEntity(
      id: '', // Will be generated by the service
      text: _commentController.text.trim(),
      createdById: currentUser.id,
      createdByName: currentUser.name ?? 'Unknown User',
      createdAt: DateTime.now(),
    );
    
    final task = taskService.selectedTask;
    if (task == null) {
      return;
    }
    
    final updatedTask = await taskService.addCommentToTask(task.id, comment);
    
    if (updatedTask != null && mounted) {
      _commentController.clear();
      FocusScope.of(context).unfocus();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            taskService.error ?? 'Failed to add comment',
          ),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'Pending';
      case TaskStatus.inProgress:
        return 'In Progress';
      case TaskStatus.completed:
        return 'Completed';
      case TaskStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return AppColors.success;
      case TaskStatus.cancelled:
        return AppColors.error;
    }
  }

  String _getPriorityText(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.high:
        return 'High';
    }
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    final taskService = Provider.of<TaskService>(context);
    final task = taskService.selectedTask;

    return Scaffold(
      appBar: AppBar(
        title: Text(task?.title ?? 'Task Details'),
        actions: [
          if (task != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/edit-task',
                  arguments: task.id,
                );
              },
            ),
        ],
      ),
      body: taskService.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : task == null
              ? _buildTaskNotFound()
              : _buildTaskDetails(task),
    );
  }

  Widget _buildTaskNotFound() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: AppColors.error.withOpacity(0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              'Task Not Found',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'The task you are looking for does not exist or has been deleted.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: 'Go Back',
              icon: Icons.arrow_back,
              onPressed: () {
                Navigator.pop(context);
              },
              width: 200.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskDetails(TaskEntity task) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Task header
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          task.title,
                          style: AppTextStyles.h3,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(task.status).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          _getStatusText(task.status),
                          style: AppTextStyles.bodySmall.copyWith(
                            color: _getStatusColor(task.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  
                  // Description
                  if (task.description != null) ...[
                    Text(
                      'Description',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      task.description!,
                      style: AppTextStyles.bodyMedium,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Priority
                  Row(
                    children: [
                      Text(
                        'Priority:',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getPriorityColor(task.priority).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          _getPriorityText(task.priority),
                          style: AppTextStyles.bodySmall.copyWith(
                            color: _getPriorityColor(task.priority),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  
                  // Due date
                  if (task.dueDate != null) ...[
                    _buildInfoItem(
                      icon: Icons.calendar_today_outlined,
                      title: 'Due Date',
                      value: _formatDate(task.dueDate!),
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Assigned to
                  if (task.assignedToName != null) ...[
                    _buildInfoItem(
                      icon: Icons.person_outline,
                      title: 'Assigned To',
                      value: task.assignedToName!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Created by
                  _buildInfoItem(
                    icon: Icons.person_outline,
                    title: 'Created By',
                    value: task.createdByName,
                  ),
                  SizedBox(height: 16.h),
                  
                  // Branch
                  if (task.branchName != null) ...[
                    _buildInfoItem(
                      icon: Icons.store_outlined,
                      title: 'Branch',
                      value: task.branchName!,
                    ),
                    SizedBox(height: 16.h),
                  ],
                  
                  // Created at
                  _buildInfoItem(
                    icon: Icons.access_time_outlined,
                    title: 'Created At',
                    value: _formatDateTime(task.createdAt),
                  ),
                  
                  // Completed at
                  if (task.completedAt != null) ...[
                    SizedBox(height: 16.h),
                    _buildInfoItem(
                      icon: Icons.check_circle_outline,
                      title: 'Completed At',
                      value: _formatDateTime(task.completedAt!),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Status actions
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Update Status',
                    style: AppTextStyles.h4,
                  ),
                  SizedBox(height: 16.h),
                  
                  // Status buttons
                  Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: [
                      // Pending button
                      if (task.status != TaskStatus.pending)
                        _buildStatusButton(
                          text: 'Mark as Pending',
                          color: Colors.orange,
                          onPressed: () => _updateTaskStatus(task, TaskStatus.pending),
                        ),
                      
                      // In progress button
                      if (task.status != TaskStatus.inProgress)
                        _buildStatusButton(
                          text: 'Mark as In Progress',
                          color: Colors.blue,
                          onPressed: () => _updateTaskStatus(task, TaskStatus.inProgress),
                        ),
                      
                      // Completed button
                      if (task.status != TaskStatus.completed)
                        _buildStatusButton(
                          text: 'Mark as Completed',
                          color: AppColors.success,
                          onPressed: () => _updateTaskStatus(task, TaskStatus.completed),
                        ),
                      
                      // Cancelled button
                      if (task.status != TaskStatus.cancelled)
                        _buildStatusButton(
                          text: 'Mark as Cancelled',
                          color: AppColors.error,
                          onPressed: () => _updateTaskStatus(task, TaskStatus.cancelled),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Comments
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Comments',
                    style: AppTextStyles.h4,
                  ),
                  SizedBox(height: 16.h),
                  
                  // Add comment
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: _commentController,
                          hintText: 'Add a comment...',
                          maxLines: 3,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      IconButton(
                        icon: const Icon(Icons.send),
                        color: AppColors.primary,
                        onPressed: _addComment,
                      ),
                    ],
                  ),
                  SizedBox(height: 16.h),
                  
                  // Comments list
                  if (task.comments == null || task.comments!.isEmpty)
                    Text(
                      'No comments yet',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    )
                  else
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: task.comments!.length,
                      separatorBuilder: (context, index) => Divider(
                        height: 24.h,
                        thickness: 1,
                        color: AppColors.border,
                      ),
                      itemBuilder: (context, index) {
                        final comment = task.comments![index];
                        return _buildCommentItem(comment);
                      },
                    ),
                ],
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // Actions
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Edit Task',
                  icon: Icons.edit_outlined,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/edit-task',
                      arguments: task.id,
                    );
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: CustomButton(
                  text: 'Delete Task',
                  icon: Icons.delete_outline,
                  color: AppColors.error,
                  onPressed: () => _deleteTask(task),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20.w,
          color: AppColors.textSecondary,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                value,
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusButton({
    required String text,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
      child: Text(text),
    );
  }

  Widget _buildCommentItem(TaskCommentEntity comment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Comment header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              comment.createdByName,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              _formatDateTime(comment.createdAt),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        
        // Comment text
        Text(
          comment.text,
          style: AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
