{"logs": [{"outputFile": "com.thecut.the_cut_app-mergeDebugResources-96:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d318f3a2bf1506a2482775d69cf4bab5\\transformed\\jetified-link-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,205,331,394,481,543,608,690,787,943,1209,1495,1573,1645,1727,1821,1941,2079,2147,2221,2386,2455,2512,2585,2666,2790,2917,3033,3119,3195,3277,3348", "endColumns": "74,74,125,62,86,61,64,81,96,155,265,285,77,71,81,93,119,137,67,73,164,68,56,72,80,123,126,115,85,75,81,70,162", "endOffsets": "125,200,326,389,476,538,603,685,782,938,1204,1490,1568,1640,1722,1816,1936,2074,2142,2216,2381,2450,2507,2580,2661,2785,2912,3028,3114,3190,3272,3343,3506"}, "to": {"startLines": "162,164,300,317,371,382,383,384,385,386,387,388,402,403,404,405,406,407,408,409,411,412,413,414,415,416,417,418,419,420,421,422,423", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14540,14699,27294,28957,34432,35956,36018,36083,36165,36262,36418,36684,38034,38112,38184,38266,38360,38480,38618,38686,38840,39005,39074,39131,39204,39285,39409,39536,39652,39738,39814,39896,39967", "endColumns": "74,74,125,62,86,61,64,81,96,155,265,285,77,71,81,93,119,137,67,73,164,68,56,72,80,123,126,115,85,75,81,70,162", "endOffsets": "14610,14769,27415,29015,34514,36013,36078,36160,36257,36413,36679,36965,38107,38179,38261,38355,38475,38613,38681,38755,39000,39069,39126,39199,39280,39404,39531,39647,39733,39809,39891,39962,40125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bef774bac08091f67bb9959ed680515\\transformed\\jetified-stripe-core-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,198,262,347,413,481,539,617,693,762,824", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "134,193,257,342,408,476,534,612,688,757,819,886"}, "to": {"startLines": "170,180,182,183,184,188,196,199,202,206,210,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15134,15983,16123,16187,16272,16575,17115,17304,17556,17898,18264,18556", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "15213,16037,16182,16267,16333,16638,17168,17377,17627,17962,18321,18618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e41d9fa2dbb9f6d9b0373aed46628dd\\transformed\\jetified-material3-1.0.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,211", "endColumns": "74,80,76", "endOffsets": "125,206,283"}, "to": {"startLines": "51,74,78", "startColumns": "4,4,4", "startOffsets": "4740,7371,7697", "endColumns": "74,80,76", "endOffsets": "4810,7447,7769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7dfbd571e75b3c5e829a11f8c3c8804\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "83,430", "startColumns": "4,4", "startOffsets": "8186,40736", "endColumns": "60,77", "endOffsets": "8242,40809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f92abf393023a99f09b879d3586a2fd7\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,13804", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,13884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\60ea357b6991ae60cfc29170380c5c9a\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "71,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7073,7870,7970,8084", "endColumns": "104,99,113,101", "endOffsets": "7173,7965,8079,8181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d8f0c43b715b185ebfed0e6fe335db8d\\transformed\\jetified-stripe-ui-core-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,221,288,374,441,502,587,651,717,785,864,924,985,1059,1123,1191,1254,1328,1394,1474,1553,1634,1727,1827,1912,1963,2011,2089,2153,2218,2289,2389,2474,2565", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "114,216,283,369,436,497,582,646,712,780,859,919,980,1054,1118,1186,1249,1323,1389,1469,1548,1629,1722,1822,1907,1958,2006,2084,2148,2213,2284,2384,2469,2560,2651"}, "to": {"startLines": "169,172,175,177,178,179,186,187,189,190,191,192,193,194,195,197,198,201,212,213,225,227,228,256,257,267,277,278,280,287,288,298,299,307,308", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15070,15324,15618,15769,15855,15922,16426,16511,16643,16709,16777,16856,16916,16977,17051,17173,17241,17482,18410,18476,19507,19665,19746,23541,23641,24648,25353,25401,25533,26245,26310,27109,27209,27920,28011", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "15129,15421,15680,15850,15917,15978,16506,16570,16704,16772,16851,16911,16972,17046,17110,17236,17299,17551,18471,18551,19581,19741,19834,23636,23721,24694,25396,25474,25592,26305,26376,27204,27289,28006,28097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4299239815b7f9a5883b5cb01612a4c3\\transformed\\material-1.11.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,4238,4334,4464,7542,7774,8247,8487,8550,8658,8718,8784,8840,8911,8971,9025,9151,9208,9270,9324,9399,9533,9618,9699,9836,9920,10006,10139,10230,10308,10364,10419,10485,10559,10637,10725,10807,10879,10956,11036,11110,11217,11310,11383,11475,11571,11645,11721,11817,11869,11951,12018,12105,12192,12254,12318,12381,12451,12557,12673,12770,12884,12944,13409", "endLines": "6,34,35,36,37,38,46,47,48,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,148", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "365,3154,3232,3317,3414,3507,4329,4459,4543,7605,7865,8310,8545,8653,8713,8779,8835,8906,8966,9020,9146,9203,9265,9319,9394,9528,9613,9694,9831,9915,10001,10134,10225,10303,10359,10414,10480,10554,10632,10720,10802,10874,10951,11031,11105,11212,11305,11378,11470,11566,11640,11716,11812,11864,11946,12013,12100,12187,12249,12313,12376,12446,12552,12668,12765,12879,12939,12998,13484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b58daadc921f951a852046faf39c73c5\\transformed\\jetified-hcaptcha-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "78", "endOffsets": "129"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "26585", "endColumns": "78", "endOffsets": "26659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b19304103023ebfaead8885b69b81460\\transformed\\jetified-paymentsheet-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,239,375,479,573,689,862,1050,1199,1287,1393,1486,1585,1773,1885,1985,2243,2343,2438,2500,2564,2648,2740,2853,3074,3141,3224,3308,3380,3467,3538,3614,3734,3823,3919,3994,4064,4124,4228,4340,4439,4562,4657,4729,4806,4890,4960,5088,5209", "endColumns": "83,99,135,103,93,115,172,187,148,87,105,92,98,187,111,99,257,99,94,61,63,83,91,112,220,66,82,83,71,86,70,75,119,88,95,74,69,59,103,111,98,122,94,71,76,83,69,127,120,89", "endOffsets": "134,234,370,474,568,684,857,1045,1194,1282,1388,1481,1580,1768,1880,1980,2238,2338,2433,2495,2559,2643,2735,2848,3069,3136,3219,3303,3375,3462,3533,3609,3729,3818,3914,3989,4059,4119,4223,4335,4434,4557,4652,4724,4801,4885,4955,5083,5204,5294"}, "to": {"startLines": "163,242,259,269,270,319,325,326,327,328,330,331,332,333,334,335,336,337,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,359,360,361,362,363,364,365,366,367,368,369,389,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14615,21816,23780,24748,24852,29083,29566,29739,29927,30076,30270,30376,30469,30568,30756,30868,30968,31226,31413,31508,31570,31634,31718,31810,31923,32144,32211,32294,32378,32450,32537,32608,32684,33239,33328,33424,33499,33569,33629,33733,33845,33944,34067,34162,36970,37541,37625,37695,37823,37944", "endColumns": "83,99,135,103,93,115,172,187,148,87,105,92,98,187,111,99,257,99,94,61,63,83,91,112,220,66,82,83,71,86,70,75,119,88,95,74,69,59,103,111,98,122,94,71,76,83,69,127,120,89", "endOffsets": "14694,21911,23911,24847,24941,29194,29734,29922,30071,30159,30371,30464,30563,30751,30863,30963,31221,31321,31503,31565,31629,31713,31805,31918,32139,32206,32289,32373,32445,32532,32603,32679,32799,33323,33419,33494,33564,33624,33728,33840,33939,34062,34157,34229,37042,37620,37690,37818,37939,38029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd005e293c1eb53732a0b5d3e7f5ddfa\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "70,77,145,149,424,428,429", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7002,7610,13183,13489,40130,40566,40653", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "7068,7692,13261,13632,40294,40648,40731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\834ec2fb3e00f66c29bead5c64ccea85\\transformed\\jetified-payments-ui-core-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,415,462,529,627,701,923,1008,1116,1185,1239,1303,1593,1666,1731,1785,1838,1915,2033,2144,2201,2283,2362,2447,2513,2619,2887,2970,3047,3110,3171,3233,3294,3400,3487,3590,3682,3758,3837,3922,4120,4318,4426,4488,5128,5190", "endColumns": "162,196,46,66,97,73,221,84,107,68,53,63,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,105,86,102,91,75,78,84,197,197,107,61,639,61,54", "endOffsets": "213,410,457,524,622,696,918,1003,1111,1180,1234,1298,1588,1661,1726,1780,1833,1910,2028,2139,2196,2278,2357,2442,2508,2614,2882,2965,3042,3105,3166,3228,3289,3395,3482,3585,3677,3753,3832,3917,4115,4313,4421,4483,5123,5185,5240"}, "to": {"startLines": "230,231,235,236,237,238,239,240,241,255,258,260,266,271,272,279,289,293,294,295,296,297,303,306,311,313,314,315,316,318,320,321,324,329,338,354,355,356,357,358,370,376,377,378,380,381,396", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19951,20114,21115,21162,21229,21327,21401,21623,21708,23472,23726,23916,24358,24946,25019,25479,26381,26664,26741,26859,26970,27027,27587,27835,28275,28423,28529,28797,28880,29020,29199,29260,29505,30164,31326,32804,32907,32999,33075,33154,34234,34819,35017,35125,35254,35894,37486", "endColumns": "162,196,46,66,97,73,221,84,107,68,53,63,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,105,86,102,91,75,78,84,197,197,107,61,639,61,54", "endOffsets": "20109,20306,21157,21224,21322,21396,21618,21703,21811,23536,23775,23975,24643,25014,25079,25528,26429,26736,26854,26965,27022,27104,27661,27915,28336,28524,28792,28875,28952,29078,29255,29317,29561,30265,31408,32902,32994,33070,33149,33234,34427,35012,35120,35182,35889,35951,37536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e48aba25dd75b0c781cfa94324700f3b\\transformed\\jetified-payments-core-20.44.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,266,336,415,473,532,586,661,745,828,934,1040,1126,1210,1291,1379,1479,1577,1650,1745,1836,1928,2042,2126,2215,2295,2397,2472,2566,2661,2752,2838,2937,3010,3089,3201,3271,3334,4005,4655,4729,4835,4935,4990,5080,5164,5232,5326,5420,5478,5561,5610,5688,5798,5867,5939,5988,6068,6164,6211,6257,6329,6387,6447,6622,6781,6905,6974,7056,7136,7223,7304,7392,7470,7565,7647,7734,7830,7886,8022,8071,8130,8197,8263,8336,8405,8476,8564,8636", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,48,79,95,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,86,95,55,135,48,58,66,65,72,68,70,87,71,79", "endOffsets": "120,199,261,331,410,468,527,581,656,740,823,929,1035,1121,1205,1286,1374,1474,1572,1645,1740,1831,1923,2037,2121,2210,2290,2392,2467,2561,2656,2747,2833,2932,3005,3084,3196,3266,3329,4000,4650,4724,4830,4930,4985,5075,5159,5227,5321,5415,5473,5556,5605,5683,5793,5862,5934,5983,6063,6159,6206,6252,6324,6382,6442,6617,6776,6900,6969,7051,7131,7218,7299,7387,7465,7560,7642,7729,7825,7881,8017,8066,8125,8192,8258,8331,8400,8471,8559,8631,8711"}, "to": {"startLines": "155,156,157,158,159,160,161,165,166,167,168,171,173,174,176,181,185,200,203,204,205,207,208,209,211,215,216,217,218,219,220,221,222,223,224,226,229,232,233,234,243,244,245,246,247,248,249,250,251,252,253,254,261,262,263,264,265,268,273,274,275,276,281,282,283,284,285,286,290,291,301,302,304,305,309,310,312,322,323,372,373,374,375,379,390,391,392,393,394,395,410", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14063,14133,14212,14274,14344,14423,14481,14774,14828,14903,14987,15218,15426,15532,15685,16042,16338,17382,17632,17730,17803,17967,18058,18150,18326,18623,18712,18792,18894,18969,19063,19158,19249,19335,19434,19586,19839,20311,20381,20444,21916,22566,22640,22746,22846,22901,22991,23075,23143,23237,23331,23389,23980,24029,24107,24217,24286,24699,25084,25164,25260,25307,25597,25669,25727,25787,25962,26121,26434,26503,27420,27500,27666,27747,28102,28180,28341,29322,29409,34519,34575,34711,34760,35187,37047,37113,37186,37255,37326,37414,38760", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,48,79,95,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,86,95,55,135,48,58,66,65,72,68,70,87,71,79", "endOffsets": "14128,14207,14269,14339,14418,14476,14535,14823,14898,14982,15065,15319,15527,15613,15764,16118,16421,17477,17725,17798,17893,18053,18145,18259,18405,18707,18787,18889,18964,19058,19153,19244,19330,19429,19502,19660,19946,20376,20439,21110,22561,22635,22741,22841,22896,22986,23070,23138,23232,23326,23384,23467,24024,24102,24212,24281,24353,24743,25159,25255,25302,25348,25664,25722,25782,25957,26116,26240,26498,26580,27495,27582,27742,27830,28175,28270,28418,29404,29500,34570,34706,34755,34814,35249,37108,37181,37250,37321,37409,37481,38835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40dfa3abad0911287578f4f4eae4d17b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4815,4921,5078,5208,5318,5475,5605,5720,5959,6109,6216,6373,6501,6648,6791,6859,6921", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "4916,5073,5203,5313,5470,5600,5715,5822,6104,6211,6368,6496,6643,6786,6854,6916,6997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4daf50f7558cf94dc7d393e450025472\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3512,3610,3717,3814,3913,4017,4121,13962", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3605,3712,3809,3908,4012,4116,4233,14058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68894b9791d5587db8480cd28ae65169\\transformed\\jetified-ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "49,50,72,73,75,85,86,143,144,146,147,150,151,153,425,426,427", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4653,7178,7272,7452,8315,8394,13003,13098,13266,13338,13637,13718,13889,40299,40378,40448", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4648,4735,7267,7366,7537,8389,8482,13093,13178,13333,13404,13713,13799,13957,40373,40443,40561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c15bdd5c1085abfb408a9873a2860ab8\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5827", "endColumns": "131", "endOffsets": "5954"}}]}]}