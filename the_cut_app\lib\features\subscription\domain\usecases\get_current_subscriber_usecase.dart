import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/subscriber.dart';
import '../repositories/subscription_repository.dart';

/// Use case to get the current subscriber information for a user
class GetCurrentSubscriberUseCase implements UseCase<Subscriber?, UserParams> {
  final SubscriptionRepository repository;

  /// Constructor
  GetCurrentSubscriberUseCase(this.repository);

  @override
  Future<Either<Failure, Subscriber?>> call(UserParams params) {
    return repository.getCurrentSubscriber(params.userId);
  }
}

/// Parameters for GetCurrentSubscriberUseCase
class UserParams extends Equatable {
  /// User ID
  final String userId;

  /// Constructor
  const UserParams({required this.userId});

  @override
  List<Object?> get props => [userId];
}
